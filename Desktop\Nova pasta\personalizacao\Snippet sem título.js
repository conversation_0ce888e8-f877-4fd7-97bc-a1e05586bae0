// Script para colocar o título e o preço na mesma linha e remover o preço duplicado
(function() {
    // Função para executar quando o DOM estiver carregado
    function ajustarOrderBump() {
        // Selecionar os elementos
        var ofertaUnica = document.querySelector('.wcf-bump-order-offer');
        var precoElement = document.querySelector('.wcf-normal-price');
        
        // Verificar se os elementos existem
        if (!ofertaUnica || !precoElement) {
            console.log("Elementos não encontrados, tentando novamente em 500ms");
            setTimeout(ajustarOrderBump, 500);
            return;
        }
        
        // Criar um novo container para ambos os elementos
        var novoContainer = document.createElement('div');
        novoContainer.className = 'wcf-bump-order-header';
        novoContainer.style.display = 'flex';
        novoContainer.style.justifyContent = 'center';
        novoContainer.style.alignItems = 'center';
        novoContainer.style.marginBottom = '10px';
        novoContainer.style.width = '100%';
        
        // Pegar o título
        var titulo = ofertaUnica.querySelector('.wcf-bump-order-bump-highlight');
        
        // Criar um clone do título para o novo container
        var tituloClone = titulo.cloneNode(true);
        tituloClone.style.marginRight = '15px';
        
        // Criar um clone do preço para o novo container
        var precoClone = precoElement.cloneNode(true);
        
        // Adicionar os elementos ao novo container
        novoContainer.appendChild(tituloClone);
        novoContainer.appendChild(precoClone);
        
        // Encontrar o elemento pai onde inserir o novo container
        var parentElement = ofertaUnica.parentNode;
        
        // Inserir o novo container antes do elemento original
        parentElement.insertBefore(novoContainer, ofertaUnica);
        
        // Esconder os elementos originais
        ofertaUnica.style.display = 'none';
        
        // Encontrar e esconder o elemento <br> se existir
        var descElement = document.querySelector('.wcf-bump-order-desc p');
        if (descElement) {
            var br = descElement.querySelector('br');
            if (br) br.style.display = 'none';
            
            // Esconder o preço original na descrição
            var precoOriginal = descElement.querySelector('.wcf-normal-price');
            if (precoOriginal) {
                // Remover completamente o preço original
                precoOriginal.parentNode.removeChild(precoOriginal);
            }
        }
        
        // Procurar e remover qualquer outro preço duplicado na página
        var todosPrecos = document.querySelectorAll('.wcf-normal-price');
        if (todosPrecos.length > 1) {
            for (var i = 1; i < todosPrecos.length; i++) {
                todosPrecos[i].parentNode.removeChild(todosPrecos[i]);
            }
        }
        
        console.log("Ajuste do order bump concluído com sucesso");
    }
    
    // Executar a função quando o DOM estiver carregado
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', ajustarOrderBump);
    } else {
        ajustarOrderBump();
    }
    
    // Executar novamente após um atraso para garantir que todos os elementos foram carregados
    setTimeout(ajustarOrderBump, 1000);
})();






