/**
 * Função para remover os asteriscos (*) dos campos obrigatórios.
 */
function removerAsteriscosObrigatorios() {
    // Seleciona todos os spans com a classe 'required' dentro da área principal do formulário
    // Usamos #customer_details como um container comum, pode ajustar se necessário
    const asteriscos = document.querySelectorAll('#customer_details span.required');
    let contador = 0;

    asteriscos.forEach(span => {
        // Verificação extra: remove apenas se o conteúdo for exatamente '*' (ignorando espaços)
        // E se tiver o atributo aria-hidden="true" (mais específico do WooCommerce)
        if (span.textContent.trim() === '*' && span.getAttribute('aria-hidden') === 'true') {
            span.remove(); // Remove o elemento <span> inteiro
            contador++;
        }
    });

    if (contador > 0) {
        console.log(`Removidos ${contador} asteriscos de campos obrigatórios.`);
    }
}

/**
 * Inicializa a remoção dos asteriscos e observa mudanças futuras.
 */
function inicializarRemocaoAsteriscos() {
    console.log('Inicializando remoção de asteriscos...');

    // 1. Remove na carga inicial
    removerAsteriscosObrigatorios();

    // 2. Remove em atualizações AJAX do WooCommerce (comum)
    if (window.jQuery) {
        jQuery(document.body).on('updated_checkout init_checkout', function() {
            console.log('Evento de atualização do checkout detectado, removendo asteriscos novamente...');
            // Pequeno delay para garantir que o DOM foi atualizado pelo WooCommerce
            setTimeout(removerAsteriscosObrigatorios, 100);
        });
    } else {
        console.warn('jQuery não encontrado, não é possível observar eventos do WooCommerce de forma ideal.');
    }

    // 3. Usa MutationObserver para robustez máxima contra scripts que adicionam campos depois
    const observerAlvo = document.getElementById('customer_details'); // Observa a área principal
    if (observerAlvo) {
        const observer = new MutationObserver(function(mutationsList) {
            // Otimização simples: Se qualquer nó foi adicionado na subárvore,
            // apenas re-executa a função de remoção.
            for (let mutation of mutationsList) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Verifica se algum dos nós adicionados (ou seus descendentes) contêm um span.required
                    let precisaVerificar = false;
                    mutation.addedNodes.forEach(node => {
                        // Verifica se o próprio nó é um elemento e contém a classe ou se tem descendentes com a classe
                        if (node.nodeType === Node.ELEMENT_NODE) {
                           if (node.querySelector('span.required')) {
                               precisaVerificar = true;
                           }
                        }
                    });

                    if (precisaVerificar) {
                        console.log('Mutação no DOM detectada (nós adicionados), verificando asteriscos...');
                        // Espera um instante mínimo caso múltiplos scripts estejam agindo
                        setTimeout(removerAsteriscosObrigatorios, 50);
                        // Não precisa continuar checando outras mutações neste ciclo
                        return;
                    }

                }
            }
        });

        observer.observe(observerAlvo, {
            childList: true, // Observa adição/remoção de filhos diretos
            subtree: true    // Observa também toda a subárvore (importante!)
        });
        console.log('MutationObserver configurado para remoção de asteriscos.');

    } else {
        console.warn('Não foi possível encontrar #customer_details para configurar o MutationObserver.');
    }
}

// --- Execução ---
// Garante que o DOM esteja pronto antes de rodar
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', inicializarRemocaoAsteriscos);
} else {
    // DOM já está pronto
    inicializarRemocaoAsteriscos();
}