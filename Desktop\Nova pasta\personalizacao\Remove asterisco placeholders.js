document.addEventListener('DOMContentLoaded', function() {
    // Função para remover asteriscos dos placeholders
    function removePlaceholderAsterisks() {
        const inputs = document.querySelectorAll('input[placeholder]');
        inputs.forEach(input => {
            let newPlaceholder = input.placeholder.replace(/\s*\*\s*/g, '');
            input.placeholder = newPlaceholder;
            
            // Observa mudanças no atributo placeholder
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.target.placeholder.includes('*')) {
                        mutation.target.placeholder = mutation.target.placeholder.replace(/\s*\*\s*/g, '');
                    }
                });
            });

            observer.observe(input, {
                attributes: true,
                attributeFilter: ['placeholder']
            });
        });
    }

    // Executa inicialmente
    removePlaceholderAsterisks();

    // Observa mudanças no DOM para novos inputs
    const domObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                removePlaceholderAsterisks();
            }
        });
    });

    domObserver.observe(document.body, {
        childList: true,
        subtree: true
    });
});