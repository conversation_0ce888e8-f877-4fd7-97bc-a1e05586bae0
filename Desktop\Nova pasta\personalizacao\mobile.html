<script>
document.addEventListener('DOMContentLoaded', function() {

    // --- Start of IIFE ---
    (function() {

        // --- Configuration ---
        const mobileBreakpoint = 768;
        const initDelay = 300;
        const observerDebounceDelay = 200;
        const displayDelay = 10; // Tiny delay (ms) before showing content

        // --- State Variables ---
        let accordionActive = false;
        let originalTableElement = null;
        let currentAccordionWrapper = null;
        let observer = null;
        let debouncedUpdateTotal = null;
        let isObserving = false; // Track observer state explicitly

        // --- Utility Functions ---
        function isMobile() { /* ... */ }
        function debounce(func, wait) { /* ... */ }
        function updateAccordionTotal() { /* ... */ }

        // --- Utility Functions (Keep as before) ---
        function isMobile() {
            return typeof window !== 'undefined' && window.innerWidth <= mobileBreakpoint;
        }
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => { clearTimeout(timeout); func.apply(this, args); };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        function updateAccordionTotal() {
             if (!accordionActive || !currentAccordionWrapper) return;
            const totalElement = document.querySelector('.order-total .woocommerce-Price-amount');
            const headerTotal = currentAccordionWrapper.querySelector('.accordion-header .total-amount');
            if (totalElement && headerTotal) {
                 const newTotalText = totalElement.textContent.trim();
                 if (headerTotal.textContent !== newTotalText) { headerTotal.textContent = newTotalText; }
            }
        }
        // --- End Utility Functions ---


        // --- Core Logic: Initialize Accordion ---
        function initializeAccordion() {
            if (!isMobile() || accordionActive) return;
            const table = document.querySelector('.shop_table:not(.order_details)');
            if (!table || !table.parentNode || table.closest('.accordion-wrapper')) return;

            originalTableElement = table;
            const originalParent = table.parentNode;

            // Create Structure (Header, Content, Wrapper)
            const accordionWrapper = document.createElement('div');
            accordionWrapper.className = 'accordion-wrapper mobile-only';
            currentAccordionWrapper = accordionWrapper;

            const totalElement = document.querySelector('.order-total .woocommerce-Price-amount');
            const totalAmount = totalElement ? totalElement.textContent.trim() : 'R$ 0,00';
            const accordionHeader = document.createElement('div');
            accordionHeader.className = 'accordion-header';
            accordionHeader.innerHTML = `
                <div class="header-content">
                    <div class="header-left">
                       <svg class="cart-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 8px; flex-shrink: 0;"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg><span>Detalhes da compra</span>
                    </div>
                    <div class="header-right">
                        <span class="total-amount">${totalAmount}</span>
                        <svg class="chevron-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-left: 8px; transition: transform 0.3s ease; flex-shrink: 0;"><polyline points="6 9 12 15 18 9"></polyline></svg>
                    </div>
                </div>`; // Keep innerHTML concise

            const accordionContent = document.createElement('div');
            accordionContent.className = 'accordion-content';
            accordionContent.style.display = 'none';

            // Assemble and Replace
            accordionWrapper.appendChild(accordionHeader);
            accordionWrapper.appendChild(accordionContent);
            try {
                originalParent.replaceChild(accordionWrapper, originalTableElement);
            } catch (error) { console.error('Error replacing table:', error); originalTableElement = null; currentAccordionWrapper = null; return; }
            accordionContent.appendChild(originalTableElement);

            // Mark Active & Add Listeners
            accordionActive = true;
            accordionContent.addEventListener('click', function(e) { e.stopPropagation(); });

            // --- MODIFIED CLICK HANDLER ---
            accordionHeader.addEventListener('click', function(e) {
                e.preventDefault(); e.stopPropagation();
                if (!currentAccordionWrapper) return;

                const chevron = accordionHeader.querySelector('.chevron-icon');
                const isActive = currentAccordionWrapper.classList.contains('active'); // Check current state BEFORE toggling

                if (!isActive) {
                    // --- OPENING ACCORDION ---
                    currentAccordionWrapper.classList.add('active');
                    if (chevron) { chevron.style.transform = 'rotate(180deg)'; }

                    // 1. Disconnect observer BEFORE showing content
                    stopObservingTotal();

                    // 2. Show content after a tiny delay
                    setTimeout(() => {
                        accordionContent.style.display = 'block';
                        // 3. Reconnect observer AFTER content is potentially rendered
                        //    Add another small delay if needed
                        setTimeout(startObservingTotal, 50); // Give time for display block to apply
                    }, displayDelay); // Tiny delay before display:block

                } else {
                    // --- CLOSING ACCORDION ---
                    currentAccordionWrapper.classList.remove('active');
                    if (chevron) { chevron.style.transform = 'rotate(0deg)'; }
                    accordionContent.style.display = 'none';
                    // Optional: Stop observer when closed if updates aren't needed then
                    // stopObservingTotal();
                }
            });

            // Create debounced function instance
            debouncedUpdateTotal = debounce(updateAccordionTotal, observerDebounceDelay);

            // Start observing initially (if needed, or wait until first open)
            startObservingTotal();
        }

        // --- Core Logic: Remove Accordion ---
        // (Keep removeAccordion as before - Ensure stopObservingTotal is called)
        function removeAccordion() {
            if (!accordionActive || !currentAccordionWrapper || !originalTableElement) return;
            const accordionContent = currentAccordionWrapper.querySelector('.accordion-content');
            if (!accordionContent || originalTableElement.parentNode !== accordionContent) {
                accordionActive = false; currentAccordionWrapper = null; originalTableElement = null; stopObservingTotal(); return;
            }
            const parentOfWrapper = currentAccordionWrapper.parentNode;
            if (parentOfWrapper) {
                try {
                    stopObservingTotal(); // Stop observer before removing
                    parentOfWrapper.replaceChild(originalTableElement, currentAccordionWrapper);
                    accordionActive = false; currentAccordionWrapper = null; originalTableElement = null;
                } catch (error) {
                    console.error('Error restoring table:', error);
                    accordionActive = false; currentAccordionWrapper = null; originalTableElement = null; stopObservingTotal();
                }
            } else {
                accordionActive = false; currentAccordionWrapper = null; originalTableElement = null; stopObservingTotal();
            }
        }

        // --- Mutation Observer Setup ---
        function startObservingTotal() {
            if (!currentAccordionWrapper || isObserving) return; // Don't start if already observing

            // Ensure previous one is stopped just in case
            stopObservingTotal(true); // Pass true to skip cancellation if debounce not ready

            const targetNode = currentAccordionWrapper.closest('.woocommerce-checkout-review-order-table') || currentAccordionWrapper.closest('form.checkout') || document.body;
            if (!targetNode) { console.warn("Observer: Could not find target node."); return; }

            if (!debouncedUpdateTotal) { // Ensure debounced function exists
                debouncedUpdateTotal = debounce(updateAccordionTotal, observerDebounceDelay);
            }

            observer = new MutationObserver(function(mutations) {
                debouncedUpdateTotal(); // Call the lightweight debounced function
            });

            const config = { childList: true, subtree: true, characterData: true };
            try {
                observer.observe(targetNode, config);
                isObserving = true; // Mark as observing
                // console.log('Observer started on:', targetNode); // DEBUG
            } catch (error) {
                console.error("Error starting Observer:", error);
                observer = null; isObserving = false;
            }
        }

        function stopObservingTotal(skipDebounceCancel = false) {
            if (observer) {
                try { observer.disconnect(); } catch (e) {}
                observer = null;
            }
            isObserving = false; // Mark as not observing

            // Cancel pending debounced calls unless skipped
            if (!skipDebounceCancel && debouncedUpdateTotal && typeof debouncedUpdateTotal.cancel === 'function') {
                 // Assuming your debounce implementation has a .cancel() method
                 // debouncedUpdateTotal.cancel();
            }
            // console.log("Observer stopped."); // DEBUG
        }


        // --- Event Handlers & Initial Setup ---
        const handleResize = debounce(function() { /* ... */ }, 250);
        // (Keep Initialization logic as before: CartFlows removal, setTimeout for init, resize listener)
        if (isMobile()) {
            const cf = document.querySelector('.wcf-collapsed-order-review-section'); if(cf){try{cf.remove();}catch(e){}}
            setTimeout(() => { if (isMobile() && !accordionActive) { initializeAccordion(); } }, initDelay);
        }
        if(typeof window !== 'undefined') { window.addEventListener('resize', handleResize); }


    // --- End of IIFE ---
    })();

});
</script>

<!-- Include the previous CSS block here -->
<style>
/* Paste the complete CSS from the previous working version here */
@media (max-width: 768px) { .accordion-wrapper.mobile-only { display: block; margin-bottom: 1em; border-radius: 4px; } }
@media (max-width: 768px) { .accordion-header {   cursor: pointer;  transparent; transition: border-color 0.3s ease; } .accordion-wrapper.active .accordion-header { border-bottom-color: #e0e0e0; } .accordion-header .header-content { display: flex; justify-content: space-between; align-items: center; width: 100%; } }
.accordion-header .header-left, .accordion-header .header-right { display: flex; align-items: center; }
.accordion-header .total-amount { font-weight: bold; margin-right: 8px; }
.accordion-content {  
.accordion-wrapper:not(.active) .accordion-content { border-top: none; padding: 0 15px; }
.accordion-header .chevron-icon { transition: transform 0.3s ease; }
.accordion-wrapper.active .accordion-header .chevron-icon { transform: rotate(180deg); }
@media (min-width: 769px) { .accordion-wrapper.mobile-only { display: none !important; } .shop_table:not(.order_details) { display: table !important; } }

@media only screen and (max-width: 768px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table {
     
        padding: 15px 0 0 0 !important;
      border-radius: 8px !important;
    }
}

@media only screen and (max-width: 768px) {
.header-right {
    display: flex !important;

    gap: 0px !important;

}
}

</style>



<script>
document.addEventListener('DOMContentLoaded', function() {
    // Encontra o botão
    const button = document.querySelector('#place_order');
    
    if (button) {
        // Pega o texto atual e o preço
        const currentText = button.textContent;
        const priceMatch = currentText.match(/R\$\s*[\d,\.]+/);
        const price = priceMatch ? priceMatch[0] : '';

        // Define o novo HTML do botão
        button.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13 7H3C2.44772 7 2 7.44772 2 8V14C2 14.5523 2.44772 15 3 15H13C13.5523 15 14 14.5523 14 14V8C14 7.44772 13.5523 7 13 7Z" stroke="currentColor" stroke-width="1.5"/>
                    <path d="M11.5 7V5C11.5 3.067 9.933 1.5 8 1.5C6.067 1.5 4.5 3.067 4.5 5V7" stroke="currentColor" stroke-width="1.5"/>
                </svg>
                Finalizar compra ${price}
            </div>
        `;

        // Adiciona estilos diretamente ao botão
        button.style.cssText += `
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
            font-size: 15px !important;
            gap: 8px !important;
            padding: 16px 24px !important;
            width: 100% !important;
            border-radius: 8px !important;
        `;
    }
});
</script>

