

<!DOCTYPE html>
<html lang="pt-BR" class="no-js">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="http://gmpg.org/xfn/11">
	<title>Checkout &#8211; Checkout</title>
<!-- script to print the admin localized variables --><script type="text/javascript">var cartflows_checkout_optimized_fields = {"billing_email_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Preencha seu E-mail"},"billing_first_name_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Preencha seu nome"},"billing_cellphone_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Celular"},"billing_cpf_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> CPF"},"billing_cpf_cnpj_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> CPF ou CNPJ"},"billing_persontype_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Tipo de Pessoa"},"billing_last_name_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Sobrenome"},"billing_company_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Nome da empresa"},"billing_country_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Pa\u00eds"},"billing_address_1_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Endere\u00e7o"},"billing_address_2_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Apartamento, su\u00edte, unidade, etc."},"billing_city_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Cidade"},"billing_state_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Estado"},"billing_postcode_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> CEP"},"billing_phone_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Telefone"},"billing_cnpj_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> CNPJ"},"shipping_first_name_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Nome"},"shipping_last_name_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Sobrenome"},"shipping_company_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Nome da empresa"},"shipping_country_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Pa\u00eds"},"shipping_address_1_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Endere\u00e7o"},"shipping_address_2_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Apartamento, su\u00edte, unidade, etc."},"shipping_city_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Cidade"},"shipping_state_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> Estado"},"shipping_postcode_field":{"is_optimized":false,"field_label":"Adicionar <div class=\"dashicons dashicons-arrow-right\"><\/div> CEP"},"wcf_custom_coupon_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Tem um cupom?"},"order_comments_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Adicionar notas ao pedido"}};</script><!-- script to print the admin localized variables --><script type="text/javascript">var cartflows_animate_tab_fields = {"enabled":"no","title":"___N\u00e3o perca a oferta___"};</script><!-- script to print the admin localized variables --><script type="text/javascript">var cartflows = {"ajax_url":"\/wp-admin\/admin-ajax.php?wcf_checkout_id=90","is_pb_preview":false,"current_theme":"Astra","current_flow":"89","current_step":90,"control_step":90,"next_step":"","page_template":"cartflows-canvas","default_page_builder":"elementor","is_checkout_page":true,"fb_setting":{"facebook_pixel_id":"","facebook_pixel_view_content":"enable","facebook_pixel_add_to_cart":"enable","facebook_pixel_initiate_checkout":"enable","facebook_pixel_add_payment_info":"enable","facebook_pixel_purchase_complete":"enable","facebook_pixel_optin_lead":"enable","facebook_pixel_tracking":"disable","facebook_pixel_tracking_for_site":"disable"},"ga_setting":{"enable_google_analytics":"disable","enable_google_analytics_for_site":"disable","google_analytics_id":"","enable_begin_checkout":"enable","enable_add_to_cart":"enable","enable_optin_lead":"enable","enable_add_payment_info":"enable","enable_purchase_event":"enable","enable_bump_order_add_to_cart":"disable"},"tik_setting":{"tiktok_pixel_id":"","enable_tiktok_begin_checkout":"disable","enable_tiktok_add_to_cart":"disable","enable_tiktok_view_content":"disable","enable_tiktok_add_payment_info":"disable","enable_tiktok_purchase_event":"disable","enable_tiktok_optin_lead":"disable","tiktok_pixel_tracking":"disable","tiktok_pixel_tracking_for_site":"disable"},"pin_settings":{"pinterest_tag_id":"","enable_pinterest_consent":"disable","enable_pinterest_begin_checkout":"disable","enable_pinterest_add_to_cart":"disable","enable_pinterest_add_payment_info":"disable","enable_pinterest_purchase_event":"disable","enable_pinterest_signup":"disable","enable_pinterest_optin_lead":"disable","pinterest_tag_tracking":"disable","pinterest_tag_tracking_for_site":"disable"},"gads_setting":{"google_ads_id":"","google_ads_label":"","enable_google_ads_begin_checkout":"disable","enable_google_ads_add_to_cart":"disable","enable_google_ads_view_content":"disable","enable_google_ads_add_payment_info":"disable","enable_google_ads_purchase_event":"disable","enable_google_ads_optin_lead":"disable","google_ads_tracking":"disable","google_ads_for_site":"disable"},"snap_settings":{"snapchat_pixel_id":"","enable_snapchat_begin_checkout":"disable","enable_snapchat_add_to_cart":"disable","enable_snapchat_view_content":"disable","enable_snapchat_purchase_event":"disable","enable_snapchat_optin_lead":"disable","enable_snapchat_subscribe_event":"disable","snapchat_pixel_tracking":"disable","snapchat_pixel_for_site":"disable"},"active_checkout_cookie":"wcf_active_checkout","is_optin":false,"pinterest_consent_cookie":"cartflows_pinterest_consent","fb_add_payment_info_data":"{\"content_ids\":[\"105\"],\"content_type\":\"product\",\"plugin\":\"CartFlows-Checkout\",\"value\":\"50.00\",\"content_name\":\"Teste\",\"content_category\":\"Sem categoria\",\"contents\":\"[{\\\"id\\\":105,\\\"name\\\":\\\"Teste\\\",\\\"price\\\":\\\"50.00\\\",\\\"quantity\\\":\\\"1\\\"}]\",\"currency\":\"BRL\",\"user_roles\":\"\",\"num_items\":1,\"domain\":\"https:\\\/\\\/pay.membero.pro\",\"language\":\"pt-BR\",\"userAgent\":\"Mozilla\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\/537.36 (KHTML, like Gecko) Chrome\\\/138.0.0.0 Safari\\\/537.36\"}","add_payment_info_data":"{\"send_to\":\"\",\"event_category\":\"Enhanced-Ecommerce\",\"currency\":\"BRL\",\"coupon\":[],\"value\":\"50.00\",\"items\":[{\"id\":105,\"name\":\"Teste\",\"sku\":\"\",\"category\":\"Sem categoria\",\"price\":\"50.00\",\"quantity\":\"1\"}],\"non_interaction\":true}","wcf_validate_coupon_nonce":"7171e7c78d","wcf_validate_remove_coupon_nonce":"b6feb6c157","wcf_validate_remove_cart_product_nonce":"b3fd4dd8d4","check_email_exist_nonce":"f254f40345","woocommerce_login_nonce":"af53d4e4a0","allow_persistence":"yes","is_logged_in":false,"email_validation_msgs":{"error_msg":"O e-mail informado n\u00e3o \u00e9 um e-mail v\u00e1lido.","success_msg":"Este e-mail j\u00e1 est\u00e1 registrado. Digite a senha para continuar."},"field_validation_msgs":{"number_field":"O valor deve estar entre"},"order_review_toggle_texts":{"toggle_show_text":"Mostrar resumo do pedido","toggle_hide_text":"Ocultar Resumo do Pedido"},"field_validation":{"is_enabled":"yes","error_msg":"\u00e9 necess\u00e1rio"},"wcf_bump_order_process_nonce":"6e2c570d0a","wcf_update_order_bump_qty_nonce":"0b4d454ad9","wcf_multiple_selection_nonce":"a6c61f92b3","wcf_single_selection_nonce":"04fdde291c","wcf_quantity_update_nonce":"58cde79af2","wcf_variation_selection_nonce":"62584e192b","wcf_quick_view_add_cart_nonce":"cc278c1a66","is_product_options":"no","allow_autocomplete_zipcode":"no","add_to_cart_text":"Processing...","wcf_refresh_checkout":false,"analytics_base_url":"https:\/\/pay.membero.pro\/wp-json\/cartflows-pro\/v1\/flow-analytics\/","analytics_nonce":"29385892ca","flow_cookie":"wcf-visited-flow-","step_cookie":"wcf-step-visited-","analytics_cookie_expire_time":365};</script><meta name='robots' content='max-image-preview:large' />
	<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
	<link rel='dns-prefetch' href='//sdk.mercadopago.com' />
<link rel='dns-prefetch' href='//cdnjs.cloudflare.com' />
<link rel="alternate" type="application/rss+xml" title="Feed para Checkout &raquo;" href="https://pay.membero.pro/feed/" />
<link rel="alternate" type="application/rss+xml" title="Feed de comentários para Checkout &raquo;" href="https://pay.membero.pro/comments/feed/" />
<link rel='stylesheet' id='CF_block-cartflows-style-css-css' href='https://pay.membero.pro/wp-content/plugins/cartflows/modules/gutenberg/build/style-blocks.css?ver=2.1.14' media='all' />
<link rel='stylesheet' id='CFP_block-cfp-style-css-css' href='https://pay.membero.pro/wp-content/plugins/cartflows-pro/modules/gutenberg/build/style-blocks.css?ver=2.1.7' media='all' />
<link rel='stylesheet' id='select2-css' href='https://pay.membero.pro/wp-content/plugins/woocommerce/assets/css/select2.css?ver=9.9.5' media='all' />
<link rel='stylesheet' id='woocommerce-layout-css' href='https://pay.membero.pro/wp-content/plugins/woocommerce/assets/css/woocommerce-layout.css?ver=9.9.5' media='all' />
<link rel='stylesheet' id='woocommerce-smallscreen-css' href='https://pay.membero.pro/wp-content/plugins/woocommerce/assets/css/woocommerce-smallscreen.css?ver=9.9.5' media='only screen and (max-width: 768px)' />
<link rel='stylesheet' id='woocommerce-general-css' href='https://pay.membero.pro/wp-content/plugins/woocommerce/assets/css/woocommerce.css?ver=9.9.5' media='all' />
<style id='woocommerce-inline-inline-css'>
.woocommerce form .form-row .required { visibility: visible; }
</style>
<link rel='stylesheet' id='brands-styles-css' href='https://pay.membero.pro/wp-content/plugins/woocommerce/assets/css/brands.css?ver=9.9.5' media='all' />
<link rel='stylesheet' id='font-awesome-all-css' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css?ver=6.6.0' media='all' />
<link rel='stylesheet' id='font-awesome-core-css' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/fontawesome.min.css?ver=6.6.0' media='all' />
<link rel='stylesheet' id='font-awesome-solid-css' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/solid.min.css?ver=6.6.0' media='all' />
<link rel='stylesheet' id='mercadopago_vars_css-css' href='https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/css/public/mp-vars.min.css?ver=8.2.0' media='all' />
<link rel='stylesheet' id='wc_mercadopago_checkout_components-css' href='https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/css/checkouts/mp-plugins-components.min.css?ver=8.2.0' media='all' />
<link rel='stylesheet' id='wcf-normalize-frontend-global-css' href='https://pay.membero.pro/wp-content/plugins/cartflows/assets/css/cartflows-normalize.css?ver=2.1.14' media='all' />
<style id='wcf-normalize-frontend-global-inline-css'>
:root{--ast-global-color-0:#046bd2;--ast-global-color-1:#045cb4;--ast-global-color-2:#1e293b;--ast-global-color-3:#334155;--ast-global-color-4:#FFFFFF;--ast-global-color-5:#F0F5FA;--ast-global-color-6:#111111;--ast-global-color-7:#D1D5DB;--ast-global-color-8:#111111;}
</style>
<link rel='stylesheet' id='wcf-frontend-global-css' href='https://pay.membero.pro/wp-content/plugins/cartflows/assets/css/frontend.css?ver=2.1.14' media='all' />
<style id='wcf-frontend-global-inline-css'>
:root { --e-global-color-wcfgcpprimarycolor: #f16334; --e-global-color-wcfgcpsecondarycolor: #000000; --e-global-color-wcfgcptextcolor: #4B5563; --e-global-color-wcfgcpaccentcolor: #1F2937;  }
</style>
<link rel='stylesheet' id='wcf-pro-frontend-global-css' href='https://pay.membero.pro/wp-content/plugins/cartflows-pro/assets/css/frontend.css?ver=2.1.7' media='all' />
<link rel='stylesheet' id='elementor-frontend-css' href='https://pay.membero.pro/wp-content/plugins/elementor/assets/css/frontend.min.css?ver=3.30.2' media='all' />
<link rel='stylesheet' id='elementor-post-101-css' href='https://pay.membero.pro/wp-content/uploads/elementor/css/post-101.css?ver=1752336914' media='all' />
<link rel='stylesheet' id='widget-icon-box-css' href='https://pay.membero.pro/wp-content/plugins/elementor/assets/css/widget-icon-box.min.css?ver=3.30.2' media='all' />
<link rel='stylesheet' id='widget-image-css' href='https://pay.membero.pro/wp-content/plugins/elementor/assets/css/widget-image.min.css?ver=3.30.2' media='all' />
<link rel='stylesheet' id='elementor-post-90-css' href='https://pay.membero.pro/wp-content/uploads/elementor/css/post-90.css?ver=1752337790' media='all' />
<link rel='stylesheet' id='wcf-checkout-template-css' href='https://pay.membero.pro/wp-content/plugins/cartflows/assets/css/checkout-template.css?ver=2.1.14' media='all' />
<style id='wcf-checkout-template-inline-css'>

			.wcf-embed-checkout-form .woocommerce #payment #place_order:before{
				content: "\e902";
				font-family: "cartflows-icon" !important;
				margin-right: 10px;
				font-size: 16px;
				font-weight: 500;
				top: 0px;
    			position: relative;
				opacity: 1;
				display: block;
			}
</style>
<link rel='stylesheet' id='wcf-pro-checkout-css' href='https://pay.membero.pro/wp-content/plugins/cartflows-pro/assets/css/checkout-styles.css?ver=2.1.7' media='all' />
<link rel='stylesheet' id='wcf-pro-multistep-checkout-css' href='https://pay.membero.pro/wp-content/plugins/cartflows-pro/assets/css/multistep-checkout.css?ver=2.1.7' media='all' />
<link rel='stylesheet' id='dashicons-css' href='https://pay.membero.pro/wp-includes/css/dashicons.min.css?ver=6.8.1' media='all' />
<link rel='stylesheet' id='elementor-gf-local-roboto-css' href='https://pay.membero.pro/wp-content/uploads/elementor/google-fonts/css/roboto.css?ver=1752325324' media='all' />
<link rel='stylesheet' id='elementor-gf-local-robotoslab-css' href='https://pay.membero.pro/wp-content/uploads/elementor/google-fonts/css/robotoslab.css?ver=1752325328' media='all' />
<link rel='stylesheet' id='elementor-gf-local-inter-css' href='https://pay.membero.pro/wp-content/uploads/elementor/google-fonts/css/inter.css?ver=1752325338' media='all' />
<script src="https://pay.membero.pro/wp-includes/js/jquery/jquery.min.js?ver=3.7.1" id="jquery-core-js"></script>
<script src="https://pay.membero.pro/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1" id="jquery-migrate-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/jquery-blockui/jquery.blockUI.min.js?ver=2.7.0-wc.9.9.5" id="jquery-blockui-js" defer data-wp-strategy="defer"></script>
<script id="wc-add-to-cart-js-extra">
var wc_add_to_cart_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/step\/elemenia\/?wc-ajax=%%endpoint%%&wcf_checkout_id=90","i18n_view_cart":"Ver carrinho","cart_url":"https:\/\/pay.membero.pro\/?page_id=12","is_cart":"","cart_redirect_after_add":"no"};
</script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/frontend/add-to-cart.min.js?ver=9.9.5" id="wc-add-to-cart-js" defer data-wp-strategy="defer"></script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/selectWoo/selectWoo.full.min.js?ver=1.0.9-wc.9.9.5" id="selectWoo-js" defer data-wp-strategy="defer"></script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/js-cookie/js.cookie.min.js?ver=2.1.4-wc.9.9.5" id="js-cookie-js" defer data-wp-strategy="defer"></script>
<script id="woocommerce-js-extra">
var woocommerce_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/step\/elemenia\/?wc-ajax=%%endpoint%%&wcf_checkout_id=90","i18n_password_show":"Mostrar senha","i18n_password_hide":"Ocultar senha"};
</script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/frontend/woocommerce.min.js?ver=9.9.5" id="woocommerce-js" defer data-wp-strategy="defer"></script>
<script id="wc-country-select-js-extra">
var wc_country_select_params = {"countries":"{\"AF\":[],\"AL\":{\"AL-01\":\"Berat\",\"AL-09\":\"Dib\\u00ebr\",\"AL-02\":\"Durr\\u00ebs\",\"AL-03\":\"Elbasan\",\"AL-04\":\"Fier\",\"AL-05\":\"Gjirokast\\u00ebr\",\"AL-06\":\"Kor\\u00e7\\u00eb\",\"AL-07\":\"Kuk\\u00ebs\",\"AL-08\":\"Lezh\\u00eb\",\"AL-10\":\"Shkod\\u00ebr\",\"AL-11\":\"Tirana\",\"AL-12\":\"Vlor\\u00eb\"},\"AO\":{\"BGO\":\"Bengo\",\"BLU\":\"Benguela\",\"BIE\":\"Bi\\u00e9\",\"CAB\":\"Cabinda\",\"CNN\":\"Cunene\",\"HUA\":\"Huambo\",\"HUI\":\"Hu\\u00edla\",\"CCU\":\"Kuando Kubango\",\"CNO\":\"Kwanza-Norte\",\"CUS\":\"Kwanza-Sul\",\"LUA\":\"Luanda\",\"LNO\":\"Lunda-Norte\",\"LSU\":\"Lunda-Sul\",\"MAL\":\"Malanje\",\"MOX\":\"Moxico\",\"NAM\":\"Namibe\",\"UIG\":\"U\\u00edge\",\"ZAI\":\"Zaire\"},\"AR\":{\"C\":\"Cidade Aut\\u00f4noma de Buenos Aires\",\"B\":\"Buenos Aires\",\"K\":\"Catamarca\",\"H\":\"Chaco\",\"U\":\"Chubut\",\"X\":\"C\\u00f3rdoba\",\"W\":\"Corrientes\",\"E\":\"Entre R\\u00edos\",\"P\":\"Formosa\",\"Y\":\"Jujuy\",\"L\":\"La Pampa\",\"F\":\"La Rioja\",\"M\":\"Mendoza\",\"N\":\"Misiones\",\"Q\":\"Neuqu\\u00e9n\",\"R\":\"R\\u00edo Negro\",\"A\":\"Salta\",\"J\":\"San Juan\",\"D\":\"San Luis\",\"Z\":\"Santa Cruz\",\"S\":\"Santa F\\u00e9\",\"G\":\"Santiago del Estero\",\"V\":\"Tierra del Fuego\",\"T\":\"Tucum\\u00e1n\"},\"AT\":[],\"AU\":{\"ACT\":\"Territ\\u00f3rio da Capital Australiana\",\"NSW\":\"Nova Gales do Sul\",\"NT\":\"Territ\\u00f3rio do Norte\",\"QLD\":\"Queensland\",\"SA\":\"Austr\\u00e1lia Meridional\",\"TAS\":\"Tasm\\u00e2nia\",\"VIC\":\"Victoria\",\"WA\":\"Austr\\u00e1lia Ocidental\"},\"AX\":[],\"BD\":{\"BD-05\":\"Bagerhat\",\"BD-01\":\"Bandarban\",\"BD-02\":\"Barguna\",\"BD-06\":\"Barishal\",\"BD-07\":\"Bhola\",\"BD-03\":\"Bogura\",\"BD-04\":\"Brahmanbaria\",\"BD-09\":\"Chandpur\",\"BD-10\":\"Chattogram\",\"BD-12\":\"Chuadanga\",\"BD-11\":\"Cox's Bazar\",\"BD-08\":\"Cumilla\",\"BD-13\":\"Dhaka\",\"BD-14\":\"Dinajpur\",\"BD-15\":\"Faridpur \",\"BD-16\":\"Feni\",\"BD-19\":\"Gaibandha\",\"BD-18\":\"Gazipur\",\"BD-17\":\"Gopalganj\",\"BD-20\":\"Habiganj\",\"BD-21\":\"Jamalpur\",\"BD-22\":\"Jashore\",\"BD-25\":\"Jhalokati\",\"BD-23\":\"Jhenaidah\",\"BD-24\":\"Joypurhat\",\"BD-29\":\"Khagrachhari\",\"BD-27\":\"Khulna\",\"BD-26\":\"Kishoreganj\",\"BD-28\":\"Kurigram\",\"BD-30\":\"Kushtia\",\"BD-31\":\"Lakshmipur\",\"BD-32\":\"Lalmonirhat\",\"BD-36\":\"Madaripur\",\"BD-37\":\"Magura\",\"BD-33\":\"Manikganj \",\"BD-39\":\"Meherpur\",\"BD-38\":\"Moulvibazar\",\"BD-35\":\"Munshiganj\",\"BD-34\":\"Mymensingh\",\"BD-48\":\"Naogaon\",\"BD-43\":\"Narail\",\"BD-40\":\"Narayanganj\",\"BD-42\":\"Narsingdi\",\"BD-44\":\"Natore\",\"BD-45\":\"Nawabganj\",\"BD-41\":\"Netrakona\",\"BD-46\":\"Nilphamari\",\"BD-47\":\"Noakhali\",\"BD-49\":\"Pabna\",\"BD-52\":\"Panchagarh\",\"BD-51\":\"Patuakhali\",\"BD-50\":\"Pirojpur\",\"BD-53\":\"Rajbari\",\"BD-54\":\"Rajshahi\",\"BD-56\":\"Rangamati\",\"BD-55\":\"Rangpur\",\"BD-58\":\"Satkhira\",\"BD-62\":\"Shariatpur\",\"BD-57\":\"Sherpur\",\"BD-59\":\"Sirajganj\",\"BD-61\":\"Sunamganj\",\"BD-60\":\"Sylhet\",\"BD-63\":\"Tangail\",\"BD-64\":\"Thakurgaon\"},\"BE\":[],\"BG\":{\"BG-01\":\"Blagoevgrad\",\"BG-02\":\"Burgas\",\"BG-08\":\"Dobrich\",\"BG-07\":\"Gabrovo\",\"BG-26\":\"Haskovo\",\"BG-09\":\"Kardzhali\",\"BG-10\":\"Kyustendil\",\"BG-11\":\"Lovech\",\"BG-12\":\"Montana\",\"BG-13\":\"Pazardzhik\",\"BG-14\":\"Pernik\",\"BG-15\":\"Pleven\",\"BG-16\":\"Plovdiv\",\"BG-17\":\"Razgrad\",\"BG-18\":\"Ruse\",\"BG-27\":\"Shumen\",\"BG-19\":\"Silistra\",\"BG-20\":\"Sliven\",\"BG-21\":\"Smolyan\",\"BG-23\":\"Distrito de Sofia\",\"BG-22\":\"Sofia\",\"BG-24\":\"Stara Zagora\",\"BG-25\":\"Targovishte\",\"BG-03\":\"Varna\",\"BG-04\":\"Veliko Tarnovo\",\"BG-05\":\"Vidin\",\"BG-06\":\"Vratsa\",\"BG-28\":\"Yambol\"},\"BH\":[],\"BI\":[],\"BJ\":{\"AL\":\"Alibori\",\"AK\":\"Atakora\",\"AQ\":\"Atlantique\",\"BO\":\"Borgou\",\"CO\":\"Collines\",\"KO\":\"Kouffo\",\"DO\":\"Donga\",\"LI\":\"Littoral\",\"MO\":\"Mono\",\"OU\":\"Ou\\u00e9m\\u00e9\",\"PL\":\"Plateau\",\"ZO\":\"Zou\"},\"BO\":{\"BO-B\":\"Beni\",\"BO-H\":\"Chuquisaca\",\"BO-C\":\"Cochabamba\",\"BO-L\":\"La Paz\",\"BO-O\":\"Oruro\",\"BO-N\":\"Pando\",\"BO-P\":\"Potos\\u00ed\",\"BO-S\":\"Santa Cruz\",\"BO-T\":\"Tarija\"},\"BR\":{\"AC\":\"Acre\",\"AL\":\"Alagoas\",\"AP\":\"Amap\\u00e1\",\"AM\":\"Amazonas\",\"BA\":\"Bahia\",\"CE\":\"Cear\\u00e1\",\"DF\":\"Distrito Federal\",\"ES\":\"Esp\\u00edrito Santo\",\"GO\":\"Goi\\u00e1s\",\"MA\":\"Maranh\\u00e3o\",\"MT\":\"Mato Grosso\",\"MS\":\"Mato Grosso do Sul\",\"MG\":\"Minas Gerais\",\"PA\":\"Par\\u00e1\",\"PB\":\"Para\\u00edba\",\"PR\":\"Paran\\u00e1\",\"PE\":\"Pernambuco\",\"PI\":\"Piau\\u00ed\",\"RJ\":\"Rio de Janeiro\",\"RN\":\"Rio Grande do Norte\",\"RS\":\"Rio Grande do Sul\",\"RO\":\"Rond\\u00f4nia\",\"RR\":\"Roraima\",\"SC\":\"Santa Catarina\",\"SP\":\"S\\u00e3o Paulo\",\"SE\":\"Sergipe\",\"TO\":\"Tocantins\"},\"CA\":{\"AB\":\"Alberta\",\"BC\":\"Col\\u00fambia Brit\\u00e2nica\",\"MB\":\"Manitoba\",\"NB\":\"Nova Brunswick\",\"NL\":\"Terra Nova e Labrador\",\"NT\":\"Territ\\u00f3rios do Noroeste\",\"NS\":\"Nova Esc\\u00f3cia\",\"NU\":\"Nunavut\",\"ON\":\"Ont\\u00e1rio\",\"PE\":\"Ilha do Pr\\u00edncipe Eduardo\",\"QC\":\"Quebec\",\"SK\":\"Saskatchewan\",\"YT\":\"Yukon\"},\"CH\":{\"AG\":\"Aargau\",\"AR\":\"Appenzell Ausserrhoden\",\"AI\":\"Appenzell Innerrhoden\",\"BL\":\"Basel-Landschaft\",\"BS\":\"Basel-Stadt\",\"BE\":\"Bern\",\"FR\":\"Fribourg\",\"GE\":\"Geneva\",\"GL\":\"Glarus\",\"GR\":\"Graub\\u00fcnden\",\"JU\":\"Jura\",\"LU\":\"Luzern\",\"NE\":\"Neuch\\u00e2tel\",\"NW\":\"Nidwalden\",\"OW\":\"Obwalden\",\"SH\":\"Schaffhausen\",\"SZ\":\"Schwyz\",\"SO\":\"Solothurn\",\"SG\":\"St. Gallen\",\"TG\":\"Thurgau\",\"TI\":\"Ticino\",\"UR\":\"Uri\",\"VS\":\"Valais\",\"VD\":\"Vaud\",\"ZG\":\"Zug\",\"ZH\":\"Z\\u00fcrich\"},\"CL\":{\"CL-AI\":\"Ais\\u00e9n del General Carlos Iba\\u00f1ez del Campo\",\"CL-AN\":\"Antofagasta\",\"CL-AP\":\"Arica e Parinacota\",\"CL-AR\":\"La Araucan\\u00eda\",\"CL-AT\":\"Atacama\",\"CL-BI\":\"Biob\\u00edo\",\"CL-CO\":\"Coquimbo\",\"CL-LI\":\"Libertador General Bernardo O'Higgins\",\"CL-LL\":\"Los Lagos\",\"CL-LR\":\"Los R\\u00edos\",\"CL-MA\":\"Magallanes\",\"CL-ML\":\"Maule\",\"CL-NB\":\"\\u00d1uble\",\"CL-RM\":\"Regi\\u00e3o Metropolitana de Santiago\",\"CL-TA\":\"Tarapac\\u00e1\",\"CL-VS\":\"Valpara\\u00edso\"},\"CN\":{\"CN1\":\"Yunnan \\\/ \\u4e91\\u5357\",\"CN2\":\"Beijing \\\/ \\u5317\\u4eac\",\"CN3\":\"Tianjin \\\/ \\u5929\\u6d25\",\"CN4\":\"Hebei \\\/ \\u6cb3\\u5317\",\"CN5\":\"Shanxi \\\/ \\u5c71\\u897f\",\"CN6\":\"Inner Mongolia \\\/ \\u5167\\u8499\\u53e4\",\"CN7\":\"Liaoning \\\/ \\u8fbd\\u5b81\",\"CN8\":\"Jilin \\\/ \\u5409\\u6797\",\"CN9\":\"Heilongjiang \\\/ \\u9ed1\\u9f99\\u6c5f\",\"CN10\":\"Shanghai \\\/ \\u4e0a\\u6d77\",\"CN11\":\"Jiangsu \\\/ \\u6c5f\\u82cf\",\"CN12\":\"Zhejiang \\\/ \\u6d59\\u6c5f\",\"CN13\":\"Anhui \\\/ \\u5b89\\u5fbd\",\"CN14\":\"Fujian \\\/ \\u798f\\u5efa\",\"CN15\":\"Jiangxi \\\/ \\u6c5f\\u897f\",\"CN16\":\"Shandong \\\/ \\u5c71\\u4e1c\",\"CN17\":\"Henan \\\/ \\u6cb3\\u5357\",\"CN18\":\"Hubei \\\/ \\u6e56\\u5317\",\"CN19\":\"Hunan \\\/ \\u6e56\\u5357\",\"CN20\":\"Guangdong \\\/ \\u5e7f\\u4e1c\",\"CN21\":\"Guangxi Zhuang \\\/ \\u5e7f\\u897f\\u58ee\\u65cf\",\"CN22\":\"Hainan \\\/ \\u6d77\\u5357\",\"CN23\":\"Chongqing \\\/ \\u91cd\\u5e86\",\"CN24\":\"Sichuan \\\/ \\u56db\\u5ddd\",\"CN25\":\"Guizhou \\\/ \\u8d35\\u5dde\",\"CN26\":\"Shaanxi \\\/ \\u9655\\u897f\",\"CN27\":\"Gansu \\\/ \\u7518\\u8083\",\"CN28\":\"Qinghai \\\/ \\u9752\\u6d77\",\"CN29\":\"Ningxia Hui \\\/ \\u5b81\\u590f\",\"CN30\":\"Macao \\\/ \\u6fb3\\u95e8\",\"CN31\":\"Tibet \\\/ \\u897f\\u85cf\",\"CN32\":\"Xinjiang \\\/ \\u65b0\\u7586\"},\"CO\":{\"CO-AMA\":\"Amazonas\",\"CO-ANT\":\"Antioquia\",\"CO-ARA\":\"Arauca\",\"CO-ATL\":\"Atl\\u00e1ntico\",\"CO-BOL\":\"Bol\\u00edvar\",\"CO-BOY\":\"Boyac\\u00e1\",\"CO-CAL\":\"Caldas\",\"CO-CAQ\":\"Caquet\\u00e1\",\"CO-CAS\":\"Casanare\",\"CO-CAU\":\"Cauca\",\"CO-CES\":\"Cesar\",\"CO-CHO\":\"Choc\\u00f3\",\"CO-COR\":\"C\\u00f3rdoba\",\"CO-CUN\":\"Cundinamarca\",\"CO-DC\":\"Distrito Capital\",\"CO-GUA\":\"Guain\\u00eda\",\"CO-GUV\":\"Guaviare\",\"CO-HUI\":\"Huila\",\"CO-LAG\":\"La Guajira\",\"CO-MAG\":\"Magdalena\",\"CO-MET\":\"Meta\",\"CO-NAR\":\"Nari\\u00f1o\",\"CO-NSA\":\"Norte de Santander\",\"CO-PUT\":\"Putumayo\",\"CO-QUI\":\"Quind\\u00edo\",\"CO-RIS\":\"Risaralda\",\"CO-SAN\":\"Santander\",\"CO-SAP\":\"San Andr\\u00e9s & Providencia\",\"CO-SUC\":\"Sucre\",\"CO-TOL\":\"Tolima\",\"CO-VAC\":\"Valle del Cauca\",\"CO-VAU\":\"Vaup\\u00e9s\",\"CO-VID\":\"Vichada\"},\"CR\":{\"CR-A\":\"Alajuela\",\"CR-C\":\"Cartago\",\"CR-G\":\"Guanacaste\",\"CR-H\":\"Heredia\",\"CR-L\":\"Lim\\u00f3n\",\"CR-P\":\"Puntarenas\",\"CR-SJ\":\"San Jos\\u00e9\"},\"CZ\":[],\"DE\":{\"DE-BW\":\"Baden-W\\u00fcrttemberg\",\"DE-BY\":\"Baviera\",\"DE-BE\":\"Berlim\",\"DE-BB\":\"Brandemburgo\",\"DE-HB\":\"Bremen\",\"DE-HH\":\"Hamburgo\",\"DE-HE\":\"Hesse\",\"DE-MV\":\"Meclemburgo-Pomer\\u00e2nia Ocidental\",\"DE-NI\":\"Baixa Sax\\u00f4nia\",\"DE-NW\":\"Ren\\u00e2nia do Norte-Vestf\\u00e1lia\",\"DE-RP\":\"Ren\\u00e2nia-Palatinado\",\"DE-SL\":\"Sarre\",\"DE-SN\":\"Sax\\u00f4nia\",\"DE-ST\":\"Sax\\u00f4nia-Anhalt\",\"DE-SH\":\"Schleswig-Holstein\",\"DE-TH\":\"Tur\\u00edngia\"},\"DK\":[],\"DO\":{\"DO-01\":\"Distrito Nacional\",\"DO-02\":\"Azua\",\"DO-03\":\"Bahoruco\",\"DO-04\":\"Barahona\",\"DO-33\":\"Cibao Nordeste\",\"DO-34\":\"Cibao Noroeste\",\"DO-35\":\"Cibao Norte\",\"DO-36\":\"Cibao Sur\",\"DO-05\":\"Dajab\\u00f3n\",\"DO-06\":\"Duarte\",\"DO-08\":\"El Seibo\",\"DO-37\":\"El Valle\",\"DO-07\":\"El\\u00edas Pi\\u00f1a\",\"DO-38\":\"Enriquillo\",\"DO-09\":\"Espaillat\",\"DO-30\":\"Hato Mayor\",\"DO-19\":\"Hermanas Mirabal\",\"DO-39\":\"Hig\\u00fcamo\",\"DO-10\":\"Independencia\",\"DO-11\":\"La Altagracia\",\"DO-12\":\"La Romana\",\"DO-13\":\"La Vega\",\"DO-14\":\"Mar\\u00eda Trinidad S\\u00e1nchez\",\"DO-28\":\"Monse\\u00f1or Nouel\",\"DO-15\":\"Monte Cristi\",\"DO-29\":\"Monte Plata\",\"DO-40\":\"Ozama\",\"DO-16\":\"Pedernales\",\"DO-17\":\"Peravia\",\"DO-18\":\"Puerto Plata\",\"DO-20\":\"Saman\\u00e1\",\"DO-21\":\"San Crist\\u00f3bal\",\"DO-31\":\"San Jos\\u00e9 de Ocoa\",\"DO-22\":\"San Juan\",\"DO-23\":\"San Pedro de Macor\\u00eds\",\"DO-24\":\"S\\u00e1nchez Ram\\u00edrez\",\"DO-25\":\"Santiago\",\"DO-26\":\"Santiago Rodr\\u00edguez\",\"DO-32\":\"Santo Domingo\",\"DO-41\":\"Valdesia\",\"DO-27\":\"Valverde\",\"DO-42\":\"Yuma\"},\"DZ\":{\"DZ-01\":\"Adrar\",\"DZ-02\":\"Chlef\",\"DZ-03\":\"Laghouat\",\"DZ-04\":\"Oum El Bouaghi\",\"DZ-05\":\"Batna\",\"DZ-06\":\"B\\u00e9ja\\u00efa\",\"DZ-07\":\"Biskra\",\"DZ-08\":\"B\\u00e9char\",\"DZ-09\":\"Blida\",\"DZ-10\":\"Bouira\",\"DZ-11\":\"Tamanghasset\",\"DZ-12\":\"T\\u00e9bessa\",\"DZ-13\":\"Tlemcen\",\"DZ-14\":\"Tiaret\",\"DZ-15\":\"Tizi Ouzou\",\"DZ-16\":\"Algiers\",\"DZ-17\":\"Djelfa\",\"DZ-18\":\"Jijel\",\"DZ-19\":\"S\\u00e9tif\",\"DZ-20\":\"Sa\\u00efda\",\"DZ-21\":\"Skikda\",\"DZ-22\":\"Sidi Bel Abb\\u00e8s\",\"DZ-23\":\"Annaba\",\"DZ-24\":\"Guelma\",\"DZ-25\":\"Constantine\",\"DZ-26\":\"M\\u00e9d\\u00e9a\",\"DZ-27\":\"Mostaganem\",\"DZ-28\":\"M\\u2019Sila\",\"DZ-29\":\"Mascara\",\"DZ-30\":\"Ouargla\",\"DZ-31\":\"Oran\",\"DZ-32\":\"El Bayadh\",\"DZ-33\":\"Illizi\",\"DZ-34\":\"Bordj Bou Arr\\u00e9ridj\",\"DZ-35\":\"Boumerd\\u00e8s\",\"DZ-36\":\"El Tarf\",\"DZ-37\":\"Tindouf\",\"DZ-38\":\"Tissemsilt\",\"DZ-39\":\"El Oued\",\"DZ-40\":\"Khenchela\",\"DZ-41\":\"Souk Ahras\",\"DZ-42\":\"Tipasa\",\"DZ-43\":\"Mila\",\"DZ-44\":\"A\\u00efn Defla\",\"DZ-45\":\"Naama\",\"DZ-46\":\"A\\u00efn T\\u00e9mouchent\",\"DZ-47\":\"Gharda\\u00efa\",\"DZ-48\":\"Relizane\"},\"EE\":[],\"EC\":{\"EC-A\":\"Azuay\",\"EC-B\":\"Bol\\u00edvar\",\"EC-F\":\"Ca\\u00f1ar\",\"EC-C\":\"Carchi\",\"EC-H\":\"Chimborazo\",\"EC-X\":\"Cotopaxi\",\"EC-O\":\"El Oro\",\"EC-E\":\"Esmeraldas\",\"EC-W\":\"Gal\\u00e1pagos\",\"EC-G\":\"Guayas\",\"EC-I\":\"Imbabura\",\"EC-L\":\"Loja\",\"EC-R\":\"Los R\\u00edos\",\"EC-M\":\"Manab\\u00ed\",\"EC-S\":\"Morona-Santiago\",\"EC-N\":\"Napo\",\"EC-D\":\"Orellana\",\"EC-Y\":\"Pastaza\",\"EC-P\":\"Pichincha\",\"EC-SE\":\"Santa Elena\",\"EC-SD\":\"Santo Domingo de los Ts\\u00e1chilas\",\"EC-U\":\"Sucumb\\u00edos\",\"EC-T\":\"Tungurahua\",\"EC-Z\":\"Zamora-Chinchipe\"},\"EG\":{\"EGALX\":\"Alexandria\",\"EGASN\":\"Assu\\u00e3\",\"EGAST\":\"Assiute\",\"EGBA\":\"Mar Vermelho\",\"EGBH\":\"Boeira\",\"EGBNS\":\"Beni Suefe\",\"EGC\":\"Cairo\",\"EGDK\":\"Dacalia\",\"EGDT\":\"Damieta\",\"EGFYM\":\"Faium\",\"EGGH\":\"Ocidental\",\"EGGZ\":\"Guiz\\u00e9\",\"EGIS\":\"Isma\\u00edlia\",\"EGJS\":\"Sinai do Sul\",\"EGKB\":\"Caliubia\",\"EGKFS\":\"Cafrel Xeique\",\"EGKN\":\"Quena\",\"EGLX\":\"Luxor\",\"EGMN\":\"Minia\",\"EGMNF\":\"Monufia\",\"EGMT\":\"Matru\",\"EGPTS\":\"Porto Sa\\u00edde\",\"EGSHG\":\"Soague\",\"EGSHR\":\"Xarquia\",\"EGSIN\":\"Sinai do Norte\",\"EGSUZ\":\"Suez\",\"EGWAD\":\"Vale Novo\"},\"ES\":{\"C\":\"A Coru\\u00f1a\",\"VI\":\"Araba\\\/\\u00c1lava\",\"AB\":\"Albacete\",\"A\":\"Alicante\",\"AL\":\"Almer\\u00eda\",\"O\":\"Asturias\",\"AV\":\"\\u00c1vila\",\"BA\":\"Badajoz\",\"PM\":\"Baleares\",\"B\":\"Barcelona\",\"BU\":\"Burgos\",\"CC\":\"C\\u00e1ceres\",\"CA\":\"C\\u00e1diz\",\"S\":\"Cantabria\",\"CS\":\"Castell\\u00f3n\",\"CE\":\"Ceuta\",\"CR\":\"Ciudad Real\",\"CO\":\"C\\u00f3rdoba\",\"CU\":\"Cuenca\",\"GI\":\"Girona\",\"GR\":\"Granada\",\"GU\":\"Guadalajara\",\"SS\":\"Gipuzkoa\",\"H\":\"Huelva\",\"HU\":\"Huesca\",\"J\":\"Ja\\u00e9n\",\"LO\":\"La Rioja\",\"GC\":\"Las Palmas\",\"LE\":\"Le\\u00f3n\",\"L\":\"Lleida\",\"LU\":\"Lugo\",\"M\":\"Madrid\",\"MA\":\"M\\u00e1laga\",\"ML\":\"Melilla\",\"MU\":\"Murcia\",\"NA\":\"Navarra\",\"OR\":\"Ourense\",\"P\":\"Palencia\",\"PO\":\"Pontevedra\",\"SA\":\"Salamanca\",\"TF\":\"Santa Cruz de Tenerife\",\"SG\":\"Segovia\",\"SE\":\"Sevilla\",\"SO\":\"Soria\",\"T\":\"Tarragona\",\"TE\":\"Teruel\",\"TO\":\"Toledo\",\"V\":\"Valencia\",\"VA\":\"Valladolid\",\"BI\":\"Biscaia\",\"ZA\":\"Zamora\",\"Z\":\"Zaragoza\"},\"ET\":[],\"FI\":[],\"FR\":[],\"GF\":[],\"GH\":{\"AF\":\"Ahafo\",\"AH\":\"Ashanti\",\"BA\":\"Brong-Ahafo\",\"BO\":\"Bono\",\"BE\":\"Bono East\",\"CP\":\"Central\",\"EP\":\"Oriental\",\"AA\":\"Greater Accra\",\"NE\":\"Nordeste\",\"NP\":\"Norte\",\"OT\":\"Oti\",\"SV\":\"Savannah\",\"UE\":\"Upper East\",\"UW\":\"Upper West\",\"TV\":\"Volta\",\"WP\":\"Ocidental\",\"WN\":\"Norte ocidental\"},\"GP\":[],\"GR\":{\"I\":\"Attica\",\"A\":\"Maced\\u00f4nia Oriental e Tr\\u00e1cia\",\"B\":\"Maced\\u00f4nia Central\",\"C\":\"Maced\\u00f4nia do Norte\",\"D\":\"Epirus\",\"E\":\"Thessaly\",\"F\":\"Ionian Islands\",\"G\":\"Gr\\u00e9cia Ocidental\",\"H\":\"Gr\\u00e9cia Central\",\"J\":\"Peloponnese\",\"K\":\"Egeu do Norte\",\"L\":\"Egeu do Sul\",\"M\":\"Creta\"},\"GT\":{\"GT-AV\":\"Alta Verapaz\",\"GT-BV\":\"Baja Verapaz\",\"GT-CM\":\"Chimaltenango\",\"GT-CQ\":\"Chiquimula\",\"GT-PR\":\"El Progreso\",\"GT-ES\":\"Escuintla\",\"GT-GU\":\"Guatemala\",\"GT-HU\":\"Huehuetenango\",\"GT-IZ\":\"Izabal\",\"GT-JA\":\"Jalapa\",\"GT-JU\":\"Jutiapa\",\"GT-PE\":\"Pet\\u00e9n\",\"GT-QZ\":\"Quetzaltenango\",\"GT-QC\":\"Quich\\u00e9\",\"GT-RE\":\"Retalhuleu\",\"GT-SA\":\"Sacatep\\u00e9quez\",\"GT-SM\":\"San Marcos\",\"GT-SR\":\"Santa Rosa\",\"GT-SO\":\"Solol\\u00e1\",\"GT-SU\":\"Suchitep\\u00e9quez\",\"GT-TO\":\"Totonicap\\u00e1n\",\"GT-ZA\":\"Zacapa\"},\"HK\":{\"HONG KONG\":\"Ilha de Hong Kong\",\"KOWLOON\":\"Kowloon\",\"NEW TERRITORIES\":\"Novos Territ\\u00f3rios\"},\"HN\":{\"HN-AT\":\"Atl\\u00e1ntida\",\"HN-IB\":\"Ilhas da Ba\\u00eda\",\"HN-CH\":\"Choluteca\",\"HN-CL\":\"Col\\u00f3n\",\"HN-CM\":\"Comayagua\",\"HN-CP\":\"Cop\\u00e1n\",\"HN-CR\":\"Cort\\u00e9s\",\"HN-EP\":\"El Para\\u00edso\",\"HN-FM\":\"Francisco Moraz\\u00e1n\",\"HN-GD\":\"Gracias a Dios\",\"HN-IN\":\"Intibuc\\u00e1\",\"HN-LE\":\"Lempira\",\"HN-LP\":\"La Paz\",\"HN-OC\":\"Ocotepeque\",\"HN-OL\":\"Olancho\",\"HN-SB\":\"Santa B\\u00e1rbara\",\"HN-VA\":\"Valle\",\"HN-YO\":\"Yoro\"},\"HR\":{\"HR-01\":\"Condado de Zagreb\",\"HR-02\":\"Condado de Krapina-Zagorje\",\"HR-03\":\"Condado de Sisak-Moslavina\",\"HR-04\":\"Condado de Karlovac\",\"HR-05\":\"Condado de Vara\\u017edin\",\"HR-06\":\"Condado de Koprivnica-Kri\\u017eevci\",\"HR-07\":\"Condado de Bjelovar-Bilogora\",\"HR-08\":\"Condado de Primorje-Gorski Kotar\",\"HR-09\":\"Condado de Lika-Senj\",\"HR-10\":\"Condado de Virovitica-Podravina\",\"HR-11\":\"Condado de Po\\u017eega-Eslav\\u00f4nia\",\"HR-12\":\"Condado de Brod-Posavina\",\"HR-13\":\"Condado de Zadar\",\"HR-14\":\"Condado de Osijek-Baranja\",\"HR-15\":\"Condado de \\u0160ibenik-Knin\",\"HR-16\":\"Condado de Vukovar-Srijem\",\"HR-17\":\"Condado de Split-Dalm\\u00e1cia\",\"HR-18\":\"Condado de \\u00cdstria\",\"HR-19\":\"Condado de Dubrovnik-Neretva\",\"HR-20\":\"Condado de Me\\u0111imurje\",\"HR-21\":\"Cidade de Zagreb\"},\"HU\":{\"BK\":\"B\\u00e1cs-Kiskun\",\"BE\":\"B\\u00e9k\\u00e9s\",\"BA\":\"Baranya\",\"BZ\":\"Borsod-Aba\\u00faj-Zempl\\u00e9n\",\"BU\":\"Budapeste\",\"CS\":\"Csongr\\u00e1d-Csan\\u00e1d\",\"FE\":\"Fej\\u00e9r\",\"GS\":\"Gy\\u0151r-Moson-Sopron\",\"HB\":\"Hajd\\u00fa-Bihar\",\"HE\":\"Heves\",\"JN\":\"J\\u00e1sz-Nagykun-Szolnok\",\"KE\":\"Kom\\u00e1rom-Esztergom\",\"NO\":\"N\\u00f3gr\\u00e1d\",\"PE\":\"Pest\",\"SO\":\"Somogy\",\"SZ\":\"Szabolcs-Szatm\\u00e1r-Bereg\",\"TO\":\"Tolna\",\"VA\":\"Vas\",\"VE\":\"Veszpr\\u00e9m\",\"ZA\":\"Zala\"},\"ID\":{\"AC\":\"Daerah Istimewa Aceh\",\"SU\":\"Sumatera Utara\",\"SB\":\"Sumatera Barat\",\"RI\":\"Riau\",\"KR\":\"Kepulauan Riau\",\"JA\":\"Jambi\",\"SS\":\"Sumatera Selatan\",\"BB\":\"Bangka Belitung\",\"BE\":\"Bengkulu\",\"LA\":\"Lampung\",\"JK\":\"DKI Jakarta\",\"JB\":\"Jawa Barat\",\"BT\":\"Banten\",\"JT\":\"Jawa Tengah\",\"JI\":\"Jawa Timur\",\"YO\":\"Daerah Istimewa Yogyakarta\",\"BA\":\"Bali\",\"NB\":\"Nusa Tenggara Barat\",\"NT\":\"Nusa Tenggara Timur\",\"KB\":\"Kalimantan Barat\",\"KT\":\"Kalimantan Tengah\",\"KI\":\"Kalimantan Timur\",\"KS\":\"Kalimantan Selatan\",\"KU\":\"Kalimantan Utara\",\"SA\":\"Sulawesi Utara\",\"ST\":\"Sulawesi Tengah\",\"SG\":\"Sulawesi Tenggara\",\"SR\":\"Sulawesi Barat\",\"SN\":\"Sulawesi Selatan\",\"GO\":\"Gorontalo\",\"MA\":\"Maluku\",\"MU\":\"Maluku Utara\",\"PA\":\"Papua\",\"PB\":\"Barat da Papua\"},\"IE\":{\"CW\":\"Carlow\",\"CN\":\"Cavan\",\"CE\":\"Clare\",\"CO\":\"Cork\",\"DL\":\"Donegal\",\"D\":\"Dublin\",\"G\":\"Galway\",\"KY\":\"Kerry\",\"KE\":\"Kildare\",\"KK\":\"Kilkenny\",\"LS\":\"Laois\",\"LM\":\"Leitrim\",\"LK\":\"Limerick\",\"LD\":\"Longford\",\"LH\":\"Louth\",\"MO\":\"Mayo\",\"MH\":\"Meath\",\"MN\":\"Monaghan\",\"OY\":\"Offaly\",\"RN\":\"Roscommon\",\"SO\":\"Sligo\",\"TA\":\"Tipperary\",\"WD\":\"Waterford\",\"WH\":\"Westmeath\",\"WX\":\"Wexford\",\"WW\":\"Wicklow\"},\"IN\":{\"AP\":\"Andhra Pradesh\",\"AR\":\"Arunachal Pradesh\",\"AS\":\"Assam\",\"BR\":\"Bihar\",\"CT\":\"Chhattisgarh\",\"GA\":\"Goa\",\"GJ\":\"Gujarat\",\"HR\":\"Haryana\",\"HP\":\"Himachal Pradesh\",\"JK\":\"Jammu and Kashmir\",\"JH\":\"Jharkhand\",\"KA\":\"Karnataka\",\"KL\":\"Kerala\",\"LA\":\"Ladaque\",\"MP\":\"Madhya Pradesh\",\"MH\":\"Maharashtra\",\"MN\":\"Manipur\",\"ML\":\"Meghalaya\",\"MZ\":\"Mizoram\",\"NL\":\"Nagaland\",\"OD\":\"Odisha\",\"PB\":\"Punjab\",\"RJ\":\"Rajasthan\",\"SK\":\"Sikkim\",\"TN\":\"Tamil Nadu\",\"TS\":\"Telangana\",\"TR\":\"Tripura\",\"UK\":\"Uttarakhand\",\"UP\":\"Uttar Pradesh\",\"WB\":\"West Bengal\",\"AN\":\"Andaman and Nicobar Islands\",\"CH\":\"Chandigarh\",\"DN\":\"Dadr\\u00e1 e Nagar-Aveli\",\"DD\":\"Daman and Diu\",\"DL\":\"Delhi\",\"LD\":\"Lakshadeep\",\"PY\":\"Pondicherry (Puducherry)\"},\"IR\":{\"KHZ\":\"Cuzist\\u00e3o (\\u062e\\u0648\\u0632\\u0633\\u062a\\u0627\\u0646)\",\"THR\":\"Teer\\u00e3 (\\u062a\\u0647\\u0631\\u0627\\u0646)\",\"ILM\":\"Ilam (\\u0627\\u06cc\\u0644\\u0627\\u0645)\",\"BHR\":\"Bushehr (\\u0628\\u0648\\u0634\\u0647\\u0631)\",\"ADL\":\"Ardabil (\\u0627\\u0631\\u062f\\u0628\\u06cc\\u0644)\",\"ESF\":\"Isfahan (\\u0627\\u0635\\u0641\\u0647\\u0627\\u0646)\",\"YZD\":\"Yazd (\\u06cc\\u0632\\u062f)\",\"KRH\":\"Kermanshah (\\u06a9\\u0631\\u0645\\u0627\\u0646\\u0634\\u0627\\u0647)\",\"KRN\":\"Kerman (\\u06a9\\u0631\\u0645\\u0627\\u0646)\",\"HDN\":\"Hamad\\u00e3 (\\u0647\\u0645\\u062f\\u0627\\u0646)\",\"GZN\":\"Qazvin (\\u0642\\u0632\\u0648\\u06cc\\u0646)\",\"ZJN\":\"Zanjan (\\u0632\\u0646\\u062c\\u0627\\u0646)\",\"LRS\":\"Lorest\\u00e3o (\\u0644\\u0631\\u0633\\u062a\\u0627\\u0646)\",\"ABZ\":\"Alborz (\\u0627\\u0644\\u0628\\u0631\\u0632)\",\"EAZ\":\"Azerbaij\\u00e3o Oriental (\\u0622\\u0630\\u0631\\u0628\\u0627\\u06cc\\u062c\\u0627\\u0646 \\u0634\\u0631\\u0642\\u06cc)\",\"WAZ\":\"Azerbaij\\u00e3o Ocidental (\\u0622\\u0630\\u0631\\u0628\\u0627\\u06cc\\u062c\\u0627\\u0646 \\u063a\\u0631\\u0628\\u06cc)\",\"CHB\":\"Chahar Mahaal e Bakhtiari (\\u0686\\u0647\\u0627\\u0631\\u0645\\u062d\\u0627\\u0644 \\u0648 \\u0628\\u062e\\u062a\\u06cc\\u0627\\u0631\\u06cc)\",\"SKH\":\"Cora\\u00e7\\u00e3o do Sul (\\u062e\\u0631\\u0627\\u0633\\u0627\\u0646 \\u062c\\u0646\\u0648\\u0628\\u06cc)\",\"RKH\":\"Cora\\u00e7\\u00e3o Razavi (\\u062e\\u0631\\u0627\\u0633\\u0627\\u0646 \\u0631\\u0636\\u0648\\u06cc)\",\"NKH\":\"Khorasan do norte (\\u062e\\u0631\\u0627\\u0633\\u0627\\u0646 \\u0634\\u0645\\u0627\\u0644\\u06cc)\",\"SMN\":\"Semnan (\\u0633\\u0645\\u0646\\u0627\\u0646)\",\"FRS\":\"Fars (\\u0641\\u0627\\u0631\\u0633)\",\"QHM\":\"Qom (\\u0642\\u0645)\",\"KRD\":\"Curdist\\u00e3o \\\/ \\u06a9\\u0631\\u062f\\u0633\\u062a\\u0627\\u0646)\",\"KBD\":\"Kohkiluyeh e Buyer Ahmad (\\u06a9\\u0647\\u06af\\u06cc\\u0644\\u0648\\u06cc\\u06cc\\u0647 \\u0648 \\u0628\\u0648\\u06cc\\u0631\\u0627\\u062d\\u0645\\u062f)\",\"GLS\":\"Golestan (\\u06af\\u0644\\u0633\\u062a\\u0627\\u0646)\",\"GIL\":\"Gilan (\\u06af\\u06cc\\u0644\\u0627\\u0646)\",\"MZN\":\"Mazandaran (\\u0645\\u0627\\u0632\\u0646\\u062f\\u0631\\u0627\\u0646)\",\"MKZ\":\"Markazi (\\u0645\\u0631\\u06a9\\u0632\\u06cc)\",\"HRZ\":\"Hormozgan (\\u0647\\u0631\\u0645\\u0632\\u06af\\u0627\\u0646)\",\"SBN\":\"Sist\\u00e3o-Baluchist\\u00e3o (\\u0633\\u06cc\\u0633\\u062a\\u0627\\u0646 \\u0648 \\u0628\\u0644\\u0648\\u0686\\u0633\\u062a\\u0627\\u0646)\"},\"IS\":[],\"IT\":{\"AG\":\"Agrigento\",\"AL\":\"Alessandria\",\"AN\":\"Ancona\",\"AO\":\"Aosta\",\"AR\":\"Arezzo\",\"AP\":\"Ascoli Piceno\",\"AT\":\"Asti\",\"AV\":\"Avellino\",\"BA\":\"Bari\",\"BT\":\"Barletta-Andria-Trani\",\"BL\":\"Belluno\",\"BN\":\"Benevento\",\"BG\":\"Bergamo\",\"BI\":\"Biella\",\"BO\":\"Bologna\",\"BZ\":\"Bolzano\",\"BS\":\"Brescia\",\"BR\":\"Brindisi\",\"CA\":\"Cagliari\",\"CL\":\"Caltanissetta\",\"CB\":\"Campobasso\",\"CE\":\"Caserta\",\"CT\":\"Catania\",\"CZ\":\"Catanzaro\",\"CH\":\"Chieti\",\"CO\":\"Como\",\"CS\":\"Cosenza\",\"CR\":\"Cremona\",\"KR\":\"Crotone\",\"CN\":\"Cuneo\",\"EN\":\"Enna\",\"FM\":\"Fermo\",\"FE\":\"Ferrara\",\"FI\":\"Firenze\",\"FG\":\"Foggia\",\"FC\":\"Forl\\u00ec-Cesena\",\"FR\":\"Frosinone\",\"GE\":\"Genova\",\"GO\":\"Gorizia\",\"GR\":\"Grosseto\",\"IM\":\"Imperia\",\"IS\":\"Isernia\",\"SP\":\"La Spezia\",\"AQ\":\"\\u00c1quila\",\"LT\":\"Latina\",\"LE\":\"Lecce\",\"LC\":\"Lecco\",\"LI\":\"Livorno\",\"LO\":\"Lodi\",\"LU\":\"Lucca\",\"MC\":\"Macerata\",\"MN\":\"Mantova\",\"MS\":\"Massa-Carrara\",\"MT\":\"Matera\",\"ME\":\"Messina\",\"MI\":\"Milano\",\"MO\":\"Modena\",\"MB\":\"Monza e della Brianza\",\"NA\":\"Napoli\",\"NO\":\"Novara\",\"NU\":\"Nuoro\",\"OR\":\"Oristano\",\"PD\":\"Padova\",\"PA\":\"Palermo\",\"PR\":\"Parma\",\"PV\":\"Pavia\",\"PG\":\"Perugia\",\"PU\":\"Pesaro e Urbino\",\"PE\":\"Pescara\",\"PC\":\"Piacenza\",\"PI\":\"Pisa\",\"PT\":\"Pistoia\",\"PN\":\"Pordenone\",\"PZ\":\"Potenza\",\"PO\":\"Prato\",\"RG\":\"Ragusa\",\"RA\":\"Ravenna\",\"RC\":\"Reggio Calabria\",\"RE\":\"Reggio Emilia\",\"RI\":\"Rieti\",\"RN\":\"Rimini\",\"RM\":\"Roma\",\"RO\":\"Rovigo\",\"SA\":\"Salerno\",\"SS\":\"Sassari\",\"SV\":\"Savona\",\"SI\":\"Siena\",\"SR\":\"Siracusa\",\"SO\":\"Sondrio\",\"SU\":\"Sud Sardegna\",\"TA\":\"Taranto\",\"TE\":\"Teramo\",\"TR\":\"Terni\",\"TO\":\"Torino\",\"TP\":\"Trapani\",\"TN\":\"Trento\",\"TV\":\"Treviso\",\"TS\":\"Trieste\",\"UD\":\"Udine\",\"VA\":\"Varese\",\"VE\":\"Venezia\",\"VB\":\"Verbano-Cusio-Ossola\",\"VC\":\"Vercelli\",\"VR\":\"Verona\",\"VV\":\"Vibo Valentia\",\"VI\":\"Vicenza\",\"VT\":\"Viterbo\"},\"IL\":[],\"IM\":[],\"JM\":{\"JM-01\":\"Kingston\",\"JM-02\":\"Saint Andrew\",\"JM-03\":\"Saint Thomas\",\"JM-04\":\"Portland\",\"JM-05\":\"Saint Mary\",\"JM-06\":\"Saint Ann\",\"JM-07\":\"Trelawny\",\"JM-08\":\"Saint James\",\"JM-09\":\"Hanover\",\"JM-10\":\"Westmoreland\",\"JM-11\":\"Saint Elizabeth\",\"JM-12\":\"Manchester\",\"JM-13\":\"Clarendon\",\"JM-14\":\"Saint Catherine\"},\"JP\":{\"JP01\":\"Hokkaido\",\"JP02\":\"Aomori\",\"JP03\":\"Iwate\",\"JP04\":\"Miyagi\",\"JP05\":\"Akita\",\"JP06\":\"Yamagata\",\"JP07\":\"Fukushima\",\"JP08\":\"Ibaraki\",\"JP09\":\"Tochigi\",\"JP10\":\"Gunma\",\"JP11\":\"Saitama\",\"JP12\":\"Chiba\",\"JP13\":\"Tokyo\",\"JP14\":\"Kanagawa\",\"JP15\":\"Niigata\",\"JP16\":\"Toyama\",\"JP17\":\"Ishikawa\",\"JP18\":\"Fukui\",\"JP19\":\"Yamanashi\",\"JP20\":\"Nagano\",\"JP21\":\"Gifu\",\"JP22\":\"Shizuoka\",\"JP23\":\"Aichi\",\"JP24\":\"Mie\",\"JP25\":\"Shiga\",\"JP26\":\"Kyoto\",\"JP27\":\"Osaka\",\"JP28\":\"Hyogo\",\"JP29\":\"Nara\",\"JP30\":\"Wakayama\",\"JP31\":\"Tottori\",\"JP32\":\"Shimane\",\"JP33\":\"Okayama\",\"JP34\":\"Hiroshima\",\"JP35\":\"Yamaguchi\",\"JP36\":\"Tokushima\",\"JP37\":\"Kagawa\",\"JP38\":\"Ehime\",\"JP39\":\"Kochi\",\"JP40\":\"Fukuoka\",\"JP41\":\"Saga\",\"JP42\":\"Nagasaki\",\"JP43\":\"Kumamoto\",\"JP44\":\"Oita\",\"JP45\":\"Miyazaki\",\"JP46\":\"Kagoshima\",\"JP47\":\"Okinawa\"},\"KE\":{\"KE01\":\"Baringo\",\"KE02\":\"Bomet\",\"KE03\":\"Bungoma\",\"KE04\":\"Busia\",\"KE05\":\"Elgeyo-Marakwet\",\"KE06\":\"Embu\",\"KE07\":\"Garissa\",\"KE08\":\"Homa Bay\",\"KE09\":\"Isiolo\",\"KE10\":\"Kajiado\",\"KE11\":\"Kakamega\",\"KE12\":\"Kericho\",\"KE13\":\"Kiambu\",\"KE14\":\"Kilifi\",\"KE15\":\"Kirinyaga\",\"KE16\":\"Kisii\",\"KE17\":\"Kisumu\",\"KE18\":\"Kitui\",\"KE19\":\"Kwale\",\"KE20\":\"Laikipia\",\"KE21\":\"Lamu\",\"KE22\":\"Machakos\",\"KE23\":\"Makueni\",\"KE24\":\"Mandera\",\"KE25\":\"Marsabit\",\"KE26\":\"Meru\",\"KE27\":\"Migori\",\"KE28\":\"Mombasa\",\"KE29\":\"Murang\\u2019a\",\"KE30\":\"Nairobi County\",\"KE31\":\"Nakuru\",\"KE32\":\"Nandi\",\"KE33\":\"Narok\",\"KE34\":\"Nyamira\",\"KE35\":\"Nyandarua\",\"KE36\":\"Nyeri\",\"KE37\":\"Samburu\",\"KE38\":\"Siaya\",\"KE39\":\"Taita-Taveta\",\"KE40\":\"Tana River\",\"KE41\":\"Tharaka-Nithi\",\"KE42\":\"Trans Nzoia\",\"KE43\":\"Turkana\",\"KE44\":\"Uasin Gishu\",\"KE45\":\"Vihiga\",\"KE46\":\"Wajir\",\"KE47\":\"West Pokot\"},\"KN\":{\"KNK\":\"S\\u00e3o Crist\\u00f3v\\u00e3o\",\"KNN\":\"N\\u00e3o\",\"KN01\":\"Igreja de Cristo Nichola Town\",\"KN02\":\"Santa Ana Sandy Point\",\"KN03\":\"S\\u00e3o Jorge Basseterre\",\"KN04\":\"S\\u00e3o Jorge Gingerland\",\"KN05\":\"S\\u00e3o Tiago Barlavento\",\"KN06\":\"S\\u00e3o Jo\\u00e3o Capisterra\",\"KN07\":\"S\\u00e3o Jo\\u00e3o Figueira\",\"KN08\":\"Santa Maria Cayon\",\"KN09\":\"S\\u00e3o Paulo Capisterre\",\"KN10\":\"S\\u00e3o Paulo Charlestown\",\"KN11\":\"S\\u00e3o Pedro Basseterre\",\"KN12\":\"Plan\\u00edcie de S\\u00e3o Tom\\u00e1s\",\"KN13\":\"Ilha M\\u00e9dia de S\\u00e3o Tom\\u00e1s\",\"KN15\":\"Trinity Palmetto Point\"},\"KR\":[],\"KW\":[],\"LA\":{\"AT\":\"Attapeu\",\"BK\":\"Bokeo\",\"BL\":\"Bolikhamsai\",\"CH\":\"Champasak\",\"HO\":\"Houaphanh\",\"KH\":\"Khammouane\",\"LM\":\"Luang Namtha\",\"LP\":\"Luang Prabang\",\"OU\":\"Oudomxay\",\"PH\":\"Phongsaly\",\"SL\":\"Salavan\",\"SV\":\"Savannakhet\",\"VI\":\"Vientiane Province\",\"VT\":\"Vientiane\",\"XA\":\"Sainyabuli\",\"XE\":\"Sekong\",\"XI\":\"Xiangkhouang\",\"XS\":\"Xaisomboun\"},\"LB\":[],\"LI\":[],\"LR\":{\"BM\":\"Bomi\",\"BN\":\"Bong\",\"GA\":\"Gbarpolu\",\"GB\":\"Grand Bassa\",\"GC\":\"Grand Cape Mount\",\"GG\":\"Grand Gedeh\",\"GK\":\"Grand Kru\",\"LO\":\"Lofa\",\"MA\":\"Margibi\",\"MY\":\"Maryland\",\"MO\":\"Montserrado\",\"NM\":\"Nimba\",\"RV\":\"Rivercess\",\"RG\":\"River Gee\",\"SN\":\"Sinoe\"},\"LU\":[],\"MA\":{\"maagd\":\"Agadir-ida ou tanane\",\"maazi\":\"Azilal\",\"mabem\":\"B\\u00e9ni-mellal\",\"maber\":\"Berkane\",\"mabes\":\"Ben slimane\",\"mabod\":\"Boujdour\",\"mabom\":\"Boulemane\",\"mabrr\":\"Berrechid\",\"macas\":\"Casablanca\",\"mache\":\"Chefchaouen\",\"machi\":\"Chichaoua\",\"macht\":\"Chtouka a\\u00eft baha\",\"madri\":\"Driouch\",\"maedi\":\"Esseouira\",\"maerr\":\"Errachidia\",\"mafah\":\"Fahs-beni makada\",\"mafes\":\"F\\u00e8s-dar-dbibegh\",\"mafig\":\"Figue\",\"mafqh\":\"Fquih ben salah\",\"mague\":\"Guelmim\",\"maguf\":\"Guercif\",\"mahaj\":\"El hajeb\",\"mahao\":\"Al haouz\",\"mahoc\":\"Al hoce\\u00efma\",\"maifr\":\"Ifrane\",\"maine\":\"Inezgane-a\\u00eft melloul\",\"majdi\":\"El jadida\",\"majra\":\"Jerada\",\"maken\":\"K\\u00e9nitra\",\"makes\":\"Kelaat sraghna\",\"makhe\":\"Khemisset\",\"makhn\":\"Kh\\u00e9nifra\",\"makho\":\"Khouribga\",\"malaa\":\"La\\u00e2youne\",\"malar\":\"Larache\",\"mamar\":\"Marrakech\",\"mamdf\":\"M'diq-fnideq\",\"mamed\":\"M\\u00e9diouna\",\"mamek\":\"Mekn\\u00e8s\",\"mamid\":\"Midelt\",\"mammd\":\"Marrakech-medina\",\"mammn\":\"Marrakech-menara\",\"mamoh\":\"Mohammedia\",\"mamou\":\"Moulay yacoub\",\"manad\":\"Nador\",\"manou\":\"Nouaceur\",\"maoua\":\"Ouarzazate\",\"maoud\":\"Oued ed-dahab\",\"maouj\":\"Oujda-angad\",\"maouz\":\"Ouezzane\",\"marab\":\"Rabat\",\"mareh\":\"Rehamna\",\"masaf\":\"Safi\",\"masal\":\"Oferta\",\"masef\":\"Sefrou\",\"maset\":\"Settat\",\"masib\":\"Sidi bennour\",\"masif\":\"Sidi ifni\",\"masik\":\"Sidi kacem\",\"masil\":\"Sidi slimane\",\"maskh\":\"Skhirat-t\\u00e9mara\",\"masyb\":\"Sidi youssef ben ali\",\"mataf\":\"Tarfaya (eh-parcial)\",\"matai\":\"Taourirt\",\"matao\":\"Taounate\",\"matar\":\"Taroudant\",\"matat\":\"Tata\",\"mataz\":\"Taza\",\"matet\":\"T\\u00e9touan\",\"matin\":\"Tinghir\",\"matiz\":\"Tiznit\",\"matng\":\"T\\u00e2nger-assilah\",\"matnt\":\"Tan-tan\",\"mayus\":\"Yssoufia\",\"mazag\":\"Zagora\"},\"MD\":{\"C\":\"Chi\\u0219in\\u0103u\",\"BL\":\"B\\u0103l\\u021bi\",\"AN\":\"Anenii Noi\",\"BS\":\"Basarabeasca\",\"BR\":\"Briceni\",\"CH\":\"Cahul\",\"CT\":\"Cantemir\",\"CL\":\"C\\u0103l\\u0103ra\\u0219i\",\"CS\":\"C\\u0103u\\u0219eni\",\"CM\":\"Cimi\\u0219lia\",\"CR\":\"Criuleni\",\"DN\":\"Dondu\\u0219eni\",\"DR\":\"Drochia\",\"DB\":\"Dub\\u0103sari\",\"ED\":\"Edine\\u021b\",\"FL\":\"F\\u0103le\\u0219ti\",\"FR\":\"Flore\\u0219ti\",\"GE\":\"UTA G\\u0103g\\u0103uzia\",\"GL\":\"Glodeni\",\"HN\":\"H\\u00eence\\u0219ti\",\"IL\":\"Ialoveni\",\"LV\":\"Leova\",\"NS\":\"Nisporeni\",\"OC\":\"Ocni\\u021ba\",\"OR\":\"Orhei\",\"RZ\":\"Rezina\",\"RS\":\"R\\u00ee\\u0219cani\",\"SG\":\"S\\u00eengerei\",\"SR\":\"Soroca\",\"ST\":\"Str\\u0103\\u0219eni\",\"SD\":\"\\u0218old\\u0103ne\\u0219ti\",\"SV\":\"\\u0218tefan Vod\\u0103\",\"TR\":\"Taraclia\",\"TL\":\"Telene\\u0219ti\",\"UN\":\"Ungheni\"},\"MF\":[],\"MQ\":[],\"MT\":[],\"MX\":{\"DF\":\"Cidade do M\\u00e9xico\",\"JA\":\"Jalisco\",\"NL\":\"Nuevo Le\\u00f3n\",\"AG\":\"Aguascalientes\",\"BC\":\"Baja California\",\"BS\":\"Baja California Sur\",\"CM\":\"Campeche\",\"CS\":\"Chiapas\",\"CH\":\"Chihuahua\",\"CO\":\"Coahuila\",\"CL\":\"Colima\",\"DG\":\"Durango\",\"GT\":\"Guanajuato\",\"GR\":\"Guerrero\",\"HG\":\"Hidalgo\",\"MX\":\"Estado de M\\u00e9xico\",\"MI\":\"Michoac\\u00e1n\",\"MO\":\"Morelos\",\"NA\":\"Nayarit\",\"OA\":\"Oaxaca\",\"PU\":\"Puebla\",\"QT\":\"Quer\\u00e9taro\",\"QR\":\"Quintana Roo\",\"SL\":\"San Luis Potos\\u00ed\",\"SI\":\"Sinaloa\",\"SO\":\"Sonora\",\"TB\":\"Tabasco\",\"TM\":\"Tamaulipas\",\"TL\":\"Tlaxcala\",\"VE\":\"Veracruz\",\"YU\":\"Yucat\\u00e1n\",\"ZA\":\"Zacatecas\"},\"MY\":{\"JHR\":\"Johor\",\"KDH\":\"Kedah\",\"KTN\":\"Kelantan\",\"LBN\":\"Labuan\",\"MLK\":\"Malaca\",\"NSN\":\"Negeri Sembilan\",\"PHG\":\"Pahang\",\"PNG\":\"Penang (Pulau Pinang)\",\"PRK\":\"Perak\",\"PLS\":\"Perlis\",\"SBH\":\"Sabah\",\"SWK\":\"Sarawak\",\"SGR\":\"Selangor\",\"TRG\":\"Terengganu\",\"PJY\":\"Putrajaya\",\"KUL\":\"Kuala Lumpur\"},\"MZ\":{\"MZP\":\"Cabo Delgado\",\"MZG\":\"Gaza\",\"MZI\":\"Inhambane\",\"MZB\":\"Manica\",\"MZL\":\"Maputo Province\",\"MZMPM\":\"Maputo\",\"MZN\":\"Nampula\",\"MZA\":\"Niassa\",\"MZS\":\"Sofala\",\"MZT\":\"Tete\",\"MZQ\":\"Zamb\\u00e9zia\"},\"NA\":{\"ER\":\"Erongo\",\"HA\":\"Hardap\",\"KA\":\"Karas\",\"KE\":\"Kavango East\",\"KW\":\"Kavango West\",\"KH\":\"Khomas\",\"KU\":\"Kunene\",\"OW\":\"Ohangwena\",\"OH\":\"Omaheke\",\"OS\":\"Omusati\",\"ON\":\"Oshana\",\"OT\":\"Oshikoto\",\"OD\":\"Otjozondjupa\",\"CA\":\"Zambezi\"},\"NG\":{\"AB\":\"Abia\",\"FC\":\"Abuja\",\"AD\":\"Adamawa\",\"AK\":\"Akwa Ibom\",\"AN\":\"Anambra\",\"BA\":\"Bauchi\",\"BY\":\"Bayelsa\",\"BE\":\"Benue\",\"BO\":\"Borno\",\"CR\":\"Cross River\",\"DE\":\"Delta\",\"EB\":\"Ebonyi\",\"ED\":\"Edo\",\"EK\":\"Ekiti\",\"EN\":\"Enugu\",\"GO\":\"Gombe\",\"IM\":\"Imo\",\"JI\":\"Jigawa\",\"KD\":\"Kaduna\",\"KN\":\"Kano\",\"KT\":\"Katsina\",\"KE\":\"Kebbi\",\"KO\":\"Kogi\",\"KW\":\"Kwara\",\"LA\":\"Lagos\",\"NA\":\"Nasarawa\",\"NI\":\"N\\u00edger\",\"OG\":\"Ogun\",\"ON\":\"Ondo\",\"OS\":\"Osun\",\"OY\":\"Oyo\",\"PL\":\"Plateau\",\"RI\":\"Rivers\",\"SO\":\"Sokoto\",\"TA\":\"Taraba\",\"YO\":\"Yobe\",\"ZA\":\"Zamfara\"},\"NL\":[],\"NO\":[],\"NP\":{\"BAG\":\"Bagmati\",\"BHE\":\"Bheri\",\"DHA\":\"Dhaulagiri\",\"GAN\":\"Gandaki\",\"JAN\":\"Janakpur\",\"KAR\":\"Karnali\",\"KOS\":\"Koshi\",\"LUM\":\"Lumbini\",\"MAH\":\"Mahakali\",\"MEC\":\"Mechi\",\"NAR\":\"Narayani\",\"RAP\":\"Rapti\",\"SAG\":\"Sagarmatha\",\"SET\":\"Seti\"},\"NI\":{\"NI-AN\":\"Atl\\u00e1ntico Norte\",\"NI-AS\":\"Atl\\u00e1ntico Sur\",\"NI-BO\":\"Boaco\",\"NI-CA\":\"Carazo\",\"NI-CI\":\"Chinandega\",\"NI-CO\":\"Chontales\",\"NI-ES\":\"Estel\\u00ed\",\"NI-GR\":\"Granada\",\"NI-JI\":\"Jinotega\",\"NI-LE\":\"Le\\u00f3n\",\"NI-MD\":\"Madriz\",\"NI-MN\":\"Managua\",\"NI-MS\":\"Masaya\",\"NI-MT\":\"Matagalpa\",\"NI-NS\":\"Nueva Segovia\",\"NI-RI\":\"Rivas\",\"NI-SJ\":\"R\\u00edo San Juan\"},\"NZ\":{\"NTL\":\"Northland\",\"AUK\":\"Auckland\",\"WKO\":\"Waikato\",\"BOP\":\"Bay of Plenty\",\"TKI\":\"Taranaki\",\"GIS\":\"Gisborne\",\"HKB\":\"Hawke\\u2019s Bay\",\"MWT\":\"Manawatu-Whanganui\",\"WGN\":\"Wellington\",\"NSN\":\"Nelson\",\"MBH\":\"Marlborough\",\"TAS\":\"Tasman\",\"WTC\":\"Costa Oeste dos Estados Unidos\",\"CAN\":\"Canterbury\",\"OTA\":\"Otago\",\"STL\":\"Southland\"},\"PA\":{\"PA-1\":\"Bocas del Toro\",\"PA-2\":\"Cocl\\u00e9\",\"PA-3\":\"Col\\u00f3n\",\"PA-4\":\"Chiriqu\\u00ed\",\"PA-5\":\"Dari\\u00e9n\",\"PA-6\":\"Herrera\",\"PA-7\":\"Los Santos\",\"PA-8\":\"Panam\\u00e1\",\"PA-9\":\"Veraguas\",\"PA-10\":\"Panam\\u00e1 Oeste\",\"PA-EM\":\"Ember\\u00e1\",\"PA-KY\":\"Guna Yala\",\"PA-NB\":\"Ng\\u00f6be-Bugl\\u00e9\"},\"PE\":{\"CAL\":\"El Callao\",\"LMA\":\"Municipalidade Metropolitana de Lima\",\"AMA\":\"Amazonas\",\"ANC\":\"Ancash\",\"APU\":\"Apur\\u00edmac\",\"ARE\":\"Arequipa\",\"AYA\":\"Ayacucho\",\"CAJ\":\"Cajamarca\",\"CUS\":\"Cusco\",\"HUV\":\"Huancavelica\",\"HUC\":\"Hu\\u00e1nuco\",\"ICA\":\"Ica\",\"JUN\":\"Jun\\u00edn\",\"LAL\":\"La Libertad\",\"LAM\":\"Lambayeque\",\"LIM\":\"Lima\",\"LOR\":\"Loreto\",\"MDD\":\"Madre de Dios\",\"MOQ\":\"Moquegua\",\"PAS\":\"Pasco\",\"PIU\":\"Piura\",\"PUN\":\"Puno\",\"SAM\":\"San Mart\\u00edn\",\"TAC\":\"Tacna\",\"TUM\":\"Tumbes\",\"UCA\":\"Ucayali\"},\"PH\":{\"ABR\":\"Abra\",\"AGN\":\"Agusan del Norte\",\"AGS\":\"Agusan del Sur\",\"AKL\":\"Aklan\",\"ALB\":\"Albay\",\"ANT\":\"Antique\",\"APA\":\"Apayao\",\"AUR\":\"Aurora\",\"BAS\":\"Basilan\",\"BAN\":\"Bataan\",\"BTN\":\"Batanes\",\"BTG\":\"Batangas\",\"BEN\":\"Benguet\",\"BIL\":\"Biliran\",\"BOH\":\"Bohol\",\"BUK\":\"Bukidnon\",\"BUL\":\"Bulacan\",\"CAG\":\"Cagayan\",\"CAN\":\"Camarines Norte\",\"CAS\":\"Camarines Sur\",\"CAM\":\"Camiguin\",\"CAP\":\"Capiz\",\"CAT\":\"Catanduanes\",\"CAV\":\"Cavite\",\"CEB\":\"Cebu\",\"COM\":\"Compostela Valley\",\"NCO\":\"Cotabato\",\"DAV\":\"Davao del Norte\",\"DAS\":\"Davao del Sur\",\"DAC\":\"Davao Occidental\",\"DAO\":\"Davao Oriental\",\"DIN\":\"Dinagat Islands\",\"EAS\":\"Eastern Samar\",\"GUI\":\"Guimaras\",\"IFU\":\"Ifugao\",\"ILN\":\"Ilocos Norte\",\"ILS\":\"Ilocos Sur\",\"ILI\":\"Iloilo\",\"ISA\":\"Isabela\",\"KAL\":\"Kalinga\",\"LUN\":\"La Union\",\"LAG\":\"Laguna\",\"LAN\":\"Lanao del Norte\",\"LAS\":\"Lanao del Sur\",\"LEY\":\"Leyte\",\"MAG\":\"Maguindanao\",\"MAD\":\"Marinduque\",\"MAS\":\"Masbate\",\"MSC\":\"Misamis Occidental\",\"MSR\":\"Misamis Oriental\",\"MOU\":\"Mountain Province\",\"NEC\":\"Negros Occidental\",\"NER\":\"Negros Oriental\",\"NSA\":\"Northern Samar\",\"NUE\":\"Nueva Ecija\",\"NUV\":\"Nueva Vizcaya\",\"MDC\":\"Occidental Mindoro\",\"MDR\":\"Oriental Mindoro\",\"PLW\":\"Palawan\",\"PAM\":\"Pampanga\",\"PAN\":\"Pangasinan\",\"QUE\":\"Quezon\",\"QUI\":\"Quirino\",\"RIZ\":\"Rizal\",\"ROM\":\"Romblon\",\"WSA\":\"Samar\",\"SAR\":\"Sarangani\",\"SIQ\":\"Siquijor\",\"SOR\":\"Sorsogon\",\"SCO\":\"South Cotabato\",\"SLE\":\"Southern Leyte\",\"SUK\":\"Sultan Kudarat\",\"SLU\":\"Sulu\",\"SUN\":\"Surigao del Norte\",\"SUR\":\"Surigao del Sur\",\"TAR\":\"Tarlac\",\"TAW\":\"Tawi-Tawi\",\"ZMB\":\"Zambales\",\"ZAN\":\"Zamboanga del Norte\",\"ZAS\":\"Zamboanga del Sur\",\"ZSI\":\"Zamboanga Sibugay\",\"00\":\"Metro Manila\"},\"PK\":{\"JK\":\"Caxemira Livre\",\"BA\":\"Baluchist\\u00e3o\",\"TA\":\"Territ\\u00f3rio Federal das \\u00c1reas Tribais (FATA)\",\"GB\":\"Gilgit-Baltist\\u00e3o\",\"IS\":\"Territ\\u00f3rio da Capital Islamabad\",\"KP\":\"Khyber Pakhtunkhwa\",\"PB\":\"Punjab\",\"SD\":\"Sind\"},\"PL\":[],\"PR\":[],\"PT\":[],\"PY\":{\"PY-ASU\":\"Asunci\\u00f3n\",\"PY-1\":\"Concepci\\u00f3n\",\"PY-2\":\"San Pedro\",\"PY-3\":\"Cordillera\",\"PY-4\":\"Guair\\u00e1\",\"PY-5\":\"Caaguaz\\u00fa\",\"PY-6\":\"Caazap\\u00e1\",\"PY-7\":\"Itap\\u00faa\",\"PY-8\":\"Misiones\",\"PY-9\":\"Paraguar\\u00ed\",\"PY-10\":\"Alto Paran\\u00e1\",\"PY-11\":\"Central\",\"PY-12\":\"\\u00d1eembuc\\u00fa\",\"PY-13\":\"Amambay\",\"PY-14\":\"Canindey\\u00fa\",\"PY-15\":\"Presidente Hayes\",\"PY-16\":\"Alto Paraguay\",\"PY-17\":\"Boquer\\u00f3n\"},\"RE\":[],\"RO\":{\"AB\":\"Alba\",\"AR\":\"Arad\",\"AG\":\"Arge\\u0219\",\"BC\":\"Bac\\u0103u\",\"BH\":\"Bihor\",\"BN\":\"Bistri\\u021ba-N\\u0103s\\u0103ud\",\"BT\":\"Boto\\u0219ani\",\"BR\":\"Br\\u0103ila\",\"BV\":\"Bra\\u0219ov\",\"B\":\"Bucure\\u0219ti\",\"BZ\":\"Buz\\u0103u\",\"CL\":\"C\\u0103l\\u0103ra\\u0219i\",\"CS\":\"Cara\\u0219-Severin\",\"CJ\":\"Cluj\",\"CT\":\"Constan\\u021ba\",\"CV\":\"Covasna\",\"DB\":\"D\\u00e2mbovi\\u021ba\",\"DJ\":\"Dolj\",\"GL\":\"Gala\\u021bi\",\"GR\":\"Giurgiu\",\"GJ\":\"Gorj\",\"HR\":\"Harghita\",\"HD\":\"Hunedoara\",\"IL\":\"Ialomi\\u021ba\",\"IS\":\"Ia\\u0219i\",\"IF\":\"Ilfov\",\"MM\":\"Maramure\\u0219\",\"MH\":\"Mehedin\\u021bi\",\"MS\":\"Mure\\u0219\",\"NT\":\"Neam\\u021b\",\"OT\":\"Olt\",\"PH\":\"Prahova\",\"SJ\":\"S\\u0103laj\",\"SM\":\"Satu Mare\",\"SB\":\"Sibiu\",\"SV\":\"Suceava\",\"TR\":\"Teleorman\",\"TM\":\"Timi\\u0219\",\"TL\":\"Tulcea\",\"VL\":\"V\\u00e2lcea\",\"VS\":\"Vaslui\",\"VN\":\"Vrancea\"},\"SN\":{\"SNDB\":\"Diourbel\",\"SNDK\":\"Dacar\",\"SNFK\":\"Fatick\",\"SNKA\":\"Kaffrine\",\"SNKD\":\"Kolda\",\"SNKE\":\"Kedougou\",\"SNKL\":\"Kaolack\",\"SNLG\":\"Louga\",\"SNMT\":\"Matam\",\"SNSE\":\"S\\u00e9dhiou\",\"SNSL\":\"S\\u00e3o Lu\\u00eds\",\"SNTC\":\"Tambacounda\",\"SNTH\":\"Thies\",\"SNZG\":\"Ziguinchor\"},\"SG\":[],\"SK\":[],\"SI\":[],\"SV\":{\"SV-AH\":\"Ahuachap\\u00e1n\",\"SV-CA\":\"Caba\\u00f1as\",\"SV-CH\":\"Chalatenango\",\"SV-CU\":\"Cuscatl\\u00e1n\",\"SV-LI\":\"La Libertad\",\"SV-MO\":\"Moraz\\u00e1n\",\"SV-PA\":\"La Paz\",\"SV-SA\":\"Santa Ana\",\"SV-SM\":\"San Miguel\",\"SV-SO\":\"Sonsonate\",\"SV-SS\":\"San Salvador\",\"SV-SV\":\"San Vicente\",\"SV-UN\":\"La Uni\\u00f3n\",\"SV-US\":\"Usulut\\u00e1n\"},\"TH\":{\"TH-37\":\"Amnat Charoen\",\"TH-15\":\"Ang Thong\",\"TH-14\":\"Ayutthaya\",\"TH-10\":\"Bangkok\",\"TH-38\":\"Bueng Kan\",\"TH-31\":\"Buri Ram\",\"TH-24\":\"Chachoengsao\",\"TH-18\":\"Chai Nat\",\"TH-36\":\"Chaiyaphum\",\"TH-22\":\"Chanthaburi\",\"TH-50\":\"Chiang Mai\",\"TH-57\":\"Chiang Rai\",\"TH-20\":\"Chonburi\",\"TH-86\":\"Chumphon\",\"TH-46\":\"Kalasin\",\"TH-62\":\"Kamphaeng Phet\",\"TH-71\":\"Kanchanaburi\",\"TH-40\":\"Khon Kaen\",\"TH-81\":\"Krabi\",\"TH-52\":\"Lampang\",\"TH-51\":\"Lamphun\",\"TH-42\":\"Loei\",\"TH-16\":\"Lopburi\",\"TH-58\":\"Mae Hong Son\",\"TH-44\":\"Maha Sarakham\",\"TH-49\":\"Mukdahan\",\"TH-26\":\"Nakhon Nayok\",\"TH-73\":\"Nakhon Pathom\",\"TH-48\":\"Nakhon Phanom\",\"TH-30\":\"Nakhon Ratchasima\",\"TH-60\":\"Nakhon Sawan\",\"TH-80\":\"Nakhon Si Thammarat\",\"TH-55\":\"Nan\",\"TH-96\":\"Narathiwat\",\"TH-39\":\"Nong Bua Lam Phu\",\"TH-43\":\"Nong Khai\",\"TH-12\":\"Nonthaburi\",\"TH-13\":\"Pathum Thani\",\"TH-94\":\"Pattani\",\"TH-82\":\"Phang Nga\",\"TH-93\":\"Phatthalung\",\"TH-56\":\"Phayao\",\"TH-67\":\"Phetchabun\",\"TH-76\":\"Phetchaburi\",\"TH-66\":\"Phichit\",\"TH-65\":\"Phitsanulok\",\"TH-54\":\"Phrae\",\"TH-83\":\"Phuket\",\"TH-25\":\"Prachin Buri\",\"TH-77\":\"Prachuap Khiri Khan\",\"TH-85\":\"Ranong\",\"TH-70\":\"Ratchaburi\",\"TH-21\":\"Rayong\",\"TH-45\":\"Roi Et\",\"TH-27\":\"Sa Kaeo\",\"TH-47\":\"Sakon Nakhon\",\"TH-11\":\"Samut Prakan\",\"TH-74\":\"Samut Sakhon\",\"TH-75\":\"Samut Songkhram\",\"TH-19\":\"Saraburi\",\"TH-91\":\"Satun\",\"TH-17\":\"Sing Buri\",\"TH-33\":\"Sisaket\",\"TH-90\":\"Songkhla\",\"TH-64\":\"Sukhothai\",\"TH-72\":\"Suphan Buri\",\"TH-84\":\"Surat Thani\",\"TH-32\":\"Surin\",\"TH-63\":\"Tak\",\"TH-92\":\"Trang\",\"TH-23\":\"Trat\",\"TH-34\":\"Ubon Ratchathani\",\"TH-41\":\"Udon Thani\",\"TH-61\":\"Uthai Thani\",\"TH-53\":\"Uttaradit\",\"TH-95\":\"Yala\",\"TH-35\":\"Yasothon\"},\"TR\":{\"TR01\":\"Adana\",\"TR02\":\"Ad\\u0131yaman\",\"TR03\":\"Afyonkarahisar\",\"TR04\":\"A\\u011fr\\u0131\",\"TR05\":\"Amasya\",\"TR06\":\"Ancara\",\"TR07\":\"Ant\\u00e1lia\",\"TR08\":\"Artvin\",\"TR09\":\"Ayd\\u0131n\",\"TR10\":\"Bal\\u0131kesir\",\"TR11\":\"Bilecik\",\"TR12\":\"Bing\\u00f6l\",\"TR13\":\"Bitlis\",\"TR14\":\"Bolu\",\"TR15\":\"Burdur\",\"TR16\":\"Bursa\",\"TR17\":\"\\u00c7anakkale\",\"TR18\":\"\\u00c7ank\\u0131r\\u0131\",\"TR19\":\"\\u00c7orum\",\"TR20\":\"Denizli\",\"TR21\":\"Diyarbak\\u0131r\",\"TR22\":\"Edirne\",\"TR23\":\"Elaz\\u0131\\u011f\",\"TR24\":\"Erzincan\",\"TR25\":\"Erzurum\",\"TR26\":\"Eski\\u015fehir\",\"TR27\":\"Gaziantep\",\"TR28\":\"Giresun\",\"TR29\":\"G\\u00fcm\\u00fc\\u015fhane\",\"TR30\":\"Hakk\\u00e2ri\",\"TR31\":\"Hatay\",\"TR32\":\"Isparta\",\"TR33\":\"\\u0130\\u00e7el\",\"TR34\":\"Istanbul\",\"TR35\":\"\\u0130zmir\",\"TR36\":\"Kars\",\"TR37\":\"Kastamonu\",\"TR38\":\"Kayseri\",\"TR39\":\"K\\u0131rklareli\",\"TR40\":\"K\\u0131r\\u015fehir\",\"TR41\":\"Kocaeli\",\"TR42\":\"Konya\",\"TR43\":\"K\\u00fctahya\",\"TR44\":\"Malatya\",\"TR45\":\"Manisa\",\"TR46\":\"Kahramanmara\\u015f\",\"TR47\":\"Mardin\",\"TR48\":\"Mu\\u011fla\",\"TR49\":\"Mu\\u015f\",\"TR50\":\"Nev\\u015fehir\",\"TR51\":\"Ni\\u011fde\",\"TR52\":\"Ordu\",\"TR53\":\"Rize\",\"TR54\":\"Sakarya\",\"TR55\":\"Samsun\",\"TR56\":\"Siirt\",\"TR57\":\"Sinop\",\"TR58\":\"Sivas\",\"TR59\":\"Tekirda\\u011f\",\"TR60\":\"Tokat\",\"TR61\":\"Trabzon\",\"TR62\":\"Tunceli\",\"TR63\":\"\\u015eanl\\u0131urfa\",\"TR64\":\"U\\u015fak\",\"TR65\":\"Van\",\"TR66\":\"Yozgat\",\"TR67\":\"Zonguldak\",\"TR68\":\"Aksaray\",\"TR69\":\"Bayburt\",\"TR70\":\"Karaman\",\"TR71\":\"K\\u0131r\\u0131kkale\",\"TR72\":\"Batman\",\"TR73\":\"\\u015e\\u0131rnak\",\"TR74\":\"Bart\\u0131n\",\"TR75\":\"Ardahan\",\"TR76\":\"I\\u011fd\\u0131r\",\"TR77\":\"Yalova\",\"TR78\":\"Karab\\u00fck\",\"TR79\":\"Kilis\",\"TR80\":\"Osmaniye\",\"TR81\":\"D\\u00fczce\"},\"TZ\":{\"TZ01\":\"Arusha\",\"TZ02\":\"Dar es Salaam\",\"TZ03\":\"Dodoma\",\"TZ04\":\"Iringa\",\"TZ05\":\"Kagera\",\"TZ06\":\"Pemba North\",\"TZ07\":\"Zanzibar North\",\"TZ08\":\"Kigoma\",\"TZ09\":\"Kilimanjaro\",\"TZ10\":\"Pemba South\",\"TZ11\":\"Zanzibar South\",\"TZ12\":\"Lindi\",\"TZ13\":\"Mara\",\"TZ14\":\"Mbeya\",\"TZ15\":\"Zanzibar West\",\"TZ16\":\"Morogoro\",\"TZ17\":\"Mtwara\",\"TZ18\":\"Mwanza\",\"TZ19\":\"Coast\",\"TZ20\":\"Rukwa\",\"TZ21\":\"Ruvuma\",\"TZ22\":\"Shinyanga\",\"TZ23\":\"Singida\",\"TZ24\":\"Tabora\",\"TZ25\":\"Tanga\",\"TZ26\":\"Manyara\",\"TZ27\":\"Geita\",\"TZ28\":\"Katavi\",\"TZ29\":\"Njombe\",\"TZ30\":\"Simiyu\"},\"LK\":[],\"RS\":{\"RS00\":\"Belgrado\",\"RS14\":\"Bor\",\"RS11\":\"Brani\\u010devo\",\"RS02\":\"Banato Central\",\"RS10\":\"Danube\",\"RS23\":\"Jablanica\",\"RS09\":\"Kolubara\",\"RS08\":\"Ma\\u010dva\",\"RS17\":\"Moravica\",\"RS20\":\"Ni\\u0161ava\",\"RS01\":\"North Ba\\u010dka\",\"RS03\":\"North Banat\",\"RS24\":\"P\\u010dinja\",\"RS22\":\"Pirot\",\"RS13\":\"Pomoravlje\",\"RS19\":\"Rasina\",\"RS18\":\"Ra\\u0161ka\",\"RS06\":\"South Ba\\u010dka\",\"RS04\":\"South Banat\",\"RS07\":\"Srem\",\"RS12\":\"\\u0160umadija\",\"RS21\":\"Toplica\",\"RS05\":\"West Ba\\u010dka\",\"RS15\":\"Zaje\\u010dar\",\"RS16\":\"Zlatibor\",\"RS25\":\"Kosovo\",\"RS26\":\"Pe\\u0107\",\"RS27\":\"Prizren\",\"RS28\":\"Kosovska Mitrovica\",\"RS29\":\"Kosovo-Pomoravlje\",\"RSKM\":\"Kosovo-Metohija\",\"RSVO\":\"Voivodina\"},\"RW\":[],\"SE\":[],\"UA\":{\"UA05\":\"Vinnychchyna\",\"UA07\":\"Volyn\",\"UA09\":\"Luhanshchyna\",\"UA12\":\"Dnipropetrovshchyna\",\"UA14\":\"Donechchyna\",\"UA18\":\"Zhytomyrshchyna\",\"UA21\":\"Zakarpattia\",\"UA23\":\"Zaporizhzhya\",\"UA26\":\"Prykarpattia\",\"UA30\":\"Kyiv\",\"UA32\":\"Kyivshchyna\",\"UA35\":\"Kirovohradschyna\",\"UA40\":\"Sebastopol\",\"UA43\":\"Crimeia\",\"UA46\":\"Lvivshchyna\",\"UA48\":\"Mykolayivschyna\",\"UA51\":\"Odeshchyna\",\"UA53\":\"Poltavshchyna\",\"UA56\":\"Rivnenshchyna\",\"UA59\":\"Sumshchyna\",\"UA61\":\"Ternopilshchyna\",\"UA63\":\"Kharkivshchyna\",\"UA65\":\"Khersonshchyna\",\"UA68\":\"Khmelnychchyna\",\"UA71\":\"Cherkashchyna\",\"UA74\":\"Chernihivshchyna\",\"UA77\":\"Chernivtsi Oblast\"},\"UG\":{\"UG314\":\"Abim\",\"UG301\":\"Adjumani\",\"UG322\":\"Agago\",\"UG323\":\"Alebtong\",\"UG315\":\"Amolatar\",\"UG324\":\"Amudat\",\"UG216\":\"Amuria\",\"UG316\":\"Amuru\",\"UG302\":\"Apac\",\"UG303\":\"Arua\",\"UG217\":\"Budaka\",\"UG218\":\"Bududa\",\"UG201\":\"Bugiri\",\"UG235\":\"Bugweri\",\"UG420\":\"Buhweju\",\"UG117\":\"Buikwe\",\"UG219\":\"Bukedea\",\"UG118\":\"Bukomansimbi\",\"UG220\":\"Bukwa\",\"UG225\":\"Bulambuli\",\"UG416\":\"Buliisa\",\"UG401\":\"Bundibugyo\",\"UG430\":\"Bunyangabu\",\"UG402\":\"Bushenyi\",\"UG202\":\"Busia\",\"UG221\":\"Butaleja\",\"UG119\":\"Butambala\",\"UG233\":\"Butebo\",\"UG120\":\"Buvuma\",\"UG226\":\"Buyende\",\"UG317\":\"Dokolo\",\"UG121\":\"Gomba\",\"UG304\":\"Gulu\",\"UG403\":\"Hoima\",\"UG417\":\"Ibanda\",\"UG203\":\"Iganga\",\"UG418\":\"Isingiro\",\"UG204\":\"Jinja\",\"UG318\":\"Kaabong\",\"UG404\":\"Kabale\",\"UG405\":\"Kabarole\",\"UG213\":\"Kaberamaido\",\"UG427\":\"Kagadi\",\"UG428\":\"Kakumiro\",\"UG101\":\"Kalangala\",\"UG222\":\"Kaliro\",\"UG122\":\"Kalungu\",\"UG102\":\"Kampala\",\"UG205\":\"Kamuli\",\"UG413\":\"Kamwenge\",\"UG414\":\"Kanungu\",\"UG206\":\"Kapchorwa\",\"UG236\":\"Kapelebyong\",\"UG126\":\"Kasanda\",\"UG406\":\"Kasese\",\"UG207\":\"Katakwi\",\"UG112\":\"Kayunga\",\"UG407\":\"Kibaale\",\"UG103\":\"Kiboga\",\"UG227\":\"Kibuku\",\"UG432\":\"Kikuube\",\"UG419\":\"Kiruhura\",\"UG421\":\"Kiryandongo\",\"UG408\":\"Kisoro\",\"UG305\":\"Kitgum\",\"UG319\":\"Koboko\",\"UG325\":\"Kole\",\"UG306\":\"Kotido\",\"UG208\":\"Kumi\",\"UG333\":\"Kwania\",\"UG228\":\"Kween\",\"UG123\":\"Kyankwanzi\",\"UG422\":\"Kyegegwa\",\"UG415\":\"Kyenjojo\",\"UG125\":\"Kyotera\",\"UG326\":\"Lamwo\",\"UG307\":\"Lira\",\"UG229\":\"Luuka\",\"UG104\":\"Luwero\",\"UG124\":\"Lwengo\",\"UG114\":\"Lyantonde\",\"UG223\":\"Manafwa\",\"UG320\":\"Maracha\",\"UG105\":\"Masaka\",\"UG409\":\"Masindi\",\"UG214\":\"Mayuge\",\"UG209\":\"Mbale\",\"UG410\":\"Mbarara\",\"UG423\":\"Mitooma\",\"UG115\":\"Mityana\",\"UG308\":\"Moroto\",\"UG309\":\"Moyo\",\"UG106\":\"Mpigi\",\"UG107\":\"Mubende\",\"UG108\":\"Mukono\",\"UG334\":\"Nabilatuk\",\"UG311\":\"Nakapiripirit\",\"UG116\":\"Nakaseke\",\"UG109\":\"Nakasongola\",\"UG230\":\"Namayingo\",\"UG234\":\"Namisindwa\",\"UG224\":\"Namutumba\",\"UG327\":\"Napak\",\"UG310\":\"Nebbi\",\"UG231\":\"Ngora\",\"UG424\":\"Ntoroko\",\"UG411\":\"Ntungamo\",\"UG328\":\"Nwoya\",\"UG331\":\"Omoro\",\"UG329\":\"Otuke\",\"UG321\":\"Oyam\",\"UG312\":\"Pader\",\"UG332\":\"Pakwach\",\"UG210\":\"Pallisa\",\"UG110\":\"Rakai\",\"UG429\":\"Rubanda\",\"UG425\":\"Rubirizi\",\"UG431\":\"Rukiga\",\"UG412\":\"Rukungiri\",\"UG111\":\"Sembabule\",\"UG232\":\"Serere\",\"UG426\":\"Sheema\",\"UG215\":\"Sironko\",\"UG211\":\"Soroti\",\"UG212\":\"Tororo\",\"UG113\":\"Wakiso\",\"UG313\":\"Yumbe\",\"UG330\":\"Zombo\"},\"UM\":{\"81\":\"Ilha Baker\",\"84\":\"Ilha Howland\",\"86\":\"Ilha Jarvis\",\"67\":\"Atol Johnston\",\"89\":\"Recife Kingman\",\"71\":\"Atol Midway\",\"76\":\"Ilha Navassa\",\"95\":\"Atol Palmyra\",\"79\":\"Ilha Wake\"},\"US\":{\"AL\":\"Alabama\",\"AK\":\"Alasca\",\"AZ\":\"Arizona\",\"AR\":\"Arkansas\",\"CA\":\"Calif\\u00f3rnia\",\"CO\":\"Colorado\",\"CT\":\"Connecticut\",\"DE\":\"Delaware\",\"DC\":\"Distrito de Columbia\",\"FL\":\"Fl\\u00f3rida\",\"GA\":\"Ge\\u00f3rgia\",\"HI\":\"Hava\\u00ed\",\"ID\":\"Idaho\",\"IL\":\"Illinois\",\"IN\":\"Indiana\",\"IA\":\"Iowa\",\"KS\":\"Kansas\",\"KY\":\"Kentucky\",\"LA\":\"Louisiana\",\"ME\":\"Maine\",\"MD\":\"Maryland\",\"MA\":\"Massachusetts\",\"MI\":\"Michigan\",\"MN\":\"Minnesota\",\"MS\":\"Mississippi\",\"MO\":\"Missouri\",\"MT\":\"Montana\",\"NE\":\"Nebraska\",\"NV\":\"Nevada\",\"NH\":\"Nova Hampshire\",\"NJ\":\"Nova Jersey\",\"NM\":\"Novo M\\u00e9xico\",\"NY\":\"Nova Iorque\",\"NC\":\"Carolina do Norte\",\"ND\":\"Dakota do Norte\",\"OH\":\"Ohio\",\"OK\":\"Oklahoma\",\"OR\":\"Oregon\",\"PA\":\"Pensilv\\u00e2nia\",\"RI\":\"Rhode Island\",\"SC\":\"Carolina do Sul\",\"SD\":\"Dakota do Sul\",\"TN\":\"Tennessee\",\"TX\":\"Texas\",\"UT\":\"Utah\",\"VT\":\"Vermont\",\"VA\":\"Virg\\u00ednia\",\"WA\":\"Washington\",\"WV\":\"Virg\\u00ednia Ocidental\",\"WI\":\"Wisconsin\",\"WY\":\"Wyoming\",\"AA\":\"For\\u00e7as Armadas (AA)\",\"AE\":\"For\\u00e7as Armadas (AE)\",\"AP\":\"For\\u00e7as Armadas (AP)\"},\"UY\":{\"UY-AR\":\"Artigas\",\"UY-CA\":\"Canelones\",\"UY-CL\":\"Cerro Largo\",\"UY-CO\":\"Colonia\",\"UY-DU\":\"Durazno\",\"UY-FS\":\"Flores\",\"UY-FD\":\"Fl\\u00f3rida\",\"UY-LA\":\"Lavalleja\",\"UY-MA\":\"Maldonado\",\"UY-MO\":\"Montevid\\u00e9u\",\"UY-PA\":\"Paysand\\u00fa\",\"UY-RN\":\"R\\u00edo Negro\",\"UY-RV\":\"Rivera\",\"UY-RO\":\"Rocha\",\"UY-SA\":\"Salto\",\"UY-SJ\":\"San Jos\\u00e9\",\"UY-SO\":\"Soriano\",\"UY-TA\":\"Tacuaremb\\u00f3\",\"UY-TT\":\"Treinta y Tres\"},\"VE\":{\"VE-A\":\"Capital\",\"VE-B\":\"Anzo\\u00e1tegui\",\"VE-C\":\"Apure\",\"VE-D\":\"Aragua\",\"VE-E\":\"Barinas\",\"VE-F\":\"Bol\\u00edvar\",\"VE-G\":\"Carabobo\",\"VE-H\":\"Cojedes\",\"VE-I\":\"Falc\\u00f3n\",\"VE-J\":\"Gu\\u00e1rico\",\"VE-K\":\"Lara\",\"VE-L\":\"M\\u00e9rida\",\"VE-M\":\"Miranda\",\"VE-N\":\"Monagas\",\"VE-O\":\"Nueva Esparta\",\"VE-P\":\"Portuguesa\",\"VE-R\":\"Sucre\",\"VE-S\":\"T\\u00e1chira\",\"VE-T\":\"Trujillo\",\"VE-U\":\"Yaracuy\",\"VE-V\":\"Zulia\",\"VE-W\":\"Depend\\u00eancias Federais da Venezuela\",\"VE-X\":\"La Guaira (Vargas)\",\"VE-Y\":\"Delta Amacuro\",\"VE-Z\":\"Amazonas\"},\"VN\":[],\"YT\":[],\"ZA\":{\"EC\":\"Cabo Oriental\",\"FS\":\"Estado Livre\",\"GP\":\"Gauteng\",\"KZN\":\"KwaZulu-Natal\",\"LP\":\"Limpopo\",\"MP\":\"Mpumalanga\",\"NC\":\"Cabo Norte\",\"NW\":\"Noroeste\",\"WC\":\"Cabo Ocidental\"},\"ZM\":{\"ZM-01\":\"Ocidental\",\"ZM-02\":\"Central\",\"ZM-03\":\"Oriental\",\"ZM-04\":\"Luapula\",\"ZM-05\":\"Norte\",\"ZM-06\":\"Noroeste\",\"ZM-07\":\"Sul\",\"ZM-08\":\"Copperbelt\",\"ZM-09\":\"Lusaka\",\"ZM-10\":\"Muchinga\"}}","i18n_select_state_text":"Selecione uma op\u00e7\u00e3o\u2026","i18n_no_matches":"Nenhuma combina\u00e7\u00e3o foi encontrada","i18n_ajax_error":"O carregando falhou","i18n_input_too_short_1":"Digite 1 ou mais caracteres","i18n_input_too_short_n":"Digite %qty% ou mais caracteres","i18n_input_too_long_1":"Exclua 1 caracter","i18n_input_too_long_n":"Exclua %qty% caracteres","i18n_selection_too_long_1":"Voc\u00ea pode apenas selecionar 1 item","i18n_selection_too_long_n":"Voc\u00ea pode apenas selecionar %qty% itens","i18n_load_more":"Carregando mais resultados\u2026","i18n_searching":"Procurando\u2026"};
</script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/frontend/country-select.min.js?ver=9.9.5" id="wc-country-select-js" defer data-wp-strategy="defer"></script>
<script id="wc-address-i18n-js-extra">
var wc_address_i18n_params = {"locale":"{\"AE\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"required\":false}},\"AF\":{\"state\":{\"required\":false,\"hidden\":true}},\"AL\":{\"state\":{\"label\":\"Pa\\u00eds\"}},\"AO\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"label\":\"Prov\\u00edncia\"}},\"AT\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"AU\":{\"city\":{\"label\":\"Bairro\"},\"postcode\":{\"label\":\"CEP\"},\"state\":{\"label\":\"Estado\"}},\"AX\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"BA\":{\"postcode\":{\"priority\":65},\"state\":{\"label\":\"Canton\",\"required\":false,\"hidden\":true}},\"BD\":{\"postcode\":{\"required\":false},\"state\":{\"label\":\"Estado\"}},\"BE\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"BG\":{\"state\":{\"required\":false}},\"BH\":{\"postcode\":{\"required\":false},\"state\":{\"required\":false,\"hidden\":true}},\"BI\":{\"state\":{\"required\":false,\"hidden\":true}},\"BO\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"label\":\"Departamento\"}},\"BS\":{\"postcode\":{\"required\":false,\"hidden\":true}},\"BW\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"required\":false,\"hidden\":true,\"label\":\"Estado\"}},\"BZ\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"required\":false}},\"CA\":{\"postcode\":{\"label\":\"C\\u00f3digo postal\"},\"state\":{\"label\":\"Prov\\u00edncia\"}},\"CH\":{\"postcode\":{\"priority\":65},\"state\":{\"label\":\"Canton\",\"required\":false}},\"CL\":{\"city\":{\"required\":true},\"postcode\":{\"required\":false,\"hidden\":false},\"state\":{\"label\":\"Regi\\u00e3o\"}},\"CN\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"CO\":{\"postcode\":{\"required\":false},\"state\":{\"label\":\"Departamento\"}},\"CR\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"CW\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"required\":false}},\"CY\":{\"state\":{\"required\":false,\"hidden\":true}},\"CZ\":{\"state\":{\"required\":false,\"hidden\":true}},\"DE\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false}},\"DK\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"DO\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"EC\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"EE\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"ET\":{\"state\":{\"required\":false,\"hidden\":true}},\"FI\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"FR\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"GG\":{\"state\":{\"required\":false,\"label\":\"Par\\u00f3quia\"}},\"GH\":{\"postcode\":{\"required\":false},\"state\":{\"label\":\"Regi\\u00e3o\"}},\"GP\":{\"state\":{\"required\":false,\"hidden\":true}},\"GF\":{\"state\":{\"required\":false,\"hidden\":true}},\"GR\":{\"state\":{\"required\":false}},\"GT\":{\"postcode\":{\"required\":false},\"state\":{\"label\":\"Departamento\"}},\"HK\":{\"postcode\":{\"required\":false},\"city\":{\"label\":\"Cidade \\\/ Estado\"},\"state\":{\"label\":\"Regi\\u00e3o\"}},\"HN\":{\"state\":{\"label\":\"Departamento\"}},\"HU\":{\"last_name\":{\"class\":[\"form-row-first\"],\"priority\":10},\"first_name\":{\"class\":[\"form-row-last\"],\"priority\":20},\"postcode\":{\"class\":[\"form-row-first\",\"address-field\"],\"priority\":65},\"city\":{\"class\":[\"form-row-last\",\"address-field\"]},\"address_1\":{\"priority\":71},\"address_2\":{\"priority\":72},\"state\":{\"label\":\"Pa\\u00eds\",\"required\":false}},\"ID\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"IE\":{\"postcode\":{\"required\":true,\"label\":\"Eircode\"},\"state\":{\"label\":\"Pa\\u00eds\"}},\"IS\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"IL\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"IM\":{\"state\":{\"required\":false,\"hidden\":true}},\"IN\":{\"postcode\":{\"label\":\"C\\u00f3digo PIN\"},\"state\":{\"label\":\"Estado\"}},\"IR\":{\"state\":{\"priority\":50},\"city\":{\"priority\":60},\"address_1\":{\"priority\":70},\"address_2\":{\"priority\":80}},\"IT\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":true,\"label\":\"Prov\\u00edncia\"}},\"JM\":{\"city\":{\"label\":\"Cidade \\\/ Correio\"},\"postcode\":{\"required\":false,\"label\":\"CEP\"},\"state\":{\"required\":true,\"label\":\"Par\\u00f3quia\"}},\"JP\":{\"last_name\":{\"class\":[\"form-row-first\"],\"priority\":10},\"first_name\":{\"class\":[\"form-row-last\"],\"priority\":20},\"postcode\":{\"class\":[\"form-row-first\",\"address-field\"],\"priority\":65},\"state\":{\"label\":\"Prefeitura\",\"class\":[\"form-row-last\",\"address-field\"],\"priority\":66},\"city\":{\"priority\":67},\"address_1\":{\"priority\":68},\"address_2\":{\"priority\":69}},\"KN\":{\"postcode\":{\"required\":false,\"label\":\"C\\u00f3digo postal\"},\"state\":{\"required\":true,\"label\":\"Par\\u00f3quia\"}},\"KR\":{\"state\":{\"required\":false,\"hidden\":true}},\"KW\":{\"state\":{\"required\":false,\"hidden\":true}},\"LV\":{\"state\":{\"label\":\"Munic\\u00edpio\",\"required\":false}},\"LB\":{\"state\":{\"required\":false,\"hidden\":true}},\"MF\":{\"state\":{\"required\":false,\"hidden\":true}},\"MQ\":{\"state\":{\"required\":false,\"hidden\":true}},\"MT\":{\"state\":{\"required\":false,\"hidden\":true}},\"MZ\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"label\":\"Prov\\u00edncia\"}},\"NI\":{\"state\":{\"label\":\"Departamento\"}},\"NL\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"NG\":{\"postcode\":{\"label\":\"CEP\",\"required\":false,\"hidden\":true},\"state\":{\"label\":\"Estado\"}},\"NZ\":{\"postcode\":{\"label\":\"CEP\"},\"state\":{\"required\":false,\"label\":\"Regi\\u00e3o\"}},\"NO\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"NP\":{\"state\":{\"label\":\"Estado\"},\"postcode\":{\"required\":false}},\"PA\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"PL\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"PR\":{\"city\":{\"label\":\"Munic\\u00edpio\"},\"state\":{\"required\":false,\"hidden\":true}},\"PT\":{\"state\":{\"required\":false,\"hidden\":true}},\"PY\":{\"state\":{\"label\":\"Departamento\"}},\"RE\":{\"state\":{\"required\":false,\"hidden\":true}},\"RO\":{\"state\":{\"label\":\"Pa\\u00eds\",\"required\":true}},\"RS\":{\"city\":{\"required\":true},\"postcode\":{\"required\":true},\"state\":{\"label\":\"Estado\",\"required\":false}},\"RW\":{\"state\":{\"required\":false,\"hidden\":true}},\"SG\":{\"state\":{\"required\":false,\"hidden\":true},\"city\":{\"required\":false}},\"SK\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"SI\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"SR\":{\"postcode\":{\"required\":false,\"hidden\":true}},\"SV\":{\"state\":{\"label\":\"Departamento\"}},\"ES\":{\"postcode\":{\"priority\":65},\"state\":{\"label\":\"Prov\\u00edncia\"}},\"LI\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"LK\":{\"state\":{\"required\":false,\"hidden\":true}},\"LU\":{\"state\":{\"required\":false,\"hidden\":true}},\"MD\":{\"state\":{\"label\":\"Munic\\u00edpio\\\/distrito\"}},\"SE\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"TR\":{\"postcode\":{\"priority\":65},\"state\":{\"label\":\"Prov\\u00edncia\"}},\"UG\":{\"postcode\":{\"required\":false,\"hidden\":true},\"city\":{\"label\":\"Cidade \\\/ vilarejo\",\"required\":true},\"state\":{\"label\":\"Estado\",\"required\":true}},\"US\":{\"postcode\":{\"label\":\"CEP\"},\"state\":{\"label\":\"Estado\"}},\"UY\":{\"state\":{\"label\":\"Departamento\"}},\"GB\":{\"postcode\":{\"label\":\"CEP\"},\"state\":{\"label\":\"Pa\\u00eds\",\"required\":false}},\"ST\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"label\":\"Estado\"}},\"VN\":{\"state\":{\"required\":false,\"hidden\":true},\"postcode\":{\"priority\":65,\"required\":false,\"hidden\":false},\"address_2\":{\"required\":false,\"hidden\":false}},\"WS\":{\"postcode\":{\"required\":false,\"hidden\":true}},\"YT\":{\"state\":{\"required\":false,\"hidden\":true}},\"ZA\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"ZW\":{\"postcode\":{\"required\":false,\"hidden\":true}},\"default\":{\"first_name\":{\"required\":true,\"class\":[\"form-row-first\"],\"autocomplete\":\"given-name\"},\"last_name\":{\"required\":true,\"class\":[\"form-row-last\"],\"autocomplete\":\"family-name\"},\"company\":{\"class\":[\"form-row-wide\"],\"autocomplete\":\"organization\",\"required\":false},\"country\":{\"type\":\"country\",\"required\":true,\"class\":[\"form-row-wide\",\"address-field\",\"update_totals_on_change\"],\"autocomplete\":\"country\"},\"address_1\":{\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"autocomplete\":\"address-line1\"},\"address_2\":{\"label_class\":[\"screen-reader-text\"],\"class\":[\"form-row-wide\",\"address-field\"],\"autocomplete\":\"address-line2\",\"required\":false},\"city\":{\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"autocomplete\":\"address-level2\"},\"state\":{\"type\":\"state\",\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"validate\":[\"state\"],\"autocomplete\":\"address-level1\"},\"postcode\":{\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"validate\":[\"postcode\"],\"autocomplete\":\"postal-code\"}},\"BR\":{\"first_name\":{\"required\":true,\"class\":[\"form-row-first\"],\"autocomplete\":\"given-name\"},\"last_name\":{\"required\":true,\"class\":[\"form-row-last\"],\"autocomplete\":\"family-name\"},\"company\":{\"class\":[\"form-row-wide\"],\"autocomplete\":\"organization\",\"required\":false},\"country\":{\"type\":\"country\",\"required\":true,\"class\":[\"form-row-wide\",\"address-field\",\"update_totals_on_change\"],\"autocomplete\":\"country\"},\"address_1\":{\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"autocomplete\":\"address-line1\"},\"address_2\":{\"label_class\":[\"screen-reader-text\"],\"class\":[\"form-row-wide\",\"address-field\"],\"autocomplete\":\"address-line2\",\"required\":false},\"city\":{\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"autocomplete\":\"address-level2\"},\"state\":{\"type\":\"state\",\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"validate\":[\"state\"],\"autocomplete\":\"address-level1\"},\"postcode\":{\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"validate\":[\"postcode\"],\"autocomplete\":\"postal-code\"}}}","locale_fields":"{\"address_1\":\"#billing_address_1_field, #shipping_address_1_field\",\"address_2\":\"#billing_address_2_field, #shipping_address_2_field\",\"state\":\"#billing_state_field, #shipping_state_field, #calc_shipping_state_field\",\"postcode\":\"#billing_postcode_field, #shipping_postcode_field, #calc_shipping_postcode_field\",\"city\":\"#billing_city_field, #shipping_city_field, #calc_shipping_city_field\"}","i18n_required_text":"obrigat\u00f3rio","i18n_optional_text":"opcional"};
</script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/frontend/address-i18n.min.js?ver=9.9.5" id="wc-address-i18n-js" defer data-wp-strategy="defer"></script>
<script id="wc-checkout-js-extra">
var wc_checkout_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/step\/elemenia\/?wc-ajax=%%endpoint%%&wcf_checkout_id=90","update_order_review_nonce":"79383b2be9","apply_coupon_nonce":"8f7ca2557e","remove_coupon_nonce":"3808faf3e5","option_guest_checkout":"yes","checkout_url":"\/?wc-ajax=checkout&wcf_checkout_id=90","is_checkout":"1","debug_mode":"","i18n_checkout_error":"Houve um erro ao processar sua compra. Por favor verifique por qualquer cobran\u00e7a no seu m\u00e9todo de pagamento e revise o seu <a href=\"https:\/\/pay.membero.pro\/orders\/?page_id=14\">hist\u00f3rico de compra<\/a> antes de refazer a compra."};
</script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/frontend/checkout.min.js?ver=9.9.5" id="wc-checkout-js" defer data-wp-strategy="defer"></script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/jquery-cookie/jquery.cookie.min.js?ver=1.4.1-wc.9.9.5" id="jquery-cookie-js" data-wp-strategy="defer"></script>
<script src="https://pay.membero.pro/wp-content/plugins/cartflows/assets/js/frontend.js?ver=2.1.14" id="wcf-frontend-global-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/cartflows-pro/assets/js/frontend.js?ver=2.1.7" id="wcf-pro-frontend-global-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/cartflows-pro/assets/js/analytics.js?ver=2.1.7" id="wcf-pro-analytics-global-js"></script>
<link rel="https://api.w.org/" href="https://pay.membero.pro/wp-json/" /><link rel="alternate" title="JSON" type="application/json" href="https://pay.membero.pro/wp-json/wp/v2/cartflows_step/90" /><link rel="EditURI" type="application/rsd+xml" title="RSD" href="https://pay.membero.pro/xmlrpc.php?rsd" />
<meta name="generator" content="WordPress 6.8.1" />
<meta name="generator" content="WooCommerce 9.9.5" />
<link rel="canonical" href="https://pay.membero.pro/step/elemenia/" />
<link rel='shortlink' href='https://pay.membero.pro/?p=90' />
<link rel="alternate" title="oEmbed (JSON)" type="application/json+oembed" href="https://pay.membero.pro/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fpay.membero.pro%2Fstep%2Felemenia%2F" />
<link rel="alternate" title="oEmbed (XML)" type="text/xml+oembed" href="https://pay.membero.pro/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fpay.membero.pro%2Fstep%2Felemenia%2F&#038;format=xml" />
	<noscript><style>.woocommerce-product-gallery{ opacity: 1 !important; }</style></noscript>
	<meta name="generator" content="Elementor 3.30.2; features: e_font_icon_svg, additional_custom_breakpoints, e_element_cache; settings: css_print_method-external, google_font-enabled, font_display-swap">
			<style>
				.e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
				.e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
					background-image: none !important;
				}
				@media screen and (max-height: 1024px) {
					.e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
					.e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
						background-image: none !important;
					}
				}
				@media screen and (max-height: 640px) {
					.e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
					.e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
						background-image: none !important;
					}
				}
			</style>
			</head>

<body class="wp-singular cartflows_step-template cartflows_step-template-cartflows-canvas single single-cartflows_step postid-90 wp-theme-astra wp-child-theme-astra-child theme-astra woocommerce-checkout woocommerce-page woocommerce-no-js ast-desktop ast-page-builder-template ast-no-sidebar astra-4.11.5 ast-blog-single-style-1 ast-custom-post-type ast-single-post ast-inherit-site-logo-transparent ast-hfb-header cartflows-2.1.14  cartflows-pro-2.1.7 elementor-default elementor-kit-101 elementor-page elementor-page-90 cartflows-canvas">

	
			<div class="cartflows-container" >

			<div data-elementor-type="wp-post" data-elementor-id="90" class="elementor elementor-90" data-elementor-post-type="cartflows_step">
				<div class="elementor-element elementor-element-1b53e36 e-flex e-con-boxed e-con e-parent" data-id="1b53e36" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
					<div class="e-con-inner">
				<div class="elementor-element elementor-element-65bb08a elementor-position-left elementor-mobile-position-left elementor-view-default elementor-vertical-align-top elementor-widget elementor-widget-icon-box" data-id="65bb08a" data-element_type="widget" data-widget_type="icon-box.default">
							<div class="elementor-icon-box-wrapper">

						<div class="elementor-icon-box-icon">
				<span  class="elementor-icon">
								</span>
			</div>
			
						<div class="elementor-icon-box-content">

									<h3 class="elementor-icon-box-title">
						<span  >
							COMPRA SEGURA						</span>
					</h3>
				
				
			</div>
			
		</div>
						</div>
					</div>
				</div>
		<div class="elementor-element elementor-element-ab0b4c0 e-flex e-con-boxed e-con e-parent" data-id="ab0b4c0" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
					<div class="e-con-inner">
		<div class="elementor-element elementor-element-4a5ca53 e-con-full e-flex e-con e-child" data-id="4a5ca53" data-element_type="container">
				<div class="elementor-element elementor-element-a035089 elementor-widget elementor-widget-image" data-id="a035089" data-element_type="widget" data-widget_type="image.default">
															<img decoding="async" src="https://pay.desyne.pro/wp-content/uploads/2024/08/sd.png" title="" alt="" loading="lazy" />															</div>
				<div class="elementor-element elementor-element-2faaddc4 elementor-widget-mobile__width-inherit elementor-widget elementor-widget-checkout-form" data-id="2faaddc4" data-element_type="widget" data-widget_type="checkout-form.default">
				<div class="elementor-widget-container">
							<div class = "wcf-el-checkout-form cartflows-elementor__checkout-form">
			<div id="wcf-embed-checkout-form" class="wcf-embed-checkout-form wcf-embed-checkout-form-modern-checkout wcf-modern-skin-one-column wcf-field-modern-label">
<!-- CHECKOUT SHORTCODE -->

<div class="woocommerce"><div class="woocommerce-notices-wrapper"></div>
<!-- Mobile responsive order review template -->
<div class="wcf-collapsed-order-review-section  order-review-summary-position-top">
	<div class='wcf-order-review-toggle'>
		<div class='wcf-order-review-toggle-button-wrap'>
			<span class='wcf-order-review-toggle-text'>Mostrar resumo do pedido</span>
			<span class='wcf-order-review-toggle-button cartflows-icon cartflows-cheveron-down'></span>
			<span class='wcf-order-review-toggle-button cartflows-icon cartflows-cheveron-up'></span>
		</div>
		<div class='wcf-order-review-total'>&#082;&#036;&nbsp;50,00</div>
	</div>

	<div class="wcf-cartflows-review-order-wrapper">
		
<table class="shop_table woocommerce-checkout-review-order-table cartflows_table" data-update-time="1752367818">
	<thead>
		<tr>
			<th class="product-name">Produto</th>
			<th class="product-total">Subtotal</th>
		</tr>
	</thead>
	<tbody>
						<tr class="cart_item">
					<td class="product-name">
						Teste&nbsp;						 <strong class="product-quantity">&times;&nbsp;1</strong>											</td>
					<td class="product-total">
						<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#82;&#36;</span>&nbsp;50,00</bdi></span>					</td>
				</tr>
					</tbody>
	<tfoot>
		<tr class="cart-subtotal">
			<th>Subtotal</th>
			<td><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#82;&#36;</span>&nbsp;50,00</bdi></span></td>
		</tr>
										<tr class="order-total">
			<th>Total</th>
			<td><strong><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#82;&#36;</span>&nbsp;50,00</bdi></span></strong> </td>
		</tr>

		
	</tfoot>
</table>

			</div>
</div>
<div class="woocommerce-notices-wrapper"></div>
<form name="checkout" method="post" class="checkout woocommerce-checkout" action="https://pay.membero.pro/?page_id=13" enctype="multipart/form-data">

	
		<div class='wcf-bump-order-grid-wrap wcf-all-bump-order-wrap wcf-before-checkout' data-update-time='1752367818'></div><div class="wcf-customer-info-main-wrapper">
		<div class="wcf-col2-set col2-set" id="customer_details">
			<div class="wcf-col-1 col-1">
							<div class="wcf-customer-info" id="customer_info">
				<div class="wcf-customer-info__notice"></div>
				<div class="woocommerce-billing-fields-custom">
					<h3 id="customer_information_heading">Informações do cliente											</h3>
					<div class="woocommerce-billing-fields__customer-info-wrapper">
					<p class="form-row form-row-fill validate-required" id="billing_email_field" data-priority=""><label for="billing_email" class="required_field">Endereço de E-mail&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="email" class="input-text " name="billing_email" id="billing_email" placeholder="Endereço de E-mail &#042;"  value="" aria-required="true" autocomplete="email username" /></span></p>																</div>
				</div>
			</div>
		<wc-order-attribution-inputs></wc-order-attribution-inputs><div class="woocommerce-billing-fields">
	
		<h3 id="billing_fields_heading">Dados Pessoais</h3>

	
	
	<div class="woocommerce-billing-fields__field-wrapper">
		<p class="form-row form-row-first wcf-column-100 validate-required" id="billing_first_name_field" data-priority="20"><label for="billing_first_name" class="required_field">Preencha seu nome&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_first_name" id="billing_first_name" placeholder="Preencha seu nome&nbsp;*"  value="" aria-required="true" autocomplete="given-name" /></span></p><p class="form-row form-row-wide wcf-column-100 validate-required" id="billing_cellphone_field" data-priority="30"><label for="billing_cellphone" class="required_field">Celular&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_cellphone" id="billing_cellphone" placeholder="Celular&nbsp;*"  value="" aria-required="true" /></span></p><p class="form-row form-row-wide wcf-column-100 validate-required" id="billing_cpf_field" data-priority="40"><label for="billing_cpf" class="required_field">CPF&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_cpf" id="billing_cpf" placeholder="CPF&nbsp;*"  value="" aria-required="true" /></span></p>	</div>

	</div>

			</div>

			<div class="wcf-col-2 col-2">
				
<div class="woocommerce-shipping-fields">
	</div>
<div class="woocommerce-additional-fields">
	
	
	<input type="hidden" class="input-hidden _wcf_flow_id" name="_wcf_flow_id" value="89"><input type="hidden" class="input-hidden _wcf_checkout_id" name="_wcf_checkout_id" value="90"></div>
			</div>
		</div>

		<div class='wcf-customer-shipping'></div><div class='wcf-bump-order-grid-wrap wcf-all-bump-order-wrap wcf-after-customer' data-update-time='1752367818'></div></div>
	
	<div class='wcf-order-wrap'>

		
		
		<h3 id="order_review_heading">COMPRA 100% SEGURA</h3>

		
		<div id="order_review" class="woocommerce-checkout-review-order">
			<table class="shop_table woocommerce-checkout-review-order-table" data-update-time="1752367818">
	<thead>
		<tr>
			<th class="product-name">Produto</th>
			<th class="product-total">Subtotal</th>
		</tr>
	</thead>
	<tbody>
						<tr class="cart_item">
					<td class="product-name">
						Teste&nbsp;						 <strong class="product-quantity">&times;&nbsp;1</strong>											</td>
					<td class="product-total">
						<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#82;&#36;</span>&nbsp;50,00</bdi></span>					</td>
				</tr>
					</tbody>
	<tfoot>
				<tr class="cart-subtotal">
			<th>Subtotal</th>
			<td><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#82;&#36;</span>&nbsp;50,00</bdi></span></td>
		</tr>

				
				
		
		<tr class="order-total">
			<th>Total</th>
			<td><strong><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#82;&#36;</span>&nbsp;50,00</bdi></span></strong> </td>
		</tr>

		
	</tfoot>
</table>
<div class='wcf-bump-order-grid-wrap wcf-all-bump-order-wrap wcf-after-order' data-update-time='1752367818'></div>		<div class="wcf-payment-option-heading">
			<h3 id="payment_options_heading">Pagamento</h3>
		</div>
		<div id="payment" class="woocommerce-checkout-payment">
			<ul class="wc_payment_methods payment_methods methods">
			<li class="wc_payment_method payment_method_woo-mercado-pago-custom">
	<input id="payment_method_woo-mercado-pago-custom" type="radio" class="input-radio" name="payment_method" value="woo-mercado-pago-custom"  checked='checked' data-order_button_text="" />

	<label for="payment_method_woo-mercado-pago-custom">
		Cartão de crédito ou débito <img decoding="async" src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/icons/icon-custom.png?ver=8.2.0" alt="Cartão de crédito ou débito" />	</label>
			<div class="payment_box payment_method_woo-mercado-pago-custom" >
			<div class="mp-checkout-custom-load">
    <div class="spinner-card-form"></div>
</div>
<div class='mp-checkout-container'>
            <div class='mp-checkout-custom-container'>
            
                            <div class='mp-wallet-button-container'>

                    <div class='mp-wallet-button-title'>
                        <span>Pague com seus cartões salvos</span>
                    </div>

                    <div class='mp-wallet-button-description'>
                        Acesse o Mercado Pago e pague mais rápido sem preencher formulários.                    </div>

                    <div class='mp-wallet-button-button'>
                        <button id="mp-wallet-button" onclick="submitWalletButton(event)">
                            <img decoding="async" src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/gateways/wallet-button/logo.svg?ver=8.2.0">
                        </button>
                    </div>
                </div>
            
            <div id="mp-custom-checkout-form-container">
                <div class='mp-checkout-custom-available-payments'>
                    <div class='mp-checkout-custom-available-payments-header'>
                        <div class="mp-checkout-custom-available-payments-title">
                            <img decoding="async" src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/icons/icon-blue-card.png?ver=8.2.0" class='mp-icon'>
                            <p class="mp-checkout-custom-available-payments-text">
                                Quais cartões você pode usar?                            </p>
                        </div>

                        <img decoding="async"
                            src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/custom/chevron-down.png?ver=8.2.0"
                            class='mp-checkout-custom-available-payments-collapsible'
                        />
                    </div>

                    <div class='mp-checkout-custom-available-payments-content'>
                        <payment-methods methods='[{&quot;title&quot;:&quot;Cart\u00f5es de cr\u00e9dito&quot;,&quot;label&quot;:&quot;At\u00e9 12x&quot;,&quot;payment_methods&quot;:[{&quot;src&quot;:&quot;https:\/\/http2.mlstatic.com\/storage\/logos-api-admin\/0daa1670-5c81-11ec-ae75-df2bef173be2-xl.png&quot;,&quot;alt&quot;:&quot;Mastercard&quot;},{&quot;src&quot;:&quot;https:\/\/http2.mlstatic.com\/storage\/logos-api-admin\/d589be70-eb86-11e9-b9a8-097ac027487d-xl.png&quot;,&quot;alt&quot;:&quot;Visa&quot;},{&quot;src&quot;:&quot;https:\/\/http2.mlstatic.com\/storage\/logos-api-admin\/fcdc39d0-57ad-11e8-8359-5d73691de80c-xl.svg&quot;,&quot;alt&quot;:&quot;Hipercard&quot;},{&quot;src&quot;:&quot;https:\/\/www.mercadopago.com\/org-img\/MP3\/API\/logos\/elo.gif&quot;,&quot;alt&quot;:&quot;Elo&quot;},{&quot;src&quot;:&quot;https:\/\/http2.mlstatic.com\/storage\/logos-api-admin\/b4785730-c13f-11ee-b4b3-bb9a23b70639-xl.svg&quot;,&quot;alt&quot;:&quot;American Express&quot;}]},{&quot;title&quot;:&quot;Cart\u00f5es de d\u00e9bito&quot;,&quot;payment_methods&quot;:[{&quot;src&quot;:&quot;https:\/\/www.mercadopago.com\/org-img\/MP3\/API\/logos\/elo.gif&quot;,&quot;alt&quot;:&quot;Elo Debito&quot;}]}]'></payment-methods>

                                                <hr>
                    </div>
                </div>

                <div class='mp-checkout-custom-card-form'>
                    <p class='mp-checkout-custom-card-form-title'>
                        Preencha os dados do seu cartão                    </p>

                    <div class='mp-checkout-custom-card-row'>
                        <input-label
                            isOptinal=false
                            message="Número do cartão"
                            for='mp-card-number'
                        >
                        </input-label>

                        <div class="mp-checkout-custom-card-input" id="form-checkout__cardNumber-container"></div>

                        <input-helper
                            isVisible=false
                            message="Dado obrigatório"
                            input-id="mp-card-number-helper"
                        >
                        </input-helper>
                    </div>

                    <div class='mp-checkout-custom-card-row' id="mp-card-holder-div">
                        <input-label
                            message="Nome do titular como aparece no cartão"
                            isOptinal=false
                        >
                        </input-label>

                        <input
                            class="mp-checkout-custom-card-input mp-card-holder-name"
                            placeholder="Ex.: María López"
                            id="form-checkout__cardholderName"
                            name="mp-card-holder-name"
                            data-checkout="cardholderName"
                        />

                        <input-helper
                            isVisible=false
                            message="Dado obrigatório"
                            input-id="mp-card-holder-name-helper"
                            data-main="mp-card-holder-name"
                        >
                        </input-helper>
                    </div>

                    <div class='mp-checkout-custom-card-row mp-checkout-custom-dual-column-row'>
                        <div class='mp-checkout-custom-card-column'>
                            <input-label
                                message="Vencimento"
                                isOptinal=false
                            >
                            </input-label>

                            <div
                                id="form-checkout__expirationDate-container"
                                class="mp-checkout-custom-card-input mp-checkout-custom-left-card-input"
                            >
                            </div>

                            <input-helper
                                isVisible=false
                                message="Dado obrigatório"
                                input-id="mp-expiration-date-helper"
                            >
                            </input-helper>
                        </div>

                        <div class='mp-checkout-custom-card-column'>
                            <input-label
                                message="Código de segurança"
                                isOptinal=false
                            >
                            </input-label>

                            <div id="form-checkout__securityCode-container" class="mp-checkout-custom-card-input"></div>

                            <p id="mp-security-code-info" class="mp-checkout-custom-info-text"></p>

                            <input-helper
                                isVisible=false
                                message="Dado obrigatório"
                                input-id="mp-security-code-helper"
                            >
                            </input-helper>
                        </div>
                    </div>

                    <div id="mp-doc-div" class="mp-checkout-custom-input-document" style="display: none;">
                        <input-document
                            label-message="Documento do titular"
                            helper-invalid="Insira o documento completo."
                            helper-empty="Preencha este campo."
                            helper-wrong="Insira um documento válido."
                            input-name="identificationNumber"
                            hidden-id="form-checkout__identificationNumber"
                            input-data-checkout="doc_number"
                            select-id="form-checkout__identificationType"
                            select-name="identificationType"
                            select-data-checkout="doc_type"
                            flag-error="docNumberError"
                        >
                        </input-document>
                    </div>
                </div>

                <div id="mp-checkout-custom-installments" class="mp-checkout-custom-installments-display-none">
                    <p class='mp-checkout-custom-card-form-title'>
                        Escolha o número de parcelas                    </p>

                    <div id="mp-checkout-custom-issuers-container" class="mp-checkout-custom-issuers-container">
                        <div class='mp-checkout-custom-card-row'>
                            <input-label
                                isOptinal=false
                                message="Banco emissor"
                                for='mp-issuer'
                            >
                            </input-label>
                        </div>

                        <div class="mp-input-select-input">
                            <select name="issuer" id="form-checkout__issuer" class="mp-input-select-select"></select>
                        </div>
                    </div>

                    <div id="mp-checkout-custom-installments-container" class="mp-checkout-custom-installments-container"></div>

                    <input-helper
                        isVisible=false
                        message="Escolha o número de parcelas"
                        input-id="mp-installments-helper"
                    >
                    </input-helper>

                    <select
                        style="display: none;"
                        data-checkout="installments"
                        name="installments"
                        id="form-checkout__installments"
                        class="mp-input-select-select"
                    >
                    </select>

                    <div id="mp-checkout-custom-box-input-tax-cft">
                        <div id="mp-checkout-custom-box-input-tax-tea">
                            <div id="mp-checkout-custom-tax-tea-text"></div>
                        </div>
                        <div id="mp-checkout-custom-tax-cft-text"></div>
                    </div>
                </div>

                <div class="mp-checkout-custom-terms-and-conditions">
                    <terms-and-conditions
                        description="Ao continuar, você concorda com nossos"
                        link-text="Termos e condições"
                        link-src="https://www.mercadopago.com.br/ajuda/termos-e-politicas_194"
                    >
                    </terms-and-conditions>
                </div>
            </div>


        </div>
    
</div>

<div id="mercadopago-utilities" style="display:none;">
    <input type="hidden" id="mp-amount" value='50' name="mercadopago_custom[amount]"/>
    <input type="hidden" id="currency_ratio" value='1' name="mercadopago_custom[currency_ratio]"/>
    <input type="hidden" id="paymentMethodId" name="mercadopago_custom[payment_method_id]"/>
    <input type="hidden" id="mp_checkout_type" name="mercadopago_custom[checkout_type]" value="custom"/>
    <input type="hidden" id="cardExpirationMonth" data-checkout="cardExpirationMonth"/>
    <input type="hidden" id="cardExpirationYear" data-checkout="cardExpirationYear"/>
    <input type="hidden" id="cardTokenId" name="mercadopago_custom[token]"/>
    <input type="hidden" id="cardInstallments" name="mercadopago_custom[installments]"/>
    <input type="hidden" id="mpCardSessionId" name="mercadopago_custom[session_id]" />
    <input type="hidden" id="payerDocNumber" name="mercadopago_custom[doc_number]" />
    <input type="hidden" id="payerDocType" name="mercadopago_custom[doc_type]" />
</div>

<script type="text/javascript">
    function submitWalletButton(event) {
        event.preventDefault();
        jQuery('#mp_checkout_type').val('wallet_button');
        jQuery('form.checkout, form#order_review').submit();
    }

    var availablePayment = document.getElementsByClassName('mp-checkout-custom-available-payments')[0];
    var collapsible = availablePayment.getElementsByClassName('mp-checkout-custom-available-payments-header')[0];

    collapsible.addEventListener("click", function() {
        const icon = collapsible.getElementsByClassName('mp-checkout-custom-available-payments-collapsible')[0];
        const content = availablePayment.getElementsByClassName('mp-checkout-custom-available-payments-content')[0];

        if (content.style.maxHeight) {
            content.style.maxHeight = null;
            content.style.padding = "0px";
            icon.src = "https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/custom/chevron-down.png?ver=8.2.0";
        } else {
            let hg = content.scrollHeight + 15 + "px";
            content.style.setProperty("max-height", hg, "important");
            content.style.setProperty("padding", "24px 0px 0px", "important");
            icon.src = "https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/custom/chevron-up.png?ver=8.2.0";
        }
    });
</script>

		</div>
	</li>
<li class="wc_payment_method payment_method_woo-mercado-pago-pix">
	<input id="payment_method_woo-mercado-pago-pix" type="radio" class="input-radio" name="payment_method" value="woo-mercado-pago-pix"  data-order_button_text="" />

	<label for="payment_method_woo-mercado-pago-pix">
		Pix <img decoding="async" src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/icons/icon-pix.png?ver=8.2.0" alt="Pix" />	</label>
			<div class="payment_box payment_method_woo-mercado-pago-pix" style="display:none;">
			
<div class='mp-checkout-container'>
     
        <div class="mp-checkout-pix-container">
            
            <pix-template
                title="Pague de forma segura e instantânea"
                subtitle="Ao confirmar a compra, nós vamos te mostrar o código para fazer o pagamento."
                alt="Logo Pix"
                src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/pix/pix.png?ver=8.2.0">
            </pix-template>

            <div class="mp-checkout-pix-terms-and-conditions">
                <terms-and-conditions
                    description="Ao continuar, você concorda com nossos"
                    link-text="Termos e condições"
                    link-src="https://www.mercadopago.com.br/ajuda/termos-e-politicas_194">
                </terms-and-conditions>
            </div>
        </div>
     
</div>

<script type="text/javascript">
    if (document.getElementById("payment_method_woo-mercado-pago-custom")) {
        jQuery("form.checkout").on("checkout_place_order_woo-mercado-pago-pix", function() {
            cardFormLoad();
        });
    }
</script>
		</div>
	</li>
		</ul>
		<div class="form-row place-order">
		<noscript>
			Seu navegador não suporta JavaScript ou ele está desativado. Certifique-se de clicar no botão <em>Atualizar totais</em> antes de finalizar o seu pedido. Você poderá ser cobrado mais do que a quantidade indicada acima, se não fizer isso.			<br/><button type="submit" class="button alt" name="woocommerce_checkout_update_totals" value="Atualizar">Atualizar</button>
		</noscript>

			<div class="woocommerce-terms-and-conditions-wrapper">
		<div class="woocommerce-privacy-policy-text"><p>Os seus dados pessoais serão utilizados para processar a sua compra, apoiar a sua experiência em todo este site e para outros fins descritos na nossa <a href="" class="woocommerce-privacy-policy-link" target="_blank">política de privacidade</a>.</p>
</div>
			</div>
	
		<div class='wcf-bump-order-grid-wrap wcf-all-bump-order-wrap wcf-after-payment' data-update-time='1752367818'></div>
		<button type="submit" class="button alt" name="woocommerce_checkout_place_order" id="place_order" value="Finalizar compra&nbsp;&nbsp;&#082;&#036;&nbsp;50,00" data-value="Finalizar compra&nbsp;&nbsp;&#082;&#036;&nbsp;50,00">Finalizar compra&nbsp;&nbsp;&#082;&#036;&nbsp;50,00</button>
		
		<input type="hidden" id="woocommerce-process-checkout-nonce" name="woocommerce-process-checkout-nonce" value="91370f44a0" /><input type="hidden" name="_wp_http_referer" value="/step/elemenia/" />	</div>
</div>
		</div>

		<input type="hidden" name="_wcf_bump_products" value="">
	</div>
</form>

</div>
<!-- END CHECKOUT SHORTCODE -->
</div>
		</div>
						</div>
				</div>
				<div class="elementor-element elementor-element-88742ee elementor-position-left elementor-mobile-position-left elementor-view-default elementor-vertical-align-top elementor-widget elementor-widget-icon-box" data-id="88742ee" data-element_type="widget" data-widget_type="icon-box.default">
							<div class="elementor-icon-box-wrapper">

						<div class="elementor-icon-box-icon">
				<span  class="elementor-icon">
								</span>
			</div>
			
						<div class="elementor-icon-box-content">

									<h3 class="elementor-icon-box-title">
						<span  >
							COMPRA SEGURA						</span>
					</h3>
				
				
			</div>
			
		</div>
						</div>
				<div class="elementor-element elementor-element-2d0af61 elementor-widget elementor-widget-text-editor" data-id="2d0af61" data-element_type="widget" data-widget_type="text-editor.default">
									<p><span class="mt-3 text-center text-sm text-slate-400 "><strong>Mercado Pago</strong> está processando este pagamento para o vendedor <a class="transititext-primary text-primary hover:text-primary-600 focus:text-primary-600 active:text-primary-700 dark:text-primary-400 dark:hover:text-primary-500 dark:focus:text-primary-500 dark:active:text-primary-600 transition duration-150 ease-in-out" title="Enviar e-<NAME_EMAIL>" href="mailto:<EMAIL>" data-te-toggle="tooltip">Vinicios Rabaioli</a></span> os seus dados pessoais serão utilizados somente para processar a sua compra</p>								</div>
				</div>
		<div class="elementor-element elementor-element-44ca567 e-con-full elementor-hidden-tablet elementor-hidden-mobile e-flex e-con e-child" data-id="44ca567" data-element_type="container">
				<div class="elementor-element elementor-element-c431bde elementor-widget elementor-widget-image" data-id="c431bde" data-element_type="widget" data-widget_type="image.default">
															<img decoding="async" src="https://pay.desyne.pro/wp-content/uploads/2024/08/banner_lateral.webp" title="" alt="" loading="lazy" />															</div>
				</div>
					</div>
				</div>
				</div>
			</div>

							
	<script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wp-*.php","\/wp-admin\/*","\/wp-content\/uploads\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/astra-child\/*","\/wp-content\/themes\/astra\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
<div class="wcf-quick-view-wrapper">
	<div class="wcf-quick-view-bg"><div class="wcf-quick-view-loader"></div></div>
	<div id="wcf-quick-view-modal">
		<div class="wcf-content-main-wrapper"><!--
		--><div class="wcf-content-main">
				<div class="wcf-lightbox-content">
					<div class="wcf-content-main-head">
						<a href="#" id="wcf-quick-view-close" class="wcf-quick-view-close-btn cfa cfa-close"><span class="cartflows-icon-close"></span></a>
					</div>
					<div id="wcf-quick-view-content" class="woocommerce single-product"></div>
				</div>
			</div>
		</div>
	</div>
</div>
			<script>
				const lazyloadRunObserver = () => {
					const lazyloadBackgrounds = document.querySelectorAll( `.e-con.e-parent:not(.e-lazyloaded)` );
					const lazyloadBackgroundObserver = new IntersectionObserver( ( entries ) => {
						entries.forEach( ( entry ) => {
							if ( entry.isIntersecting ) {
								let lazyloadBackground = entry.target;
								if( lazyloadBackground ) {
									lazyloadBackground.classList.add( 'e-lazyloaded' );
								}
								lazyloadBackgroundObserver.unobserve( entry.target );
							}
						});
					}, { rootMargin: '200px 0px 200px 0px' } );
					lazyloadBackgrounds.forEach( ( lazyloadBackground ) => {
						lazyloadBackgroundObserver.observe( lazyloadBackground );
					} );
				};
				const events = [
					'DOMContentLoaded',
					'elementor/lazyload/observe',
				];
				events.forEach( ( event ) => {
					document.addEventListener( event, lazyloadRunObserver );
				} );
			</script>
				<script>
		(function () {
			var c = document.body.className;
			c = c.replace(/woocommerce-no-js/, 'woocommerce-js');
			document.body.className = c;
		})();
	</script>
	<script type="text/template" id="tmpl-variation-template">
	<div class="woocommerce-variation-description">{{{ data.variation.variation_description }}}</div>
	<div class="woocommerce-variation-price">{{{ data.variation.price_html }}}</div>
	<div class="woocommerce-variation-availability">{{{ data.variation.availability_html }}}</div>
</script>
<script type="text/template" id="tmpl-unavailable-variation-template">
	<p role="alert">Desculpe, este produto não está disponível. Escolha uma combinação diferente.</p>
</script>
<link rel='stylesheet' id='wc-blocks-style-css' href='https://pay.membero.pro/wp-content/plugins/woocommerce/assets/client/blocks/wc-blocks.css?ver=wc-9.9.5' media='all' />
<link rel='stylesheet' id='cartflows-elementor-style-css' href='https://pay.membero.pro/wp-content/plugins/cartflows/modules/elementor/widgets-css/frontend.css?ver=2.1.14' media='all' />
<link rel='stylesheet' id='cartflows-pro-elementor-style-css' href='https://pay.membero.pro/wp-content/plugins/cartflows-pro/modules/elementor/widgets-css/frontend.css?ver=2.1.7' media='all' />
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/checkouts/mp-plugins-components.min.js?ver=8.2.0" id="wc_mercadopago_checkout_components-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/checkouts/mp-checkout-update.min.js?ver=8.2.0" id="wc_mercadopago_checkout_update-js"></script>
<script id="wc_mercadopago_checkout_metrics-js-extra">
var wc_mercadopago_checkout_metrics_params = {"theme":"astra-child","location":"\/checkout","plugin_version":"8.2.0","platform_version":"9.9.5","site_id":"MLB","currency":"BRL"};
var wc_mercadopago_checkout_metrics_params = {"theme":"astra-child","location":"\/checkout","plugin_version":"8.2.0","platform_version":"9.9.5","site_id":"MLB","currency":"BRL"};
var wc_mercadopago_checkout_metrics_params = {"theme":"astra-child","location":"\/checkout","plugin_version":"8.2.0","platform_version":"9.9.5","site_id":"MLB","currency":"BRL"};
</script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/checkouts/mp-checkout-metrics.min.js?ver=8.2.0" id="wc_mercadopago_checkout_metrics-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/session.min.js?ver=8.2.0" id="wc_mercadopago_security_session-js"></script>
<script src="https://sdk.mercadopago.com/js/v2?ver=8.2.0" id="wc_mercadopago_sdk-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/checkouts/custom/mp-custom-page.min.js?ver=8.2.0" id="wc_mercadopago_custom_page-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/checkouts/custom/mp-custom-elements.min.js?ver=8.2.0" id="wc_mercadopago_custom_elements-js"></script>
<script id="wc_mercadopago_custom_checkout-js-extra">
var wc_mercadopago_custom_checkout_params = {"public_key":"APP_USR-8dec0be1-f11d-4e0f-9e2f-f1de101ed385","intl":"pt-BR","site_id":"MLB","currency":"BRL","theme":"astra-child","location":"\/checkout","plugin_version":"8.2.0","platform_version":"9.9.5","cvvText":"d\u00edgitos","installmentObsFee":"Sem acr\u00e9scimos","installmentButton":"Mais op\u00e7\u00f5es","bankInterestText":"The interest rate is applied and charged by your bank.","interestText":"Interest","placeholders":{"issuer":"Banco emissor","installments":"Parcelamento","cardExpirationDate":"mm\/aa"},"cvvHint":{"back":"do verso","front":"da frente"},"input_helper_message":{"cardNumber":{"invalid_type":"N\u00famero do cart\u00e3o \u00e9 obrigat\u00f3rio","invalid_length":"N\u00famero do cart\u00e3o inv\u00e1lido"},"cardholderName":{"221":"Nome do titular \u00e9 obrigat\u00f3rio","316":"Nome do titular inv\u00e1lido"},"expirationDate":{"invalid_type":"Data de vencimento inv\u00e1lida","invalid_length":"Data de vencimento incompleta","invalid_value":"Data de vencimento inv\u00e1lida"},"securityCode":{"invalid_type":"C\u00f3digo de seguran\u00e7a \u00e9 obrigat\u00f3rio","invalid_length":"C\u00f3digo de seguran\u00e7a incompleto"}},"threeDsText":{"title_loading":"Estamos levando voc\u00ea para validar o cart\u00e3o","title_loading2":"com seu banco","text_loading":"Precisamos confirmar que voc\u00ea \u00e9 o titular do cart\u00e3o.","title_loading_response":"Estamos recebendo a resposta do seu banco","title_frame":"Conclua a valida\u00e7\u00e3o banc\u00e1ria para aprovar seu pagamento","tooltip_frame":"Mantenha esta tela aberta. Se voc\u00ea fech\u00e1-la, n\u00e3o poder\u00e1 retomar a valida\u00e7\u00e3o.","message_close":"<b>Por motivos de seguran\u00e7a, seu pagamento foi recusado<\/b><br>Recomendamos que voc\u00ea pague com o meio de pagamento e dispositivo que costuma usar para compras on-line."},"error_messages":{"default":"Algo deu errado, recomendamos que voc\u00ea tente novamente ou pague com outro meio de pagamento.","installments":{"invalid amount":"Este valor n\u00e3o permite pagamentos com cart\u00e3o de cr\u00e9dito, recomendamos pagar com outro meio de pagamento ou alterar o conte\u00fado do seu carrinho."}}};
var wc_mercadopago_custom_checkout_params = {"public_key":"APP_USR-8dec0be1-f11d-4e0f-9e2f-f1de101ed385","intl":"pt-BR","site_id":"MLB","currency":"BRL","theme":"astra-child","location":"\/checkout","plugin_version":"8.2.0","platform_version":"9.9.5","cvvText":"d\u00edgitos","installmentObsFee":"Sem acr\u00e9scimos","installmentButton":"Mais op\u00e7\u00f5es","bankInterestText":"The interest rate is applied and charged by your bank.","interestText":"Interest","placeholders":{"issuer":"Banco emissor","installments":"Parcelamento","cardExpirationDate":"mm\/aa"},"cvvHint":{"back":"do verso","front":"da frente"},"input_helper_message":{"cardNumber":{"invalid_type":"N\u00famero do cart\u00e3o \u00e9 obrigat\u00f3rio","invalid_length":"N\u00famero do cart\u00e3o inv\u00e1lido"},"cardholderName":{"221":"Nome do titular \u00e9 obrigat\u00f3rio","316":"Nome do titular inv\u00e1lido"},"expirationDate":{"invalid_type":"Data de vencimento inv\u00e1lida","invalid_length":"Data de vencimento incompleta","invalid_value":"Data de vencimento inv\u00e1lida"},"securityCode":{"invalid_type":"C\u00f3digo de seguran\u00e7a \u00e9 obrigat\u00f3rio","invalid_length":"C\u00f3digo de seguran\u00e7a incompleto"}},"threeDsText":{"title_loading":"Estamos levando voc\u00ea para validar o cart\u00e3o","title_loading2":"com seu banco","text_loading":"Precisamos confirmar que voc\u00ea \u00e9 o titular do cart\u00e3o.","title_loading_response":"Estamos recebendo a resposta do seu banco","title_frame":"Conclua a valida\u00e7\u00e3o banc\u00e1ria para aprovar seu pagamento","tooltip_frame":"Mantenha esta tela aberta. Se voc\u00ea fech\u00e1-la, n\u00e3o poder\u00e1 retomar a valida\u00e7\u00e3o.","message_close":"<b>Por motivos de seguran\u00e7a, seu pagamento foi recusado<\/b><br>Recomendamos que voc\u00ea pague com o meio de pagamento e dispositivo que costuma usar para compras on-line."},"error_messages":{"default":"Algo deu errado, recomendamos que voc\u00ea tente novamente ou pague com outro meio de pagamento.","installments":{"invalid amount":"Este valor n\u00e3o permite pagamentos com cart\u00e3o de cr\u00e9dito, recomendamos pagar com outro meio de pagamento ou alterar o conte\u00fado do seu carrinho."}}};
</script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/checkouts/custom/mp-custom-checkout.min.js?ver=8.2.0" id="wc_mercadopago_custom_checkout-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/sourcebuster/sourcebuster.min.js?ver=9.9.5" id="sourcebuster-js-js"></script>
<script id="wc-order-attribution-js-extra">
var wc_order_attribution = {"params":{"lifetime":1.0e-5,"session":30,"base64":false,"ajaxurl":"https:\/\/pay.membero.pro\/wp-admin\/admin-ajax.php","prefix":"wc_order_attribution_","allowTracking":true},"fields":{"source_type":"current.typ","referrer":"current_add.rf","utm_campaign":"current.cmp","utm_source":"current.src","utm_medium":"current.mdm","utm_content":"current.cnt","utm_id":"current.id","utm_term":"current.trm","utm_source_platform":"current.plt","utm_creative_format":"current.fmt","utm_marketing_tactic":"current.tct","session_entry":"current_add.ep","session_start_time":"current_add.fd","session_pages":"session.pgs","session_count":"udata.vst","user_agent":"udata.uag"}};
</script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/frontend/order-attribution.min.js?ver=9.9.5" id="wc-order-attribution-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/elementor/assets/js/webpack.runtime.min.js?ver=3.30.2" id="elementor-webpack-runtime-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/elementor/assets/js/frontend-modules.min.js?ver=3.30.2" id="elementor-frontend-modules-js"></script>
<script src="https://pay.membero.pro/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3" id="jquery-ui-core-js"></script>
<script id="elementor-frontend-js-before">
var elementorFrontendConfig = {"environmentMode":{"edit":false,"wpPreview":false,"isScriptDebug":false},"i18n":{"shareOnFacebook":"Compartilhar no Facebook","shareOnTwitter":"Compartilhar no Twitter","pinIt":"Fixar","download":"Baixar","downloadImage":"Baixar imagem","fullscreen":"Tela cheia","zoom":"Zoom","share":"Compartilhar","playVideo":"Reproduzir v\u00eddeo","previous":"Anterior","next":"Pr\u00f3ximo","close":"Fechar","a11yCarouselPrevSlideMessage":"Slide anterior","a11yCarouselNextSlideMessage":"Pr\u00f3ximo slide","a11yCarouselFirstSlideMessage":"Este \u00e9 o primeiro slide","a11yCarouselLastSlideMessage":"Este \u00e9 o \u00faltimo slide","a11yCarouselPaginationBulletMessage":"Ir para o slide"},"is_rtl":false,"breakpoints":{"xs":0,"sm":480,"md":768,"lg":1025,"xl":1440,"xxl":1600},"responsive":{"breakpoints":{"mobile":{"label":"Dispositivos m\u00f3veis no modo retrato","value":767,"default_value":767,"direction":"max","is_enabled":true},"mobile_extra":{"label":"Dispositivos m\u00f3veis no modo paisagem","value":880,"default_value":880,"direction":"max","is_enabled":false},"tablet":{"label":"Tablet no modo retrato","value":1024,"default_value":1024,"direction":"max","is_enabled":true},"tablet_extra":{"label":"Tablet no modo paisagem","value":1200,"default_value":1200,"direction":"max","is_enabled":false},"laptop":{"label":"Notebook","value":1366,"default_value":1366,"direction":"max","is_enabled":false},"widescreen":{"label":"Tela ampla (widescreen)","value":2400,"default_value":2400,"direction":"min","is_enabled":false}},"hasCustomBreakpoints":false},"version":"3.30.2","is_static":false,"experimentalFeatures":{"e_font_icon_svg":true,"additional_custom_breakpoints":true,"container":true,"e_optimized_markup":true,"theme_builder_v2":true,"nested-elements":true,"e_element_cache":true,"home_screen":true,"global_classes_should_enforce_capabilities":true,"cloud-library":true,"e_opt_in_v4_page":true,"mega-menu":true},"urls":{"assets":"https:\/\/pay.membero.pro\/wp-content\/plugins\/elementor\/assets\/","ajaxurl":"https:\/\/pay.membero.pro\/wp-admin\/admin-ajax.php","uploadUrl":"https:\/\/pay.membero.pro\/wp-content\/uploads"},"nonces":{"floatingButtonsClickTracking":"ad64690301"},"swiperClass":"swiper","settings":{"page":[],"editorPreferences":[]},"kit":{"active_breakpoints":["viewport_mobile","viewport_tablet"],"global_image_lightbox":"yes","lightbox_enable_counter":"yes","lightbox_enable_fullscreen":"yes","lightbox_enable_zoom":"yes","lightbox_enable_share":"yes","lightbox_title_src":"title","lightbox_description_src":"description","woocommerce_notices_elements":[]},"post":{"id":90,"title":"Checkout%20%E2%80%93%20Checkout","excerpt":"","featuredImage":false}};
</script>
<script src="https://pay.membero.pro/wp-content/plugins/elementor/assets/js/frontend.min.js?ver=3.30.2" id="elementor-frontend-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/cartflows/assets/js/checkout-template.js?ver=2.1.14" id="wcf-checkout-template-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/cartflows-pro/assets/js/checkout.js?ver=2.1.7" id="wcf-pro-checkout-js"></script>
<script id="mercadopago_melidata-js-extra">
var mercadopago_melidata_params = {"type":"buyer","site_id":"MLB","location":"\/checkout","payment_method":"","plugin_version":"8.2.0","platform_version":"9.9.5"};
var mercadopago_melidata_params = {"type":"buyer","site_id":"MLB","location":"\/checkout","payment_method":"","plugin_version":"8.2.0","platform_version":"9.9.5"};
var mercadopago_melidata_params = {"type":"buyer","site_id":"MLB","location":"\/checkout","payment_method":"","plugin_version":"8.2.0","platform_version":"9.9.5"};
var mercadopago_melidata_params = {"type":"buyer","site_id":"MLB","location":"\/checkout","payment_method":"","plugin_version":"8.2.0","platform_version":"9.9.5"};
var mercadopago_melidata_params = {"type":"buyer","site_id":"MLB","location":"\/checkout","payment_method":"","plugin_version":"8.2.0","platform_version":"9.9.5"};
</script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/melidata/melidata-client.min.js?ver=8.2.0" id="mercadopago_melidata-js"></script>
<script src="https://pay.membero.pro/wp-includes/js/underscore.min.js?ver=1.13.7" id="underscore-js"></script>
<script id="wp-util-js-extra">
var _wpUtilSettings = {"ajax":{"url":"\/wp-admin\/admin-ajax.php"}};
</script>
<script src="https://pay.membero.pro/wp-includes/js/wp-util.min.js?ver=6.8.1" id="wp-util-js"></script>
<script id="wc-add-to-cart-variation-js-extra">
var wc_add_to_cart_variation_params = {"wc_ajax_url":"\/step\/elemenia\/?wc-ajax=%%endpoint%%&wcf_checkout_id=90","i18n_no_matching_variations_text":"Desculpe, nenhum produto atende sua sele\u00e7\u00e3o. Escolha uma combina\u00e7\u00e3o diferente.","i18n_make_a_selection_text":"Selecione uma das op\u00e7\u00f5es do produto antes de adicion\u00e1-lo ao carrinho.","i18n_unavailable_text":"Desculpe, este produto n\u00e3o est\u00e1 dispon\u00edvel. Escolha uma combina\u00e7\u00e3o diferente.","i18n_reset_alert_text":"Sua sele\u00e7\u00e3o foi redefinida. Selecione algumas op\u00e7\u00f5es de produtos antes de adicionar este produto ao seu carrinho."};
</script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/frontend/add-to-cart-variation.min.js?ver=9.9.5" id="wc-add-to-cart-variation-js" defer data-wp-strategy="defer"></script>
<script src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/js/flexslider/jquery.flexslider.min.js?ver=2.7.2-wc.9.9.5" id="flexslider-js" defer data-wp-strategy="defer"></script>
<script src="https://pay.membero.pro/wp-content/plugins/elementor-pro/assets/js/webpack-pro.runtime.min.js?ver=3.30.0" id="elementor-pro-webpack-runtime-js"></script>
<script src="https://pay.membero.pro/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6" id="wp-hooks-js"></script>
<script src="https://pay.membero.pro/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6" id="wp-i18n-js"></script>
<script id="wp-i18n-js-after">
wp.i18n.setLocaleData( { 'text direction\u0004ltr': [ 'ltr' ] } );
</script>
<script id="elementor-pro-frontend-js-before">
var ElementorProFrontendConfig = {"ajaxurl":"https:\/\/pay.membero.pro\/wp-admin\/admin-ajax.php","nonce":"df7546c0f8","urls":{"assets":"https:\/\/pay.membero.pro\/wp-content\/plugins\/elementor-pro\/assets\/","rest":"https:\/\/pay.membero.pro\/wp-json\/"},"settings":{"lazy_load_background_images":true},"popup":{"hasPopUps":false},"shareButtonsNetworks":{"facebook":{"title":"Facebook","has_counter":true},"twitter":{"title":"Twitter"},"linkedin":{"title":"LinkedIn","has_counter":true},"pinterest":{"title":"Pinterest","has_counter":true},"reddit":{"title":"Reddit","has_counter":true},"vk":{"title":"VK","has_counter":true},"odnoklassniki":{"title":"OK","has_counter":true},"tumblr":{"title":"Tumblr"},"digg":{"title":"Digg"},"skype":{"title":"Skype"},"stumbleupon":{"title":"StumbleUpon","has_counter":true},"mix":{"title":"Mix"},"telegram":{"title":"Telegram"},"pocket":{"title":"Pocket","has_counter":true},"xing":{"title":"XING","has_counter":true},"whatsapp":{"title":"WhatsApp"},"email":{"title":"Email"},"print":{"title":"Print"},"x-twitter":{"title":"X"},"threads":{"title":"Threads"}},"woocommerce":{"menu_cart":{"cart_page_url":"https:\/\/pay.membero.pro\/?page_id=12","checkout_page_url":"https:\/\/pay.membero.pro\/?page_id=13","fragments_nonce":"1360eb3287"},"productAddedToCart":true},"facebook_sdk":{"lang":"pt_BR","app_id":""},"lottie":{"defaultAnimationUrl":"https:\/\/pay.membero.pro\/wp-content\/plugins\/elementor-pro\/modules\/lottie\/assets\/animations\/default.json"}};
</script>
<script src="https://pay.membero.pro/wp-content/plugins/elementor-pro/assets/js/frontend.min.js?ver=3.30.0" id="elementor-pro-frontend-js"></script>
<script src="https://pay.membero.pro/wp-content/plugins/elementor-pro/assets/js/elements-handlers.min.js?ver=3.30.0" id="pro-elements-handlers-js"></script>
			<script>
			/(trident|msie)/i.test(navigator.userAgent)&&document.getElementById&&window.addEventListener&&window.addEventListener("hashchange",function(){var t,e=location.hash.substring(1);/^[A-z0-9_-]+$/.test(e)&&(t=document.getElementById(e))&&(/^(?:a|select|input|button|textarea)$/i.test(t.tagName)||(t.tabIndex=-1),t.focus())},!1);
			</script>
			</body>

</html>

