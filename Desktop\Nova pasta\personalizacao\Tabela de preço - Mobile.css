/* Reset de estilos do CartFlows */
.wcf-collapsed-order-review-section {
display: none !important;
}

.accordion-wrapper {
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
background: white;
border-radius: 8px;
box-shadow: 0 1px 3px rgba(0,0,0,0.1);
margin-bottom: 20px;
padding: 16px;
display: block !important;
width: 100% !important;
box-sizing: border-box;
}

/* Estilos do cabeçalho */
.header-left span {
color: black;
font-weight: 600;
}

.header-right span.total-amount {
color: black;
}

.accordion-header {
display: flex;
justify-content: space-between;
align-items: center;
cursor: pointer;
padding: 8px 0;
width: 100%;
}

.header-content {
display: flex;
align-items: center;
justify-content: space-between;
width: 100%;
}

.header-left {
display: flex;
align-items: center;
gap:12px !important;
}

.header-right {
display: flex;
align-items: center;
gap: 12px;
}

/* Ícones */
.cart-icon {
stroke: currentColor;
}
.chevron-icon {
transition: transform 0.3s ease;
}

/* Conteúdo do acordeon */
.accordion-content {
display: none;
padding-top: 16px;
width: 100%;
}

.accordion-wrapper.active .accordion-content {
display: block !important;
}

.accordion-wrapper.active .chevron-icon {
transform: rotate(180deg);
}

/* Estilos da tabela */
.shop_table {
width: 100%;
border-collapse: collapse;
margin-top: 10px;
}
.cartflows_table thead {
background-color: #f8f9fa;
}

.cartflows_table th,
.cartflows_table td {
padding: 12px;
text-align: left;
}
@media screen and (max-width: 769px) {
.wcf-product-image {
display: flex;
align-items: center;
gap: 0px !important;
}
}

@media screen and (min-width: 769px) {
table.shop_table.woocommerce-checkout-review-order-table {
    /* padding: 50px !important; */
    padding-right: 20px !important;
    padding-left: 20px !important;
}
}
@media screen and (max-width: 769px) {
.wcf-embed-checkout-form table.shop_table thead tr th:nth-child( 2 ), .wcf-embed-checkout-form table.shop_table tbody tr td:nth-child( 2 ), .wcf-embed-checkout-form table.shop_table tfoot tr td:nth-child( 2 ) {

    padding-left: 30px !important;
}
}
.wcf-product-thumbnail img {
width: 48px;
height: 48px;
object-fit: cover;
border-radius: 4px;
}
@media (max-width: 768px) {
.accordion-wrapper.mobile-only {
    margin: 0 !important;
}
}
@media (max-width: 768px) {
table.shop_table.woocommerce-checkout-review-order-table.cartflows_table {
    background: #f9fafb;
    border-radius: 4px !important;
}
}

@media (max-width: 768px) {
.wcf-product-thumbnail img {
    width: 48px !important;
    height: 48px !important;
  
}
}

@media (max-width: 768px) {
h3#billing_fields_heading {
   
    margin-top: -0.1px !important;
}
}


li.payment_method_woo-mercado-pago-pix label {
   
    color: #000000 !important;
  
}


.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box {
    border-top: none !important;
  
}

p.mp-pix-template-title {
    font-weight: 600 !important;
}
@media (max-width: 768px) {
/* Garantir visibilidade do wrapper */
#order_review,
.woocommerce-checkout-review-order-table {
display: block !important;
width: 100% !important;
}
}

/* Ajustes para o container principal */
form.checkout {
width: 100% !important;
max-width: 100% !important;
}
pix-template,
.mp-pix-template-container,
.mp-pix-template-title,
.mp-pix-template-subtitle {
    font-family: "Inter", sans-serif !important;
}