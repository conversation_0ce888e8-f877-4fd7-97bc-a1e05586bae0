<style>
.secure-payment-notice {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 16px 0 !important;
    width: 100% !important;
    justify-content: center !important;
}
@media (max-width: 480px) {
.secure-payment-notice {
    display: flex !important;
    align-items: center !important;
    gap: 0px !important;
   
}
}
.secure-payment-notice svg {
    width: 16px !important;
    height: 16px !important;
    color: #3BAE7E !important;
    flex-shrink: 0 !important;
}

.secure-payment-notice span {
     color: #3BAE7E !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    text-align: center !important;
}

.mp-checkout-custom-terms-and-conditions,
.mp-checkout-pix-terms-and-conditions {
    display: none !important;
}
</style>

<script>
(function() {
    const secureNoticeHTML = `
        <div class="secure-payment-notice">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12.6667 7.33333H3.33333C2.59695 7.33333 2 7.93028 2 8.66666V13.3333C2 14.0697 2.59695 14.6667 3.33333 14.6667H12.6667C13.403 14.6667 14 14.0697 14 13.3333V8.66666C14 7.93028 13.403 7.33333 12.6667 7.33333Z" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.66667 7.33333V4.66666C4.66667 3.78261 5.01786 2.93476 5.64298 2.30964C6.2681 1.68452 7.11595 1.33333 8 1.33333C8.88406 1.33333 9.73191 1.68452 10.357 2.30964C10.9821 2.93476 11.3333 3.78261 11.3333 4.66666V7.33333" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 10.6667V11.3333" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span>Os seus dados de pagamento são criptografados e processados de forma segura.</span>
        </div>
    `;

    // Função para inserir a notificação apenas na aba do cartão
    function insertNotice() {
        // Remover qualquer notificação existente
        document.querySelectorAll('.secure-payment-notice').forEach(el => el.remove());
        
        // Verificar se a aba do cartão está ativa
        const cartaoRadio = document.getElementById('payment_method_woo-mercado-pago-custom');
        
        if (cartaoRadio && cartaoRadio.checked) {
            // Inserir no container de parcelas do cartão
            const installmentsContainer = document.getElementById('mp-checkout-custom-installments');
            if (installmentsContainer && !installmentsContainer.querySelector('.secure-payment-notice')) {
                installmentsContainer.insertAdjacentHTML('beforeend', secureNoticeHTML);
            }
            
            // Backup: se não encontrar o container de parcelas, inserir na caixa de pagamento do cartão
            if (!installmentsContainer) {
                const cartaoBox = document.querySelector('.payment_method_woo-mercado-pago-custom .payment_box');
                if (cartaoBox && !cartaoBox.querySelector('.secure-payment-notice')) {
                    cartaoBox.insertAdjacentHTML('beforeend', secureNoticeHTML);
                }
            }
        }
        
        // Sempre esconder os termos
        document.querySelectorAll('.mp-checkout-custom-terms-and-conditions, .mp-checkout-pix-terms-and-conditions')
            .forEach(el => { el.style.display = 'none'; });
    }

    // Função que será chamada quando os métodos de pagamento forem alterados
    function handlePaymentMethodChange() {
        setTimeout(insertNotice, 300);
    }

    // Executar inicialmente
    setTimeout(insertNotice, 500);
    
    // Adicionar event listeners aos radios dos métodos de pagamento
    document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
        radio.addEventListener('change', handlePaymentMethodChange);
    });
    
    // Adicionar suporte para eventos do WooCommerce
    if (typeof jQuery !== 'undefined') {
        jQuery(document.body).on('payment_method_selected updated_checkout', function() {
            setTimeout(insertNotice, 300);
        });
    }
    
    // Verificar ocasionalmente
    const checkInterval = setInterval(insertNotice, 2000);
    
    // Parar de verificar após 30 segundos para economizar recursos
    setTimeout(() => {
        clearInterval(checkInterval);
    }, 30000);
})();
</script>