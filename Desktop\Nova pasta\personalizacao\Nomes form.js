// Aguarda o DOM estar completamente carregado
document.addEventListener('DOMContentLoaded', function() {
    // Seleciona o campo de email pelo ID
    const emailField = document.getElementById('billing_email');
    
    // Verifica se o elemento existe antes de modificar
    if (emailField) {
        // Altera o placeholder para o novo texto desejado
        emailField.placeholder = 'Digite seu melhor e-mail';
        
        // Adiciona um evento de foco para melhorar a experiência do usuário
        emailField.addEventListener('focus', function() {
            this.placeholder = '';
        });
        
        // Adiciona um evento de perda de foco
        emailField.addEventListener('blur', function() {
            if (!this.value) {
                this.placeholder = 'Digite seu melhor e-mail';
            }
        });
    }
});

document.addEventListener('DOMContentLoaded', function() {
    // Encontra o elemento pelo ID
    const paymentHeading = document.getElementById('payment_options_heading');
    
    // Verifica se o elemento existe
    if (paymentHeading) {
        // Altera o texto para "Pagamento"
        paymentHeading.textContent = "Pagamento";
    }
});
