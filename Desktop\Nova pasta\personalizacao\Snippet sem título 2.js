// Script para remover todos os elementos duplicados no order bump
(function() {
    function limparOrderBump() {
        // Verificar se já existe algum header
        var headers = document.querySelectorAll('.wcf-bump-order-header');
        
        // Se existir mais de um header, manter apenas o primeiro
        if (headers.length > 1) {
            for (var i = 1; i < headers.length; i++) {
                if (headers[i].parentNode) {
                    headers[i].parentNode.removeChild(headers[i]);
                }
            }
        }
        
        // Se não existir nenhum header, criar um novo
        if (headers.length === 0) {
            criarNovoHeader();
        }
        
        // Esconder a oferta original
        var ofertaOriginal = document.querySelector('.wcf-bump-order-offer');
        if (ofertaOriginal) {
            ofertaOriginal.style.display = 'none';
        }
        
        // Remover todos os títulos duplicados fora do header
        var header = document.querySelector('.wcf-bump-order-header');
        if (header) {
            var tituloNoHeader = header.querySelector('.wcf-bump-order-bump-highlight');
            var todosTitulos = document.querySelectorAll('.wcf-bump-order-bump-highlight');
            
            for (var i = 0; i < todosTitulos.length; i++) {
                if (todosTitulos[i] !== tituloNoHeader && todosTitulos[i].closest('.wcf-bump-order-header') !== header) {
                    var elementoPai = todosTitulos[i].parentNode;
                    if (elementoPai && !elementoPai.classList.contains('wcf-bump-order-header')) {
                        elementoPai.style.display = 'none';
                    }
                }
            }
        }
        
        // Remover todos os preços duplicados
        var precoNoHeader = header ? header.querySelector('.wcf-normal-price') : null;
        var todosPrecos = document.querySelectorAll('.wcf-normal-price');
        
        for (var i = 0; i < todosPrecos.length; i++) {
            if (todosPrecos[i] !== precoNoHeader) {
                if (todosPrecos[i].parentNode) {
                    todosPrecos[i].parentNode.removeChild(todosPrecos[i]);
                }
            }
        }
    }
    
    function criarNovoHeader() {
        // Encontrar os elementos originais
        var ofertaDiv = document.querySelector('.wcf-bump-order-offer');
        var precoSpan = document.querySelector('.wcf-normal-price');
        
        if (!ofertaDiv || !precoSpan) return;
        
        // Criar o header
        var header = document.createElement('div');
        header.className = 'wcf-bump-order-header';
        header.style.display = 'flex';
        header.style.justifyContent = 'space-between';
        header.style.alignItems = 'center';
        header.style.marginBottom = '10px';
        header.style.width = '100%';
        
        // Pegar o título
        var titulo = ofertaDiv.querySelector('.wcf-bump-order-bump-highlight');
        if (!titulo) return;
        
        // Criar clones dos elementos
        var tituloClone = titulo.cloneNode(true);
        var precoClone = precoSpan.cloneNode(true);
        
        // Adicionar os elementos ao header
        header.appendChild(tituloClone);
        header.appendChild(precoClone);
        
        // Inserir o header antes da oferta
        ofertaDiv.parentNode.insertBefore(header, ofertaDiv);
    }
    
    // Executar a função imediatamente
    limparOrderBump();
    
    // Executar a cada 500ms para garantir que o layout permaneça correto
    setInterval(limparOrderBump, 500);
    
    // Executar após cliques
    document.addEventListener('click', function() {
        setTimeout(limparOrderBump, 100);
        setTimeout(limparOrderBump, 500);
    });
    
    // Executar após mudanças no DOM
    var observer = new MutationObserver(function() {
        limparOrderBump();
    });
    
    // Iniciar o observador
    if (document.body) {
        observer.observe(document.body, { childList: true, subtree: true });
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            observer.observe(document.body, { childList: true, subtree: true });
        });
    }
})();