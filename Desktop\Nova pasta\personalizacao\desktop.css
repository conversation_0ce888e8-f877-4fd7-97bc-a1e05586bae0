<style>

.accordion-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    padding: 16px;
}


table.shop_table.woocommerce-checkout-review-order-table.cartflows_table {
    background: #f9fafb;
    border-radius: 8px;
}

.wcf-product-thumbnail img {
    width: 54px !important;
    height: 54px !important;
 
}
@media screen and (min-width: 769px) {
   .wcf-product-name {
    margin-left: -28px !important;
}
}

    .wcf-product-thumbnail {
        flex-shrink: 0;
    }

    .wcf-product-name {
        margin-left: 0;
        padding-left: 0;
    }

    td.product-name {
        white-space: normal;
    }

    /* Remove espaços extras que podem estar vindo do &nbsp; */
    td.product-name .wcf-product-image + strong.product-quantity {
        margin-left: 10px;
    }
}
.accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 8px 0;
    width: 100%;
}

.header-content {
    display: flex;
    align-items: center;
    width: 100%;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: auto;
}

.cart-icon {
    stroke: currentColor;
}

.chevron-icon {
    transition: transform 0.3s ease;
    margin-left: 12px;
}

.accordion-wrapper.active .chevron-icon {
    transform: rotate(180deg);
}

.accordion-content {
    display: none;
    padding-top: 16px;
}

.accordion-wrapper.active .accordion-content {
    display: block;
}

.total-amount {
    font-weight: 600;
}

.shop_table {
    width: 100%;
    border-collapse: collapse;
}

.cartflows_table thead {
    background-color: #f8f9fa;
}

.cartflows_table th,
.cartflows_table td {
    padding: 12px;
    text-align: left;
}

.wcf-product-image {
    display: flex;
    align-items: center;
    gap: 12px;
}

.wcf-product-thumbnail img {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 4px;
}

.accordion-wrapper {
    border-radius: 0px 0px 8px 8px !important
    ;
}
</style>

