<script>
    
    
    (function() {
    let lastValue = '';
    let isProcessing = false;

    function maskCPFCNPJ(value) {
        value = value.replace(/\D/g, '');
        
        if (value.length <= 11) {
            return value.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, '$1.$2.$3-$4')
                        .replace(/^(\d{3})(\d{3})(\d{3})(\d{1})$/, '$1.$2.$3-$4')
                        .replace(/^(\d{3})(\d{3})(\d{1,3})$/, '$1.$2.$3')
                        .replace(/^(\d{3})(\d{1,3})$/, '$1.$2')
                        .replace(/^(\d{1,3})$/, '$1');
        } else {
            return value.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, '$1.$2.$3/$4-$5')
                        .replace(/^(\d{2})(\d{3})(\d{3})(\d{1,4})$/, '$1.$2.$3/$4')
                        .replace(/^(\d{2})(\d{3})(\d{1,3})$/, '$1.$2.$3')
                        .replace(/^(\d{2})(\d{1,3})$/, '$1.$2')
                        .replace(/^(\d{1,2})$/, '$1');
        }
    }

    function updateMPFields(value, force = false) {
        if (isProcessing && !force) return;
        isProcessing = true;

        try {
            const cleanValue = value.replace(/\D/g, '');
            if (!cleanValue) return;

            lastValue = cleanValue;

            // Atualiza campo oculto
            const hiddenInput = document.getElementById('form-checkout__identificationNumber');
            if (hiddenInput && hiddenInput.value !== cleanValue) {
                hiddenInput.value = cleanValue;
                hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
            }

            // Atualiza campos visíveis se existirem
            const docInput = document.querySelector('.mp-document');
            const docSelect = document.getElementById('form-checkout__identificationType');

            if (docInput && docInput.value !== cleanValue) {
                docInput.value = cleanValue;
                docInput.dispatchEvent(new Event('input', { bubbles: true }));
            }

            const docType = cleanValue.length > 11 ? 'CNPJ' : 'CPF';
            if (docSelect && docSelect.value !== docType) {
                docSelect.value = docType;
                docSelect.dispatchEvent(new Event('change', { bubbles: true }));
            }
        } finally {
            isProcessing = false;
        }
    }

    function initializeDocumentSync() {
        const mainInput = document.getElementById('billing_cpf_cnpj');
        if (!mainInput) return;

        // Sincroniza valor inicial
        if (mainInput.value) {
            updateMPFields(mainInput.value, true);
        }

        // Remove listener anterior se existir
        mainInput.removeEventListener('input', mainInput.mpListener);

        // Adiciona novo listener
        mainInput.mpListener = function(e) {
            const maskedValue = maskCPFCNPJ(e.target.value);
            if (e.target.value !== maskedValue) {
                e.target.value = maskedValue;
            }
            updateMPFields(maskedValue);
        };

        mainInput.addEventListener('input', mainInput.mpListener);
    }

    // Inicialização inicial
    initializeDocumentSync();

    // Observador mais eficiente
    let debounceTimer;
    const observer = new MutationObserver(() => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
            if (lastValue) {
                updateMPFields(lastValue, true);
            }
            initializeDocumentSync();
        }, 500);
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Atualização periódica mais leve
    setInterval(() => {
        if (lastValue && !isProcessing) {
            updateMPFields(lastValue, true);
        }
    }, 2000);
})();
</script>