/* Estilo Moderno e Minimalista para Página de Pagamento PIX */

@media (min-width: 768px) {
img.mp-pix-template-image {
    width: 36% !important;
}
}



.mp-pix-template-image {
   
 padding-bottom:24px !important;
   
}




/* Container principal */
.mp-details-pix {
  max-width: 500px !important;
  margin: 0 auto !important;
  background-color: #ffffff !important;
  border-radius: 16px !important;

  overflow: hidden !important;
  font-family: 'Poppins', sans-serif !important;
}

/* Layout da seção PIX */
.mp-row-checkout-pix {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  padding: 16px 20px !important;
}

/* Esconder elementos desnecessários */
.mp-col-md-4, 
.mp-details-pix-qr-subtitle,
.mp-details-pix-qr-description {
  display: none !important;
}

/* Ajustar coluna do QR code para ocupar toda largura e centralizar */
.mp-col-md-8 {
  width: 100% !important;
  padding: 0 !important;
  background-color: transparent !important;
  text-align: center !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}

h3#billing_fields_heading {
    padding-bottom: 12px;
}

/* Título principal */
.mp-details-title {
  font-size: 22px !important;
  font-weight: 600 !important;
  color: #333333 !important;
  text-align: center !important;
  margin-bottom: 25px !important;
}

/* Valor a pagar */
.mp-details-pix-amount {
  margin-bottom: 30px !important;
  text-align: center !important;
  width: 100% !important;
}

.mp-details-pix-qr {
  font-size: 16px !important;
  color: #666666 !important;
  display: block !important;
  margin-bottom: 8px !important;
  text-align: center !important;
}

.mp-details-pix-qr-value {
  font-size: 28px !important;
  font-weight: 700 !important;
  color: #333333 !important;
  text-align: center !important;
}

/* Título do QR code */
.mp-details-pix-qr-title {
  font-size: 16px !important;
  color: #666666 !important;
  margin-bottom: 20px !important;
  text-align: center !important;
  width: 100% !important;
}

.mp-details-pix-button:hover {
  background-color: #28a745 !important;
}
/* QR Code - Centralização corrigida */
.mp-details-pix-qr-img {
  width: 200px !important;
  height: 200px !important;
  padding: 15px !important;
  background-color: #ffffff !important;
  border: 1px solid #e6e6e6 !important;
  border-radius: 12px !important;
  margin: 0 auto 30px !important;
  display: block !important;
}

/* Container do código PIX */
.mp-details-pix-container {
  width: 100% !important;
  max-width: 350px !important;
  margin: 0 auto !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

/* Container do input e botão */
.mp-row-checkout-pix-container {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  width: 100% !important;
}

/* CORRIGIDO*/

/* Input do código PIX */
#mp-qr-code {
  width: 100% !important;
  padding: 12px 15px !important;
  border: 1px solid #e6e6e6 !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  color: #666666 !important;
  background-color: #f9f9f9 !important;
  margin-bottom: 15px !important;
  text-align: center !important;
}

/* Botão de copiar */
.mp-details-pix-button {
  width: 100% !important;
  padding: 14px 20px !important;
  background-color: #32CD32 !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
/* Adicionar ícone de cópia ao botão */
.mp-details-pix-button::before {
  content: "" !important;
  display: inline-block !important;
  width: 18px !important;
  height: 18px !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  margin-right: 8px !important;
}

/* Tempo restante */
.mp-row-checkout-pix::after {
  content: "Tempo restante: 30 minutos" !important;
  display: block !important;
  margin-top: 0px !important;
  font-size: 14px !important;
  color: #666666 !important;
  text-align: center !important;
  width: 100% !important;
}






/* Melhorias para a tabela de detalhes do pedido (imagem 2) */
.woocommerce-order-overview {
  display: flex !important;
  flex-wrap: wrap !important;
  justify-content: space-between !important;
  background-color: #f8f9fa !important;

  padding: 15px !important;
  margin: 0 0 30px 0 !important;
  border: none !important;

}

.woocommerce-order-overview li {
  flex: 1 1 45% !important;
  margin: 5px 0 !important;
  padding: 8px !important;
  border: none !important;
  font-size: 14px !important;
  color: #666666 !important;
  display: flex !important;
  flex-direction: column !important;
}

.woocommerce-order-overview li strong {
  font-size: 16px !important;
  color: #333333 !important;
  font-weight: 600 !important;
  margin-top: 4px !important;
}

/* Melhorias para a área de status do pagamento (imagem 3) */
#transaction-status-message {
  background-color: #f8f9fa !important;

  padding: 15px !important;
  margin: 20px auto !important;
  max-width: 400px !important;

}
/* CORRIGIDO*/

#transaction-status-message div {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

#transaction-status-message img {
  width: 24px !important;
  height: 24px !important;
  margin-right: 10px !important;
}

#transaction-status-message p {
  margin: 0 !important;
  color: #666666 !important;
  font-size: 15px !important;
  font-weight: 500 !important;
}
.
@media (max-width: 480px) {
mp-row-checkout-pix {
    padding: 0px !important;
}
}
/* Responsividade para dispositivos móveis */
@media (max-width: 480px) {
.mp-details-pix {
    padding: 0px !important;
} 
}
    max-width: 100% !important;
    border-radius: 12px !important;
  }
  
  .mp-row-checkout-pix {
    padding: 25px 15px !important;
  }
@media (max-width: 767.98px) {
    .mp-pix-right {
       
        margin-bottom: 0px !important;
    }
}

  
  .mp-details-pix-qr-value {
    font-size: 24px !important;
  }
  
  .mp-details-pix-qr-img {
    width: 180px !important;
    height: 180px !important;
  }
  
  .woocommerce-order-overview li {
    flex: 1 1 100% !important;
  }
}

/* Estilo específico para centralizar o código PIX abaixo do QR code */
.mp-details-pix-container p,
.mp-pix-image-qr-code p,
#mp-qr-code,
.mp-details-pix-qr-code p {
  text-align: center !important;
  width: 100% !important;
  display: block !important;
  margin: 0 auto 15px !important;
}

/* Ajuste para o input que contém o código */
#mp-qr-code {
  width: 100% !important;
  max-width: 350px !important;
  padding: 12px 15px !important;
  border: 1px solid #e6e6e6 !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  color: #666666 !important;
  background-color: #f9f9f9 !important;
  margin: 0 auto 15px !important;
  text-align: center !important;
  display: block !important;
}


.mp-details-pix {
    display: flex !important;
    justify-content: center !important;
    text-align: center !important;
}
.mp-col-md-8.mp-text-center.mp-pix-right {
    border: none !important;
}
ul.woocommerce-order-overview.woocommerce-thankyou-order-details.order_details {
    display: none !important;
}
p.mp-details-title {
    display: none !important;
}

/* CORRIGIDO*/
/* Container principal do PIX */
.mp-details-pix {
    display: flex;
    justify-content: center;
    text-align: center;
    width: 100%;
    max-width: 1100px;
    margin: 0 auto;
    padding: 10px;
}

/* Linha de checkout do PIX */
.mp-row-checkout-pix {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Colunas */
.mp-col-md-4,
.mp-col-md-8 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 550px;
    margin: 0 auto;
    padding: 5px;
}

/* Container do código PIX */
.mp-details-pix-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    text-align: center;
    margin: 0;
    padding: 5px 0;
}

/* Linha do container de checkout PIX */
.mp-row-checkout-pix-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 400px;
    margin: 5px auto;
}

/* Input do código PIX */
#mp-qr-code {
    width: 100%;
    max-width: 400px;
    text-align: center;
    margin: 5px auto;
    padding: 8px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #f8f8f8;
    font-size: 13px;
    color: #333;
}

/* Botão de copiar código */
.mp-details-pix-button {
    width: 100%;
    max-width: 400px;
    margin: 5px auto;
    padding: 8px;
    background-color: #32CD32;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 13px;
    display: block;
}

/* Imagem do QR code */
.mp-details-pix-qr-img {
    display: block;
    margin: 5px auto;
    max-width: 200px;
}


/* CORRIGIDO*/

/* Textos e títulos */
.mp-details-pix-title,
.mp-details-pix-qr-title,
.mp-details-pix-qr-subtitle,
.mp-details-pix-qr-description,
.mp-details-pix-amount,
.mp-details-pix p {
    text-align: center;
    width: 100%;
    margin: 5px auto;
    padding: 0;
    font-size: 14px;
}

/* Lista de passos */
.mp-steps-congrats {
    padding-left: 0;
    margin: 5px 0;
}

.mp-details-list {
    margin-bottom: 5px;
    padding: 0;
}

.mp-details-pix-number-p {
    margin: 0 0 2px 0;
}

.mp-details-list-description {
    margin: 0;
    font-size: 13px;
}

/* Valor a pagar */
.mp-details-pix-amount {
    margin: 5px 0;
}

.mp-details-pix-qr,
.mp-details-pix-qr-value {
    margin: 0;
    padding: 0;
}

/* Responsividade para dispositivos móveis */
@media (max-width: 768px) {
    .mp-row-checkout-pix {
        flex-direction: column-reverse;
    }
    
    .mp-col-md-4,
    .mp-col-md-8 {
        width: 100%;
        max-width: 100%;
        padding: 5px;
    }
    
    #mp-qr-code,
    .mp-details-pix-button {
        max-width: 100%;
    }
    
    .mp-details-pix-img {
        max-width: 80px;
    }
}
.mp-row-checkout-pix-container {

  padding: 0px !important;
}
.mp-details-pix-amount {
   margin-bottom: 10px !important; 
  
}


/* CORRIGIDO*/

/* Para garantir que qualquer sombra nos contêineres seja removida */
.mp-details-pix {
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    background-color: transparent !important;
}
.elementor-366 .elementor-element.elementor-element {
 
    padding:0px !important;

}


.elementor-element.elementor-element-6a543efd.elementor-widget.elementor-widget-image {
    display: none !important;
}






















/* Estilo para aumentar o tamanho do QR code e torná-lo responsivo */
.woocommerce-order .mp-details-pix-qr-img {
  width: 280px !important;
  height: 280px !important;
  max-width: 90% !important;
  padding: 15px !important;
  background-color: #ffffff !important;
  border: 1px solid #e6e6e6 !important;
  border-radius: 12px !important;
  margin: 0 auto 30px !important;
  display: block !important;
  transition: transform 0.3s ease !important;
}

/* Efeito de hover para o QR code */
.woocommerce-order .mp-details-pix-qr-img:hover {
  transform: scale(1.05) !important;
}

/* Responsividade para diferentes tamanhos de tela */
@media (min-width: 768px) {
  .woocommerce-order .mp-details-pix-qr-img {
    width: 250px !important;
    height: 250px !important;
  }
}

@media (min-width: 768px) {
.mp-details-pix-qr-value {
    font-size: 28px !important;
}
}
/* Para telas menores */
@media (max-width: 480px) {
  .woocommerce-order .mp-details-pix-qr-img {
    width: 180px !important;
    height: 180px !important;
  }
  
  /* Ajustes para melhor visualização em telas pequenas */
  .woocommerce-order .mp-details-pix {
    padding: 10px 5px !important;
  }
  
  .woocommerce-order .mp-row-checkout-pix {
    padding: 15px 10px !important;
  }
}

/* Para telas muito pequenas */
@media (max-width: 320px) {
  .woocommerce-order .mp-details-pix-qr-img {
    width: 220px !important;
    height: 220px !important;
    padding: 10px !important;
  }
}

/* Melhorar a centralização do QR code */
.woocommerce-order .mp-col-md-8.mp-text-center.mp-pix-right {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
}

/* Ajustar o container principal para melhor responsividade */
.woocommerce-order .mp-details-pix {
  width: 100% !important;
  max-width: 600px !important;
  margin: 0 auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

@media only screen and (max-width: 768px) {
.header-right {
    display: flex !important;

    gap: 0px !important;

}
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout h3, .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #ship-to-different-address, .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #order_review_heading {
   
    margin: 22px 0 0px !important;
}

