document.addEventListener('DOMContentLoaded', function() {
    // Função para mudar o texto
    function changeSecurityCodeText() {
        // Procura todos os elementos com a classe mp-input-label
        const labels = document.querySelectorAll('.mp-input-label');
        
        // Itera sobre cada elemento encontrado
        labels.forEach(label => {
            // Verifica se o texto contém "Código de segurança"
            if (label.textContent.includes('Código de segurança')) {
                // Substitui mantendo o asterisco vermelho
                label.innerHTML = 'CVV<b style="color: red;">*</b>';
            }
        });
    }

    // Executa a função inicialmente
    changeSecurityCodeText();

    // Observa mudanças no DOM para casos de carregamento dinâmico
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                changeSecurityCodeText();
            }
        });
    });

    // Configura o observer para observar todo o documento
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Adiciona listener para eventos de alteração do método de pagamento
    const paymentInputs = document.querySelectorAll('input[name="payment_method"]');
    paymentInputs.forEach(input => {
        input.addEventListener('change', function() {
            setTimeout(changeSecurityCodeText, 100);
        });
    });
});