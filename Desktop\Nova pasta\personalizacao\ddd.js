document.addEventListener("DOMContentLoaded", function () {

    let desktopAccordionInitialized = false; // Flag to track if desktop setup is active
    let currentObserver = null; // Variable to hold the MutationObserver instance

    // --- Function to Update Accordion Header Total ---
    function updateAccordionHeaderTotal() {
        // Target the total *within* the table, wherever it is (likely inside accordion content)
        const totalElement = document.querySelector('.custom-order-summary-container .shop_table.woocommerce-checkout-review-order-table .order-total .woocommerce-Price-amount');
        // Target the display span in the accordion header
        const headerTotalSpan = document.querySelector('.custom-order-summary-container .accordion-header .total-amount');

        if (totalElement && headerTotalSpan) {
            headerTotalSpan.textContent = totalElement.textContent;
            // console.log('Header total updated:', totalElement.textContent); // For debugging
        } else {
            // console.log('Update total failed: Element(s) not found.', totalElement, headerTotalSpan); // For debugging
        }
    }

    // --- Function to Setup Mutation Observer ---
    function setupMutationObserver() {
        if (currentObserver) {
            currentObserver.disconnect(); // Disconnect previous if exists
        }

        // Observe the container where updates are expected
        const containerToObserve = document.querySelector('.custom-order-summary-container');

        if (containerToObserve) {
            currentObserver = new MutationObserver(function(mutations) {
                // Check if relevant nodes changed (e.g., price amount)
                 let relevantChange = false;
                 mutations.forEach(mutation => {
                     // Simple check: look for changes within the table or if nodes containing prices were added/removed
                     if (mutation.target.closest('.shop_table') || mutation.target.querySelector('.woocommerce-Price-amount')) {
                         relevantChange = true;
                     }
                      // More robust: check specifically if '.order-total .woocommerce-Price-amount' text changed
                     if (mutation.type === 'characterData' && mutation.target.parentElement?.closest('.order-total')) {
                         relevantChange = true;
                     }
                 });

                 if (relevantChange) {
                    // Use setTimeout to allow WC AJAX potentially finish updates before grabbing total
                    setTimeout(updateAccordionHeaderTotal, 150);
                 }
            });

            currentObserver.observe(containerToObserve, {
                subtree: true,
                childList: true,
                characterData: true,
                attributes: true, // Observe attribute changes too, might be needed for some AJAX updates
                attributeFilter: ['class', 'style'] // Optional: filter specific attributes if needed
            });
            // console.log('Observer attached to .custom-order-summary-container'); // For debugging
        } else {
            // console.log('Observer setup failed: .custom-order-summary-container not found'); // For debugging
        }
    }

    // --- Function to Initialize Desktop Layout (Move Table + Add Accordion) ---
    function initializeDesktopLayout() {
        if (desktopAccordionInitialized) {
            // console.log('Desktop init aborted: already initialized.'); // For debugging
            return; // Prevent re-initialization
        }

        // === Part 1: Find, Clean Up, and Move the Table ===
        const tables = document.querySelectorAll(".shop_table.woocommerce-checkout-review-order-table");
        const customerInfoWrapper = document.querySelector(".wcf-customer-info-main-wrapper");

        if (tables.length === 0 || !customerInfoWrapper) {
             // console.log('Desktop init aborted: missing table or customer info wrapper.'); // For debugging
            return; // Essential elements missing
        }

        // Remove duplicates (keep the first one)
        for (let i = tables.length - 1; i > 0; i--) { // Iterate backwards for safe removal
             if (tables[i].parentNode) {
                tables[i].parentNode.removeChild(tables[i]);
                // console.log('Removed duplicate table:', i); // For debugging
             }
        }

        const tableToModify = tables[0]; // The single table we will work with

        // Create or find the custom container
        let tableContainer = document.querySelector(".custom-order-summary-container");
        if (!tableContainer) {
            tableContainer = document.createElement("div");
            tableContainer.classList.add("custom-order-summary-container");
            tableContainer.style.marginBottom = "20px";
            tableContainer.style.width = "100%";
            tableContainer.style.boxSizing = "border-box";
            customerInfoWrapper.parentNode.insertBefore(tableContainer, customerInfoWrapper);
            // console.log('.custom-order-summary-container created.'); // For debugging
        }

        // Ensure the table is inside the container
        if (tableToModify.parentNode !== tableContainer) {
            tableContainer.appendChild(tableToModify);
            // console.log('Table moved into .custom-order-summary-container.'); // For debugging
        }

        // === Part 2: Wrap the Table in an Accordion ===
        // Check if it's already wrapped (e.g., if resize happens fast)
        if (tableToModify.closest('.accordion-wrapper')) {
             // console.log('Accordion init aborted: table already wrapped.'); // For debugging
            return;
        }

        const accordionWrapper = document.createElement('div');
        accordionWrapper.className = 'accordion-wrapper';

        // Get total amount *from the table element*
        const totalElement = tableToModify.querySelector('.order-total .woocommerce-Price-amount');
        const totalAmountText = totalElement ? totalElement.textContent.trim() : ''; // Use trim()

        const accordionHeader = document.createElement('div');
        accordionHeader.className = 'accordion-header';
        accordionHeader.innerHTML = `
            <div class="header-content" style="display: flex; justify-content: space-between; align-items: center; width: 100%; cursor: pointer;">
                <div class="header-left" style="display: flex; align-items: center;">
                    <svg class="cart-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 8px;">
                        <circle cx="9" cy="21" r="1"></circle>
                        <circle cx="20" cy="21" r="1"></circle>
                        <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                    </svg>
                    <span>Detalhes da compra</span>
                </div>
                <div class="header-right" style="display: flex; align-items: center;">
                    <span class="total-amount" style="margin-right: 8px;">${totalAmountText}</span>
                    <svg class="chevron-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; transition: transform 0.3s ease;">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </div>
            </div>
        `;

        const accordionContent = document.createElement('div');
        accordionContent.className = 'accordion-content';
        // Style for initial state (hidden) - relies on CSS `.active` class to override
        accordionContent.style.display = 'none';
        accordionContent.style.overflow = 'hidden'; // Helps with transitions

        // Move the actual table element into the content div
        accordionContent.appendChild(tableToModify);

        // Assemble the wrapper
        accordionWrapper.appendChild(accordionHeader);
        accordionWrapper.appendChild(accordionContent);

        // Replace the table's original position (now empty) within the container with the new wrapper
        // Since tableToModify was moved into accordionContent, its original spot is effectively empty.
        // We append the wrapper to the container.
        tableContainer.appendChild(accordionWrapper);
        // console.log('Accordion wrapper added, containing the table.'); // For debugging


        // === Part 3: Add Click Listener ===
        // Add listener AFTER the header is part of the wrapper and potentially in the DOM
        const headerElement = accordionWrapper.querySelector('.accordion-header'); // Target specifically
        if (headerElement) {
             headerElement.addEventListener('click', function() {
                 // console.log('Accordion header clicked!'); // For debugging
                 accordionWrapper.classList.toggle('active');

                 // Toggle content visibility directly and chevron rotation
                 const content = accordionWrapper.querySelector('.accordion-content');
                 const chevron = headerElement.querySelector('.chevron-icon');
                 if (accordionWrapper.classList.contains('active')) {
                     content.style.display = 'block'; // Or 'grid', 'flex', etc., depending on table display
                     if(chevron) chevron.style.transform = 'rotate(180deg)';
                     // Optional: Animate height using JS if CSS transitions aren't sufficient
                 } else {
                     content.style.display = 'none';
                      if(chevron) chevron.style.transform = 'rotate(0deg)';
                     // Optional: Animate height back to 0
                 }
             });
             // console.log('Click listener added to accordion header.'); // For debugging
        } else {
             // console.log('Error: Accordion header not found for adding click listener.'); // For debugging
        }


        desktopAccordionInitialized = true; // Mark as initialized

        // === Part 4: Setup Observer ===
        setupMutationObserver();
    }

    // --- Function to Teardown Desktop Layout (Remove Accordion) ---
    function teardownDesktopLayout() {
        if (!desktopAccordionInitialized) {
            // console.log('Desktop teardown aborted: not initialized.'); // For debugging
            return;
        }

        const accordionWrapper = document.querySelector('.custom-order-summary-container .accordion-wrapper');
        const container = document.querySelector('.custom-order-summary-container');

        if (accordionWrapper && container) {
            // Find the original table *inside* the accordion content
            const originalTable = accordionWrapper.querySelector('.accordion-content > .shop_table.woocommerce-checkout-review-order-table'); // More specific selector

            if (originalTable) {
                // Put the table back into the container directly
                container.appendChild(originalTable);
                 // Remove the now-empty accordion wrapper
                accordionWrapper.remove();
                // console.log('Accordion wrapper removed, table restored in container.'); // For debugging
            } else {
                 // Fallback if table not found inside: just remove wrapper
                 accordionWrapper.remove();
                 // console.log('Accordion wrapper removed (table not found inside during teardown).'); // For debugging
            }
        }

        // Disconnect the observer when leaving desktop view
        if (currentObserver) {
            currentObserver.disconnect();
            currentObserver = null;
            // console.log('Observer disconnected.'); // For debugging
        }

        desktopAccordionInitialized = false; // Mark as ready for re-initialization if needed
    }

    // --- Initial Check and Resize Handling ---
    let isCurrentlyDesktop = window.innerWidth > 768;

    // Run on initial load if desktop
    if (isCurrentlyDesktop) {
        initializeDesktopLayout();
    }

    // Debounce resize function to avoid excessive calls
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            const wasDesktop = isCurrentlyDesktop;
            isCurrentlyDesktop = window.innerWidth > 768;

            // console.log(`Resize detected. Was Desktop: ${wasDesktop}, Is Desktop: ${isCurrentlyDesktop}`); // For debugging

            if (isCurrentlyDesktop && !wasDesktop) {
                // Transitioned from Mobile to Desktop
                // console.log('Resize: Mobile -> Desktop. Initializing...'); // For debugging
                initializeDesktopLayout();
            } else if (!isCurrentlyDesktop && wasDesktop) {
                // Transitioned from Desktop to Mobile
                // console.log('Resize: Desktop -> Mobile. Tearing down...'); // For debugging
                teardownDesktopLayout();
            }
        }, 250); // 250ms delay
    });

}); // End DOMContentLoaded