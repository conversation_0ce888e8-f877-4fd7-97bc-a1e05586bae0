/* --- Reset Básico para o Container LI --- */
li.wc_payment_method.payment_method_woo-mercado-pago-pix {
    background: none !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 0 20px 0 !important; /* Aumentar margem inferior */
    list-style: none !important;
}

/* --- Esconder o Radio Button Original --- */
li.payment_method_woo-mercado-pago-pix input[type="radio"] {
    position: absolute !important;
    opacity: 0 !important;
    pointer-events: none !important;
    width: 1px !important;
    height: 1px !important;
}

/* --- Estilo do Label (Botão de Seleção PIX) --- */
li.payment_method_woo-mercado-pago-pix label {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    padding: 14px 22px !important; /* Levemente mais padding */
    border: 1px solid #dee2e6 !important; /* Borda cinza um pouco mais visível */
    border-radius: 8px !important;
    cursor: pointer;
    background-color: #ffffff !important;
    font-weight: 500 !important;
    font-size: 1rem !important;
    color: #495057 !important;
    transition: all 0.2s ease-in-out !important;
    box-sizing: border-box !important;
    margin-bottom: 15px !important; /* Espaço antes da caixa */
}

/* --- Estilo do Ícone Pequeno Dentro do Label (Mercado Pago) --- */
li.payment_method_woo-mercado-pago-pix label img {
    width: 22px !important; /* Ligeiramente maior */
    height: 22px !important;
    margin-right: 12px !important;
    flex-shrink: 0;
}

/* --- Estilo do Label QUANDO SELECIONADO (Mais Refinado) --- */
li.payment_method_woo-mercado-pago-pix input[type="radio"]:checked + label {
            border-color: #3BAE7E !important;
            /* border-width: 2px !important; */
            border-style: solid !important;
                  background-color: #e8f5e99e !important;
            /* box-shadow: 0 0 0 2px rgba(59, 174, 126, 0.25) !important;
   
}

/* --- Caixa de Detalhes do Pagamento (Com mais presença) --- */
.payment_box.payment_method_woo-mercado-pago-pix {
    padding: 35px 25px !important; /* Mais padding interno */
    background-color: #f8f9fa !important; /* Fundo cinza claro */
    border: 1px solid #e9ecef !important; /* Borda sutil um pouco mais escura que o fundo */
    border-radius: 8px !important;
    margin-top: 0 !important;
    text-align: center !important;
    clear: both !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important; /* Sombra externa muito sutil */
    position: relative; /* Para posicionamento de elementos internos se necessário */
}

/* --- Container Interno (Flexbox para alinhar conteúdo) --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-container {
    border: none !important;
    padding: 0 !important;
    background-color: transparent !important;
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

/* --- SEU SVG Principal --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-image {
    content: url('data:image/svg+xml;utf8,<svg width="80" height="80" viewBox="0 0 193 193" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M143.749 141.252C140.491 141.259 137.264 140.621 134.253 139.375C131.243 138.128 128.509 136.298 126.21 133.99L100.879 108.659C99.982 107.807 98.7923 107.332 97.5555 107.332C96.3186 107.332 95.129 107.807 94.2323 108.659L68.8045 134.087C66.5059 136.396 63.7724 138.227 60.7619 139.473C57.7515 140.72 54.5239 141.357 51.2656 141.348H46.2718L78.358 173.435C88.3699 183.446 104.618 183.446 114.63 173.435L146.801 141.252H143.749ZM51.2656 51.6516C57.9 51.6516 64.1243 54.233 68.8045 58.9132L94.2323 84.341C94.6692 84.7787 95.1882 85.1259 95.7594 85.3628C96.3307 85.5997 96.9431 85.7216 97.5615 85.7216C98.18 85.7216 98.7923 85.5997 99.3636 85.3628C99.9349 85.1259 100.454 84.7787 100.891 84.341L126.222 59.0097C128.519 56.702 131.251 54.872 134.259 53.6257C137.267 52.3793 140.493 51.7412 143.749 51.7481H146.801L114.63 19.5774C109.819 14.7696 103.296 12.0689 96.494 12.0689C89.6924 12.0689 83.1691 14.7696 78.358 19.5774L46.2718 51.6637L51.2656 51.6516Z" fill="%2330BEAF"/><path d="M173.423 78.358L153.978 58.9132C153.541 59.0925 153.075 59.1867 152.603 59.1907H143.761C139.189 59.1907 134.714 61.0483 131.493 64.281L106.162 89.6123C105.036 90.7447 103.696 91.6434 102.221 92.2566C100.746 92.8698 99.1649 93.1855 97.5675 93.1855C95.9702 93.1855 94.3886 92.8698 92.9137 92.2566C91.4387 91.6434 90.0995 90.7447 88.973 89.6123L63.5453 64.1725C60.2829 60.9236 55.8698 59.0941 51.2656 59.0821H40.4094C39.9593 59.0785 39.5137 58.9927 39.0946 58.8288L19.5774 78.358C9.56557 88.3699 9.56557 104.618 19.5774 114.642L39.0946 134.159C39.5091 133.992 39.9506 133.902 40.3973 133.894H51.2656C55.8494 133.894 60.3125 132.048 63.5453 128.815L88.9609 103.376C91.28 101.168 94.3594 99.9361 97.5615 99.9361C100.764 99.9361 103.843 101.168 106.162 103.376L131.493 128.707C134.714 131.94 139.189 133.785 143.761 133.785H152.603C153.085 133.785 153.556 133.906 153.978 134.075L173.423 114.63C183.434 104.618 183.434 88.3699 173.423 78.358Z" fill="%2330BEAF"/></svg>') !important;
    width: 65px !important; /* Tamanho um pouco menor para mais respiro */
    height: 65px !important;
    display: block !important;
    margin: 0 auto 25px auto !important; /* Garante centralização e margem inferior */
    padding: 0 !important;
    background: none !important;
    border: none !important;
}

/* --- Estilo do Título Principal na Caixa PIX (Mais Presença) --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-title {
    font-size: 1.25em !important; /* Um pouco maior */
    font-weight: 600 !important;
    color: #343a40 !important; /* Cinza mais escuro */
    margin: 0 0 12px 0 !important; /* Mais espaço abaixo */
    line-height: 1.4 !important;
}


        li.payment_method_woo-mercado-pago-custom input[type="radio"]:checked + label {
            border-color: #3BAE7E !important;
            /* border-width: 2px !important; */
            border-style: solid !important;
            background-color: #e8f5e99e !important;
            /* box-shadow: 0 0 0 2px rgba(59, 174, 126, 0.25) !important; */
        }
  
/* --- Estilo do Subtítulo/Descrição na Caixa PIX (Clareza) --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-subtitle {
    font-size: 1em !important; /* Tamanho padrão para boa leitura */
    color: #495057 !important;
    line-height: 1.6 !important;
    margin: 0 auto !important;
    max-width: 90% !important;
}

/* --- Ocultar Termos e Condições (Mantido opcional) --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-checkout-pix-terms-and-conditions {
    display: none !important;
}

.secure-payment-notice {
  display: flex;          /* Alinha o ícone e o texto na mesma linha */
  align-items: center;    /* Centraliza verticalmente o ícone e o texto */
  color: #3BAE7E;       /* Define a cor para o texto e para o SVG (via currentColor) */
  font-size: 0.9em;       /* Tamanho de fonte um pouco menor para aviso */
  margin-top: 15px;       /* Adiciona um espaço acima do aviso (ajuste conforme necessário) */
  gap: 8px;               /* Espaço entre o ícone e o texto (alternativa a margin) */
}

/* Opcional: Se precisar ajustar o tamanho do ícone especificamente */
.secure-payment-notice svg {
  width: 16px;            /* Garante o tamanho */
  height: 16px;
  flex-shrink: 0;       /* Impede que o ícone encolha se o espaço for limitado */
}

/* Não é estritamente necessário se a cor for definida no container, mas para garantir: */
.secure-payment-notice span {
  line-height: 1.4;     /* Melhora a leitura se o texto quebrar linha */
}

