.wcf-bump-order-style-1 .wcf-bump-order-offer-content-left img {
    padding: 0 !important;
}
.wcf-bump-order-wrap .wcf-bump-order-field-wrap label {
    display: flex !important;
    align-items: center !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    font-size: 1em !important;
    color: #495057 !important;
}
li.wc_payment_method.payment_method_woo-mercado-pago-pix {
    margin: 0px !important;
}



.wcf-bump-order-header {
    padding: 0px !important;
}



/* --- Container dos Detalhes da Oferta (O Card Branco) --- */
/* Estilos Desktop */
.wcf-bump-order-wrap .wcf-content-container {
    display: flex !important;
    flex-direction: row !important; /* Lado a lado no Desktop */
    align-items: flex-start !important;
    gap: 20px !important;           /* Espaço entre imagem e texto no Desktop */
    background-color: #ffffff !important;
   
    border-radius: 8px !important;
    padding: 25px !important;      /* Padding Desktop */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06) !important;
    width: 100% !important;
    box-sizing: border-box !important;
}
/* --- Coluna Esquerda (Imagem) - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-offer-content-left.wcf-bump-order-image {
    flex-shrink: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    width: auto !important;
}

/* --- Imagem - Desktop --- */
.wcf-bump-order-wrap .wcf-image {
    display: block !important;
    /* Mantém estilos padrão ou anteriores da imagem no desktop */
    /* (Tamanho padrão do plugin ou tema) */
    max-width: 100%; /* Garante não estourar container */
    height: auto;    /* Mantem proporção */
    border-radius: 6px; /* Arredondamento padrão */
}

/* --- Coluna Direita (Bloco de Texto) - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-offer-content-right {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important; /* Empilha Título -> Preço -> Descrição */
    margin: 0 !important;
    padding: 0 !important;
}

/* --- Área do Título ('Oferta Única') - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-offer {
    width: 100% !important;
    margin: 0 0 8px 0 !important;
    padding: 0 !important;
    order: 1 !important;            /* Título Primeiro */
    text-align: left !important;
}

.wcf-bump-order-wrap .wcf-bump-order-bump-highlight {
    font-weight: 600 !important;

    color: #343a40 !important;
    display: block !important;
    line-height: 1.3 !important;
}

/* --- Container da Descrição - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-desc {
    order: 2 !important;            /* Descrição (com preço) vem depois */
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    font-size: 0.95em !important;
    line-height: 1.6 !important;
    color: #6c757d !important;
}

/* --- Parágrafo DENTRO da Descrição - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-desc p {
    margin: 0 !important;
    padding: 0 !important;
}

/* --- Span do Preço DENTRO do Parágrafo - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
    display: block !important;      /* Preço na linha de cima */
 
    color: #212529 !important;
    line-height: 1.2 !important;
    margin-bottom: 12px !important; /* Espaço abaixo */
}

/* Ocultar <br> extras */
.wcf-bump-order-wrap .wcf-bump-order-desc p br {
    display: none !important;
}
.wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price + br,
.wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price + .nbsp {
    display: none !important;
}



}

/* Ajustes finos para telas MUITO pequenas (Ex: abaixo de 400px) */
@media (max-width: 480px) {
    .wcf-bump-order-wrap .wcf-content-container {
        padding: 15px !important;
        gap: 12px !important;
    }
     .wcf-bump-order-wrap .wcf-image {
        width: 0px !important; /* Pode reduzir um pouco mais se necessário */
     }
     .wcf-bump-order-wrap .wcf-bump-order-bump-highlight {
        font-size: 1.05em !important;
     }
    .wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
        font-size: 1.4em !important;
     }
     .wcf-bump-order-wrap .wcf-bump-order-desc p { /* Aplica ao texto da descrição no <p> */
        font-size: 0.85em !important;
     }
}
@media (max-width: 600px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
        padding: 15px 16px 0 16px !important;
        margin: 0px !important;
    }
}

@media only screen and (min-width: 768px) {
    @media only screen and (min-width: 768px) {
        .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
            padding: 16px 24px 0 24px !important;
            margin: 0px !important;
        }
    }
}
@m

@media only screen and (max-width: 768px) {
    @media only screen and (min-width: 768px) {
        .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
            padding: 16px 24px 0 24px !important;
            margin: 0px !important;
        }
    }
}
@media only screen and (max-width: 768px) {
.mp-checkout-custom-card-form .mp-checkout-custom-card-row {

    padding-bottom: 16px !important;
}
}
@media only screen and (max-width: 768px) {
div#mp-card-holder-div {
	
    padding-bottom: 16px !important;
	
}
}


@media only screen and (max-width: 768px) {
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment label {

    padding: 12px !important;
}
}


/* Ajustes finos para telas MUITO pequenas (Ex: abaixo de 400px) */
@media (max-width: 480px) {
    .wcf-bump-order-wrap .wcf-content-container {
        padding: 15px !important;
        gap: 12px !important;
    }
     .wcf-bump-order-wrap .wcf-image {
        width: 60px !important; /* Pode reduzir um pouco mais se necessário */
     }
     .wcf-bump-order-wrap .wcf-bump-order-bump-highlight {
        font-size: 1.05em !important;
     }
    .wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
        font-size: 1.4em !important;
     }
     .wcf-bump-order-wrap .wcf-bump-order-desc p { /* Aplica ao texto da descrição no <p> */
        font-size: 0.85em !important;
     }
}


@media (max-width: 480px) {
.wcf-bump-order-field-wrap {
    padding: 14px !important;
}
}





/* CSS de suporte para o JavaScript */
.wcf-bump-order-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
    margin-bottom: 10px !important;
}

.wcf-bump-order-header .wcf-bump-order-offer {
    display: block !important;
    margin: 0 !important;
}

.wcf-bump-order-header .wcf-normal-price {
    display: block !important;
    margin: 0 !important;
    font-weight: bold !important;
}

/* Ajustes para mobile */
@media only screen and (max-width: 767px) {
    .wcf-bump-order-header {
        padding: 0 5px !important;
    }
}

@media only screen and (max-width: 767px) {
span.wcf-normal-price {
    text-align: left !important;
}
}
@media only screen and (max-width: 767px) {
.wcf-bump-order-desc {
    text-align: left !important;
}
}


@media (max-width: 480px) {
    .wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
        font-size: 14px !important;
    }
}
@media (max-width: 480px) {
span.woocommerce-Price-amount.amount {
    font-size: 14px !important;
}
}


.wc_payment_method > input[type="radio"]:checked + label {
    background: #f9fafb !important;
    border-color: #3BAE7E !important;
    /* border-width: 2px !important; */
    border-style: solid !important;
    background-color: #e8f5e99e !important;
}























/* CSS para o aviso de pagamento seguro */
.secure-payment-notice {
    display: flex;
    align-items: center;
    color: #2ecc71;
    margin: 15px 0;
    font-size: 14px;
    line-height: 1.4;
}

.secure-payment-notice svg {
    margin-right: 8px;
    flex-shrink: 0;
}

.secure-payment-notice span {
    color: #2ecc71;
}

/* Versão mobile - ícone acima do texto centralizado */
@media only screen and (max-width: 767px) {
    .secure-payment-notice {
        flex-direction: column !important;
        text-align: center !important;
        justify-content: center !important;
    }
    
    .secure-payment-notice svg {
        margin-right: 0 !important;
        margin-bottom: 8px !important;
    }
    
    .secure-payment-notice span {
        text-align: center !important;
        width: 100% !important;
    }
}



li.wc_payment_method.payment_method_woo-mercado-pago-pix {
    margin: 0px !important;
}

