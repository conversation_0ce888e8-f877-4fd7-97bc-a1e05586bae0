<script>
    jQuery(document).ready(function($) {
        // Variáveis globais para rastreamento
        var mpTabsInitialized = false;
        
        function setupModernPaymentTabs() {
            console.log("Verificando configuração de abas");
            
            // Função para limpar duplicações na aba do cartão
            function fixDuplicatedLabelsCard() {
                // Textos dos labels que estão duplicados
                const labelsToFix = ['Número do cartão', 'Nome do titular como aparece no cartão', 'Vencimento', 'CVV'];
                
                // Para cada texto duplicado, mantenha apenas o primeiro
                labelsToFix.forEach(function(text) {
                    // Encontrar todos os elementos com esse texto exato
                    $(".payment_method_woo-mercado-pago-custom").find(":contains('" + text + "')").filter(function() {
                        return $(this).text().trim() === text;
                    }).not(':first').remove();
                });
                
                // Remover duplicações na estrutura do DOM para campos específicos
                $('.mp-input-label:contains("Número do cartão")').not(':first').remove();
                $('.mp-input-label:contains("Nome do titular como aparece no cartão")').not(':first').remove();
                $('.mp-input-label:contains("Vencimento")').not(':first').remove();
                $('.mp-input-label:contains("CVV")').not(':first').remove();
            }
            
            // Função para limpar duplicações na aba do PIX
            function fixDuplicatedElementsPix() {
                // Remover containers duplicados do PIX
                $('.payment_method_woo-mercado-pago-pix .mp-pix-template-container').not(':first').remove();
                
                // Remover outros elementos duplicados
                $('.payment_method_woo-mercado-pago-pix .mp-pix-template-title').not(':first').remove();
                $('.payment_method_woo-mercado-pago-pix .mp-pix-template-subtitle').not(':first').remove();
                $('.payment_method_woo-mercado-pago-pix .mp-terms-and-conditions-container').not(':first').remove();
                
                // Se houver qualquer outro elemento duplicado específico do PIX, remova aqui
                $('.payment_method_woo-mercado-pago-pix pix-template').each(function() {
                    $(this).children(':gt(0)').remove();
                });
            }
            
            // Chamar as funções para corrigir duplicações
            fixDuplicatedLabelsCard();
            fixDuplicatedElementsPix();
            
            // Verificar se as abas já existem
            if ($('.mp-payment-tabs-container').length && mpTabsInitialized) {
                console.log("Abas já existem, atualizando estado");
                updateTabsState();
                return;
            }
            
            // Remover quaisquer abas duplicadas
            $('.mp-payment-tabs-container').not(':first').remove();
            
            // Adicionar CSS para estilo e comportamento
            if (!$('#mp-tabs-styles').length) {
                $('head').append(`
                    <style id="mp-tabs-styles">
                        .mp-payment-tabs-container {
                            display: flex;
                            margin-bottom: 16px !important;
                            background: #f7f7f7;
                            border-radius: 8px;
                            overflow: hidden;
                            padding: 4px;
                            position: relative;
                            z-index: 10;
                        }
                        
                        .mp-payment-tab {
                            flex: 1;
                            padding: 12px;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 8px;
                            transition: all 0.2s ease;
                            font-weight: 500;
                            font-size: 16px;
                            color: #333;
                            border-radius: 6px;
                            margin: 0 3px;
                        }
                        
                        .mp-payment-tab:hover {
                            background: rgba(42, 187, 172, 0.08);
                        }
                        
                        .mp-payment-tab.active {
                            background: white;
                            color: #2abbac;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
                        }
                        
                        .mp-payment-tab svg {
                            height: 20px;
                            width: 20px;
                            transition: all 0.2s ease;
                            vertical-align: middle;
                            fill: currentColor;
                        }
                        
                        .mp-payment-tab img {
                            height: 20px;
                            transition: all 0.2s ease;
                            vertical-align: middle;
                            filter: brightness(0);
                        }
                        
                        .mp-payment-tab.active img {
                            filter: invert(67%) sepia(29%) saturate(1064%) hue-rotate(133deg) brightness(88%) contrast(89%);
                        }
                        
                        .mp-payment-tab.active svg {
                            color: #2abbac;
                            fill: #2abbac;
                        }
                        
                        /* Wrapper para conteúdo com altura condicionada */
                        .mp-content-wrapper {
                            position: relative;
                            background: #fff;
                            border-radius: 6px;
                            overflow: hidden;
                        }
                        
                        /* Altura mínima para PIX (225px) */
                        .mp-pix-mode .mp-content-wrapper {
                            min-height: 225px;
                        }
                        
                        /* Altura mínima para o cartão */
                        .mp-card-mode .mp-content-wrapper {
                            min-height: 300px;
                        }
                        
                        /* Substitui o spinner pelo efeito shimmer/skeleton do Elementor */
                        .mp-content-loading {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(255,255,255,0.9);
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            z-index: 5;
                            padding: 20px;
                        }
                        
                        /* Skeleton loading effect */
                        .mp-skeleton-item {
                            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                            background-size: 200% 100%;
                            animation: shimmer 1.5s infinite;
                            border-radius: 4px;
                            margin-bottom: 12px;
                        }
                        
                        @keyframes shimmer {
                            0% { background-position: -200% 0; }
                            100% { background-position: 200% 0; }
                        }
                        
                        .mp-skeleton-container {
                            width: 100%;
                            max-width: 500px;
                        }
                        
                        .mp-skeleton-card .mp-skeleton-item:nth-child(1) {
                            height: 30px;
                            width: 70%;
                            margin-bottom: 20px;
                        }
                        
                        .mp-skeleton-card .mp-skeleton-item:nth-child(2),
                        .mp-skeleton-card .mp-skeleton-item:nth-child(3) {
                            height: 40px;
                        }
                        
                        .mp-skeleton-card .mp-skeleton-row {
                            display: flex;
                            gap: 12px;
                            margin-bottom: 12px;
                        }
                        
                        .mp-skeleton-card .mp-skeleton-row .mp-skeleton-item {
                            flex: 1;
                            height: 40px;
                            margin-bottom: 0;
                        }
                        
                        .mp-skeleton-pix .mp-skeleton-item:nth-child(1) {
                            height: 80px;
                            width: 80px;
                            border-radius: 50%;
                            margin: 0 auto 20px;
                        }
                        
                        .mp-skeleton-pix .mp-skeleton-item:nth-child(2) {
                            height: 24px;
                            width: 70%;
                            margin: 0 auto 15px;
                        }
                        
                        .mp-skeleton-pix .mp-skeleton-item:nth-child(3) {
                            height: 16px;
                            width: 90%;
                            margin: 0 auto;
                        }
                        
                        .wc_payment_methods {
                            padding: 0 !important;
                            border: none !important;
                            margin: 0 !important;
                        }
                        
                        /* Ocultar apenas os labels dos radio buttons, não todos os labels */
                        .wc_payment_methods > li > label {
                            display: none !important;
                        }
                        
                        /* Manter todos os outros labels visíveis */
                        .wc_payment_methods .payment_box label,
                        .wc_payment_methods .mp-input-label {
                            display: block !important;
                        }
                        
                        .wc_payment_methods input[type="radio"] {
                            position: absolute;
                            opacity: 0;
                        }
                        
                        .payment_box {
                            margin-top: 0 !important;
                            padding: 16px 0 !important;
                            border: none !important;
                            background: transparent !important;
                        }
                        
                        /* Preservar layout original dos inputs de cartão */
                        .mp-checkout-custom-dual-column-row {
                            display: flex !important;
                            gap: 16px !important;
                        }
                        
                        .mp-checkout-custom-card-column {
                            flex: 1 !important;
                        }
                        
                        /* Garante que o label CVV esteja visível */
                        .mp-input-label[data-cy="input-label"] {
                            display: block !important;
                        }
                        
                        /* Evita duplicação visual do PIX */
                        .mp-pix-template-container:not(:first-of-type) {
                            display: none !important;
                        }
                    </style>
                `);
            }
            
            // Criar o container de abas se não existir
            if (!$('.mp-payment-tabs-container').length) {
                var tabsContainer = $('<div class="mp-payment-tabs-container"></div>');
                
                // Criar a aba Cartão
                var cardTab = $('<div class="mp-payment-tab mp-payment-tab-card"></div>');
                cardTab.html('<img src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/icons/icon-blue-card.png" alt="Cartão"> Cartão');
                
                // Criar a aba PIX com o novo SVG fornecido
                var pixTab = $('<div class="mp-payment-tab mp-payment-tab-pix"></div>');
                pixTab.html(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50" width="20px" height="20px">
                        <path d="M 25 0.0390625 C 22.84 0.0390625 20.799531 0.88015625 19.269531 2.4101562 L 9.6796875 12 L 12.929688 12 C 14.529687 12 16.039922 12.619766 17.169922 13.759766 L 23.939453 20.529297 C 24.519453 21.109297 25.480547 21.109531 26.060547 20.519531 L 32.830078 13.759766 C 33.960078 12.619766 35.470312 12 37.070312 12 L 40.320312 12 L 30.730469 2.4101562 C 29.200469 0.88015625 27.16 0.0390625 25 0.0390625 z M 7.6796875 14 L 2.4101562 19.269531 C -0.74984375 22.429531 -0.74984375 27.570469 2.4101562 30.730469 L 7.6796875 36 L 12.929688 36 C 13.999687 36 14.999766 35.580078 15.759766 34.830078 L 22.529297 28.060547 C 23.889297 26.700547 26.110703 26.700547 27.470703 28.060547 L 34.240234 34.830078 C 35.000234 35.580078 36.000312 36 37.070312 36 L 42.320312 36 L 47.589844 30.730469 C 50.749844 27.570469 50.749844 22.429531 47.589844 19.269531 L 42.320312 14 L 37.070312 14 C 36.000313 14 35.000234 14.419922 34.240234 15.169922 L 27.470703 21.939453 C 26.790703 22.619453 25.9 22.960938 25 22.960938 C 24.1 22.960937 23.209297 22.619453 22.529297 21.939453 L 15.759766 15.169922 C 14.999766 14.419922 13.999688 14 12.929688 14 L 7.6796875 14 z M 25 29.037109 C 24.615 29.038359 24.229453 29.185469 23.939453 29.480469 L 17.169922 36.240234 C 16.039922 37.380234 14.529687 38 12.929688 38 L 9.6796875 38 L 19.269531 47.589844 C 20.799531 49.119844 22.84 49.960938 25 49.960938 C 27.16 49.960938 29.200469 49.119844 30.730469 47.589844 L 40.320312 38 L 37.070312 38 C 35.470313 38 33.960078 37.380234 32.830078 36.240234 L 26.060547 29.470703 C 25.770547 29.180703 25.385 29.035859 25 29.037109 z"/>
                    </svg>
                    Pix
                `);
                
                // Adicionar as abas ao container
                tabsContainer.append(cardTab).append(pixTab);
                
                // Adicionar o container antes da lista de métodos de pagamento
                $('.wc_payment_methods').before(tabsContainer);
            }
            
            // Envolver os métodos de pagamento em um wrapper se não estiverem
            if (!$('.mp-content-wrapper').length) {
                $('.wc_payment_methods').wrap('<div class="mp-content-wrapper"></div>');
            }
            
            // Criar o esqueleto de carregamento para o conteúdo (substitui o spinner)
            if ($('.mp-content-loading').length) {
                $('.mp-content-loading').remove();
            }
            
            // Criar novos elementos de esqueleto
            var skeletonHTML = `
                <div class="mp-content-loading" style="display:none;">
                    <div class="mp-skeleton-container mp-skeleton-card" style="display:none;">
                        <div class="mp-skeleton-item"></div>
                        <div class="mp-skeleton-item"></div>
                        <div class="mp-skeleton-item"></div>
                        <div class="mp-skeleton-row">
                            <div class="mp-skeleton-item"></div>
                            <div class="mp-skeleton-item"></div>
                        </div>
                        <div class="mp-skeleton-item"></div>
                    </div>
                    <div class="mp-skeleton-container mp-skeleton-pix" style="display:none;">
                        <div class="mp-skeleton-item"></div>
                        <div class="mp-skeleton-item"></div>
                        <div class="mp-skeleton-item"></div>
                    </div>
                </div>
            `;
            
            $('.mp-content-wrapper').append(skeletonHTML);
            
            // Função para mostrar o esqueleto do tipo apropriado
            function showContentLoading(type) {
                $('.mp-content-loading').fadeIn(100);
                if (type === 'card') {
                    $('.mp-skeleton-card').show();
                    $('.mp-skeleton-pix').hide();
                } else {
                    $('.mp-skeleton-pix').show();
                    $('.mp-skeleton-card').hide();
                }
            }
            
            // Função para esconder esqueleto
            function hideContentLoading() {
                $('.mp-content-loading').fadeOut(200);
            }
            
            // Aplicar evento de clique para a aba PIX
            $('.mp-payment-tab-pix').off('click').on('click', function() {
                // Atualizar aparência das abas
                $('.mp-payment-tab').removeClass('active');
                $(this).addClass('active');
                
                // Usar classe de PIX (com altura mínima específica)
                $('body').removeClass('mp-card-mode').addClass('mp-pix-mode');
                
                // Mostrar esqueleto para PIX
                showContentLoading('pix');
                
                // Selecionar o método PIX
                $('#payment_method_woo-mercado-pago-pix').prop('checked', true).get(0).click();
                
                // Verificar carregamento completo do PIX
                checkPixLoaded();
            });
            
            // Aplicar evento de clique para a aba Cartão
            $('.mp-payment-tab-card').off('click').on('click', function() {
                // Atualizar aparência das abas
                $('.mp-payment-tab').removeClass('active');
                $(this).addClass('active');
                
                // Usar classe de cartão
                $('body').removeClass('mp-pix-mode').addClass('mp-card-mode');
                
                // Mostrar esqueleto para cartão
                showContentLoading('card');
                
                // Selecionar o método Cartão
                $('#payment_method_woo-mercado-pago-custom').prop('checked', true).get(0).click();
                
                // Verificar carregamento completo do Cartão
                checkCardLoaded();
            });
            
            // Função para verificar se o PIX carregou completamente
            function checkPixLoaded() {
                if ($('.mp-pix-template-container').length) {
                    setTimeout(function() {
                        hideContentLoading();
                        $('.payment_method_woo-mercado-pago-pix').show();
                        $('.payment_method_woo-mercado-pago-custom').hide();
                        
                        // Verificar novamente se há duplicações após o carregamento completo
                        fixDuplicatedElementsPix();
                    }, 300);
                    return;
                }
                
                setTimeout(checkPixLoaded, 200);
            }
            
            // Função para verificar se o Cartão carregou completamente
            function checkCardLoaded() {
                if ($('#form-checkout__cardNumber-container iframe').length && 
                    $('#form-checkout__expirationDate-container iframe').length && 
                    $('#form-checkout__securityCode-container iframe').length) {
                    
                    setTimeout(function() {
                        hideContentLoading();
                        $('.payment_method_woo-mercado-pago-custom').show();
                        $('.payment_method_woo-mercado-pago-pix').hide();
                        
                        // Verificar novamente se há duplicações após o carregamento completo
                        fixDuplicatedLabelsCard();
                    }, 700);
                    return;
                }
                
                setTimeout(checkCardLoaded, 200);
            }
            
            // Marcar que as abas foram inicializadas
            mpTabsInitialized = true;
            
            // Inicializar estado das abas
            updateTabsState();
        }
        
        // Função para apenas atualizar o estado das abas
        function updateTabsState() {
            if ($('#payment_method_woo-mercado-pago-pix').is(':checked')) {
                $('.mp-payment-tab-pix').addClass('active');
                $('.mp-payment-tab-card').removeClass('active');
                $('.payment_method_woo-mercado-pago-pix').show();
                $('.payment_method_woo-mercado-pago-custom').hide();
                // Adicionar classe de PIX, remover classe de cartão
                $('body').removeClass('mp-card-mode').addClass('mp-pix-mode');
            } else {
                $('.mp-payment-tab-card').addClass('active');
                $('.mp-payment-tab-pix').removeClass('active');
                $('.payment_method_woo-mercado-pago-custom').show();
                $('.payment_method_woo-mercado-pago-pix').hide();
                // Adicionar classe de cartão, remover classe de PIX
                $('body').removeClass('mp-pix-mode').addClass('mp-card-mode');
            }
        }
        
        // Executar quando o documento estiver pronto
        $(document).ready(function() {
            setTimeout(setupModernPaymentTabs, 500);
        });
        
        // Executar quando o checkout for atualizado
        $(document.body).on('updated_checkout', function() {
            // Verificar se precisamos recriar as abas ou apenas atualizar estado
            if (!$('.mp-payment-tabs-container').length) {
                // Abas foram removidas, precisamos recriar
                mpTabsInitialized = false;
            }
            setTimeout(setupModernPaymentTabs, 300);
        });
    });
    </script> 