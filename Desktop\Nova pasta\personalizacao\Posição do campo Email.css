/* Garante que os itens fiquem empilhados verticalmente (se for flex) */
.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper {
  display: block !important; /* Tente block ou flex */
  /* Se usar flex, garanta a direção: */
  /* flex-direction: column !important; */
}

/* Reseta a ordem de TODOS os campos dentro do wrapper */
.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > p.form-row {
   order: initial !important; /* Tenta resetar a ordem */
   position: static !important; /* Tenta resetar posição */
   float: none !important; /* Tenta resetar float */
   margin-top: initial !important; /* Tenta resetar margem */
}

/* OU, você pode definir a ordem explicitamente (menos recomendado, mas funciona) */
/* Dê números sequenciais para a ordem visual desejada */
/* #billing_first_name_field { order: 1 !important; } */
/* #billing_email_field { order: 2 !important; } */
/* #billing_cpf_cnpj_field { order: 3 !important; } */
/* #billing_cellphone_field { order: 4 !important; } */
/* Continue para outros campos visíveis se necessário... */