<style>
/* Reset e container principal */
.wc_payment_methods {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    gap: 20px !important;
    list-style: none !important;
}
#payment_options_heading {
    display: flex;
    align-items: center;
}

#payment_options_heading::before {
    content: '';
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M1 4a1 1 0 011-1h16a1 1 0 011 1v8a1 1 0 01-1 1H2a1 1 0 01-1-1V4zm12 4a3 3 0 11-6 0 3 3 0 016 0zM4 9a1 1 0 100-2 1 1 0 000 2zm13-1a1 1 0 11-2 0 1 1 0 012 0zM1.75 14.5a.75.75 0 000 1.5c4.417 0 8.693.603 12.749 1.73 1.111.309 2.251-.512 2.251-1.696v-.784a.75.75 0 00-1.5 0v.784a.272.272 0 01-.35.25A49.043 49.043 0 001.75 14.5z' clip-rule='evenodd'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}
/* Container dos botões */
.payment-buttons-container {
    display: flex !important;
    gap: 16px !important;
    width: 100% !important;
}

/* Cada botão de pagamento */
.wc_payment_method {
    flex: 1 !important;
    list-style: none !important;
    position: relative !important;
}

/* Labels dos botões */
.wc_payment_method > label {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    padding: 16px !important;
    background: white !important;
    border: 1px solid #E5E7EB !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    width: 100% !important;
    font-weight: 500 !important;
}

/* Ícones dos botões */
.wc_payment_method > label img {
    height: 24px !important;
    width: auto !important;
    margin-left: 8px !important;
}

/* Estilo específico para o ícone do Pix */
.wc_payment_method.payment_method_woo-mercado-pago-pix > label img {
    content: url('https://pay.desyne.pro/wp-content/uploads/2025/01/pix.svg') !important;
    height: 24px !important;
    width: 24px !important;
}

/* Estilo específico para o ícone do Cartão */
.wc_payment_method.payment_method_woo-mercado-pago-custom > label img {
    content: url('https://pay.desyne.pro/wp-content/uploads/2025/01/fff.svg') !important;
    height: 24px !important;
    width: 24px !important;
}

/* Radio buttons */
.wc_payment_method > input[type="radio"] {
    display: none !important;
}

/* Estado ativo */
.wc_payment_method > input[type="radio"]:checked + label {
    background: #f9fafb !important;
}

/* Ajuste específico para o ícone do Pix quando selecionado */
.wc_payment_method.payment_method_woo-mercado-pago-pix > input[type="radio"]:checked + label img {
    filter: brightness(0) !important;
}

/* Remove o filtro de inversão para o ícone do cartão */
.wc_payment_method.payment_method_woo-mercado-pago-custom > input[type="radio"]:checked + label img {
    filter: none !important;
}

/* Container do conteúdo */
.payment_box {
    display: none !important;
    width: 100% !important;
    padding: 24px !important;
    background: white !important;
    border: 1px solid #E5E7EB !important;
    border-radius: 8px !important;
    margin-top: 16px !important;
}

/* Mostrar conteúdo quando selecionado */
.wc_payment_method > input[type="radio"]:checked ~ .payment_box {
    display: block !important;
}

/* Força largura total em elementos do conteúdo */
.payment_box,
.mp-checkout-container,
.mp-checkout-custom-container,
.mp-checkout-pix-container,
.mp-checkout-custom-card-form,
.mp-checkout-custom-card-row,
.mp-checkout-custom-available-payments,
.mp-checkout-custom-card-column,
.mp-input-select-input,
.mp-checkout-custom-issuers-container,
.mp-checkout-custom-installments-container,
#mp-custom-checkout-form-container,
.mp-checkout-custom-available-payments-content,
.mp-checkout-custom-card-form,
.mp-checkout-custom-installments,
.mp-checkout-pix-container,
.mp-checkout-container {
    width: 100% !important;
    max-width: none !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    box-sizing: border-box !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Função para inicializar os métodos de pagamento
    function initializePaymentMethods() {
        const methods = document.querySelectorAll('.wc_payment_method');
        
        methods.forEach(method => {
            const radio = method.querySelector('input[type="radio"]');
            const paymentBox = method.querySelector('.payment_box');
            
            if (radio) {
                radio.addEventListener('change', function() {
                    // Esconder todos os payment boxes
                    document.querySelectorAll('.payment_box').forEach(box => {
                        box.style.display = 'none';
                    });
                    
                    // Mostrar o payment box selecionado
                    if (paymentBox) {
                        paymentBox.style.display = 'block';
                    }
                });
            }
        });

        // Selecionar o primeiro método por padrão
        const firstRadio = document.querySelector('.wc_payment_method input[type="radio"]');
        if (firstRadio) {
            firstRadio.checked = true;
            firstRadio.dispatchEvent(new Event('change'));
        }
    }

    // Inicializar
    initializePaymentMethods();
});
</script>