/**
 * Function to arrange checkout fields: Put email right after name.
 */
function arrangeMyCheckoutFields_FinalAttempt() {
    console.log('Attempting to arrange checkout fields...');

    const nameFieldContainer = document.getElementById('billing_first_name_field');
    const emailFieldContainer = document.getElementById('billing_email_field');
    // Target the specific wrapper within the 'Dados Pessoais' section
    const wrapper = document.querySelector('.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper');

    if (nameFieldContainer && emailFieldContainer && wrapper) {
        // Check if the email field is DIRECTLY after the name field in the current DOM
        if (nameFieldContainer.nextElementSibling !== emailFieldContainer) {
            console.log('Email field NOT immediately after name. MOVING email field.');
            // Move the email field to be the direct next sibling of the name field
            nameFieldContainer.insertAdjacentElement('afterend', emailFieldContainer);
        } else {
            console.log('Email field already positioned correctly after name in HTML.');
        }

        // Ensure consistent full-width styling (helps prevent weird floats/wraps)
        nameFieldContainer.classList.add('form-row-wide');
        nameFieldContainer.classList.remove('form-row-first', 'wcf-column-50');
        emailFieldContainer.classList.add('form-row-wide');
        emailFieldContainer.classList.remove('form-row-fill');

    } else {
        console.error('Could not find all required elements (name, email, or wrapper).');
        if (!nameFieldContainer) console.error('- Name field (billing_first_name_field) missing.');
        if (!emailFieldContainer) console.error('- Email field (billing_email_field) missing.');
        if (!wrapper) console.error('- Wrapper (.woocommerce-billing-fields__field-wrapper) missing.');
    }
}

/**
 * Function to initialize all attempts to fix the layout.
 */
function initializeFieldArrangement() {
    console.log('Initializing field arrangement attempts...');

    // 1. Run immediately on DOM ready
    arrangeMyCheckoutFields_FinalAttempt();

    // 2. Run after a short delay (catch scripts running slightly after DOMContentLoaded)
    setTimeout(arrangeMyCheckoutFields_FinalAttempt, 500); // 0.5 seconds
    setTimeout(arrangeMyCheckoutFields_FinalAttempt, 1500); // 1.5 seconds (extra safe)

    // 3. Run on WooCommerce Checkout Update (Handles AJAX changes)
    if (window.jQuery) {
        jQuery(document.body).on('updated_checkout', function() {
            console.log('WooCommerce "updated_checkout" event triggered. Re-arranging fields...');
            arrangeMyCheckoutFields_FinalAttempt();
        });
    } else {
        console.warn('jQuery not found, cannot bind to WooCommerce update events reliably.');
    }

    // 4. Use MutationObserver to watch for ANY changes within the wrapper
    //    This is the strongest defense against other scripts rearranging things later.
    const targetWrapperNode = document.querySelector('.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper');
    if (targetWrapperNode) {
        console.log('Setting up MutationObserver to watch the billing fields wrapper.');
        const observerConfig = {
            childList: true // Watch for additions/removals/reordering of direct children
        };

        const observerCallback = function(mutationsList, observer) {
            // We don't need to inspect the mutations, just re-run our check
            console.log('Mutation detected in wrapper. Re-checking field order...');
            arrangeMyCheckoutFields_FinalAttempt();
        };

        const observer = new MutationObserver(observerCallback);
        observer.observe(targetWrapperNode, observerConfig);

        // Optional: You might want to disconnect the observer later if needed,
        // but for a checkout page, letting it run is usually fine.
        // Example: window.addEventListener('beforeunload', () => observer.disconnect());

    } else {
        console.error('Could not find wrapper node to attach MutationObserver.');
    }
}

// --- Start Execution ---
// Run the initialization function when the DOM is ready
if (document.readyState === 'loading') { // Loading hasn't finished yet
    document.addEventListener('DOMContentLoaded', initializeFieldArrangement);
} else { // `DOMContentLoaded` has already fired
    initializeFieldArrangement();
}