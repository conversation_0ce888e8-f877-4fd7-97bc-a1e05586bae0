

<!DOCTYPE html>
<html lang="pt-BR" class="no-js">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="http://gmpg.org/xfn/11">
	<title>ElemenIA &#8211; Desyne</title>
<!-- script to print the admin localized variables --><script type="text/javascript">var cartflows_checkout_optimized_fields = {"billing_first_name_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Preencha seu nome"},"billing_cellphone_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Celular"},"billing_email_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Preencha seu E-mail"},"billing_persontype_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Tipo de Pessoa"},"billing_cpf_cnpj_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add CPF ou CNPJ"},"billing_last_name_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Sobrenome"},"billing_company_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Nome da empresa"},"billing_country_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Pa\u00eds"},"billing_address_1_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Endere\u00e7o"},"billing_address_2_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Apartamento, su\u00edte, unidade, etc."},"billing_city_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Cidade"},"billing_state_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Estado"},"billing_postcode_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add CEP"},"billing_phone_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Telefone"},"billing_cpf_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add CPF"},"billing_cnpj_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add CNPJ"},"shipping_first_name_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Nome"},"shipping_last_name_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Sobrenome"},"shipping_company_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Nome da empresa"},"shipping_country_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Pa\u00eds"},"shipping_address_1_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Endere\u00e7o"},"shipping_address_2_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Apartamento, su\u00edte, unidade, etc."},"shipping_city_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Cidade"},"shipping_state_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Estado"},"shipping_postcode_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add CEP"},"wcf_custom_coupon_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Have a coupon?"},"order_comments_field":{"is_optimized":false,"field_label":"<div class=\"dashicons dashicons-arrow-right\"><\/div> Add Order Notes"}};</script><!-- script to print the admin localized variables --><script type="text/javascript">var cartflows_animate_tab_fields = {"enabled":"no","title":"___N\u00e3o perca a oferta___"};</script><!-- script to print the admin localized variables --><script type="text/javascript">var cartflows = {"ajax_url":"\/wp-admin\/admin-ajax.php?wcf_checkout_id=364","is_pb_preview":false,"current_theme":"Hello Elementor","current_flow":"363","current_step":364,"control_step":364,"next_step":"","page_template":"cartflows-canvas","default_page_builder":"elementor","is_checkout_page":true,"fb_setting":{"facebook_pixel_id":"","facebook_pixel_view_content":"enable","facebook_pixel_add_to_cart":"enable","facebook_pixel_initiate_checkout":"enable","facebook_pixel_add_payment_info":"enable","facebook_pixel_purchase_complete":"enable","facebook_pixel_optin_lead":"enable","facebook_pixel_tracking":"disable","facebook_pixel_tracking_for_site":"disable"},"ga_setting":{"enable_google_analytics":"disable","enable_google_analytics_for_site":"disable","google_analytics_id":"","enable_begin_checkout":"enable","enable_add_to_cart":"enable","enable_optin_lead":"enable","enable_add_payment_info":"enable","enable_purchase_event":"enable","enable_bump_order_add_to_cart":"disable"},"tik_setting":{"tiktok_pixel_id":"","enable_tiktok_begin_checkout":"disable","enable_tiktok_add_to_cart":"disable","enable_tiktok_view_content":"disable","enable_tiktok_add_payment_info":"disable","enable_tiktok_purchase_event":"disable","enable_tiktok_optin_lead":"disable","tiktok_pixel_tracking":"disable","tiktok_pixel_tracking_for_site":"disable"},"pin_settings":{"pinterest_tag_id":"","enable_pinterest_consent":"disable","enable_pinterest_begin_checkout":"disable","enable_pinterest_add_to_cart":"disable","enable_pinterest_add_payment_info":"disable","enable_pinterest_purchase_event":"disable","enable_pinterest_signup":"disable","enable_pinterest_optin_lead":"disable","pinterest_tag_tracking":"disable","pinterest_tag_tracking_for_site":"disable"},"gads_setting":{"google_ads_id":"","google_ads_label":"","enable_google_ads_begin_checkout":"disable","enable_google_ads_add_to_cart":"disable","enable_google_ads_view_content":"disable","enable_google_ads_add_payment_info":"disable","enable_google_ads_purchase_event":"disable","enable_google_ads_optin_lead":"disable","google_ads_tracking":"disable","google_ads_for_site":"disable"},"snap_settings":{"snapchat_pixel_id":"","enable_snapchat_begin_checkout":"disable","enable_snapchat_add_to_cart":"disable","enable_snapchat_view_content":"disable","enable_snapchat_purchase_event":"disable","enable_snapchat_optin_lead":"disable","enable_snapchat_subscribe_event":"disable","snapchat_pixel_tracking":"disable","snapchat_pixel_for_site":"disable"},"active_checkout_cookie":"wcf_active_checkout","is_optin":false,"pinterest_consent_cookie":"cartflows_pinterest_consent","fb_add_payment_info_data":"{\"content_ids\":[\"469\"],\"content_type\":\"product\",\"plugin\":\"CartFlows-Checkout\",\"value\":\"39.00\",\"content_name\":\"ElemenIA\",\"content_category\":\"Sem categoria\",\"contents\":\"[{\\\"id\\\":469,\\\"name\\\":\\\"ElemenIA\\\",\\\"price\\\":\\\"39.00\\\",\\\"quantity\\\":\\\"1\\\"}]\",\"currency\":\"BRL\",\"user_roles\":\"\",\"num_items\":1,\"domain\":\"https:\\\/\\\/pay.desyne.pro\",\"language\":\"pt-BR\",\"userAgent\":\"Mozilla\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\/537.36 (KHTML, like Gecko) Chrome\\\/138.0.0.0 Safari\\\/537.36\"}","add_payment_info_data":"{\"send_to\":\"\",\"event_category\":\"Enhanced-Ecommerce\",\"currency\":\"BRL\",\"coupon\":[],\"value\":\"39.00\",\"items\":[{\"id\":469,\"name\":\"ElemenIA\",\"sku\":\"\",\"category\":\"Sem categoria\",\"price\":\"39.00\",\"quantity\":\"1\"}],\"non_interaction\":true}","wcf_validate_coupon_nonce":"d29bc6cde5","wcf_validate_remove_coupon_nonce":"a6827bc4bd","wcf_validate_remove_cart_product_nonce":"dc8f2e89a0","check_email_exist_nonce":"2cc1e55a41","woocommerce_login_nonce":"95ee123453","allow_persistence":"yes","is_logged_in":false,"email_validation_msgs":{"error_msg":"Entered email address is not a valid email.","success_msg":"This email is already registered. Please enter the password to continue."},"field_validation_msgs":{"number_field":"Value must be between "},"order_review_toggle_texts":{"toggle_show_text":"Show Order Summary","toggle_hide_text":"Hide Order Summary"},"field_validation":{"is_enabled":"yes","error_msg":"\u00e9 necess\u00e1rio"},"wcf_bump_order_process_nonce":"5a51a7042f","wcf_update_order_bump_qty_nonce":"538724a96c","wcf_multiple_selection_nonce":"7d5213b73a","wcf_single_selection_nonce":"88a31b9bba","wcf_quantity_update_nonce":"dce343207a","wcf_variation_selection_nonce":"9b683c1cb3","wcf_quick_view_add_cart_nonce":"376a870ce8","is_product_options":"no","allow_autocomplete_zipcode":"no","add_to_cart_text":"Processing...","wcf_refresh_checkout":false,"analytics_base_url":"https:\/\/pay.desyne.pro\/wp-json\/cartflows-pro\/v1\/flow-analytics\/","analytics_nonce":"5c06afee2f","flow_cookie":"wcf-visited-flow-","step_cookie":"wcf-step-visited-","analytics_cookie_expire_time":365};</script><meta name='robots' content='noindex, nofollow' />
	<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
	<link rel='dns-prefetch' href='//cdnjs.cloudflare.com' />
<link rel='dns-prefetch' href='//sdk.mercadopago.com' />
<link rel="alternate" type="application/rss+xml" title="Feed para Desyne &raquo;" href="https://pay.desyne.pro/feed/" />
<link rel="alternate" type="application/rss+xml" title="Feed de comentários para Desyne &raquo;" href="https://pay.desyne.pro/comments/feed/" />
<script>
window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/pay.desyne.pro\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1"}};
/*! This file is auto-generated */
!function(i,n){var o,s,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),r=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===r[t]})}function u(e,t,n){switch(t){case"flag":return n(e,"\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f","\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f")?!1:!n(e,"\ud83c\uddfa\ud83c\uddf3","\ud83c\uddfa\u200b\ud83c\uddf3")&&!n(e,"\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f","\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f");case"emoji":return!n(e,"\ud83d\udc26\u200d\ud83d\udd25","\ud83d\udc26\u200b\ud83d\udd25")}return!1}function f(e,t,n){var r="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?new OffscreenCanvas(300,150):i.createElement("canvas"),a=r.getContext("2d",{willReadFrequently:!0}),o=(a.textBaseline="top",a.font="600 32px Arial",{});return e.forEach(function(e){o[e]=t(a,e,n)}),o}function t(e){var t=i.createElement("script");t.src=e,t.defer=!0,i.head.appendChild(t)}"undefined"!=typeof Promise&&(o="wpEmojiSettingsSupports",s=["flag","emoji"],n.supports={everything:!0,everythingExceptFlag:!0},e=new Promise(function(e){i.addEventListener("DOMContentLoaded",e,{once:!0})}),new Promise(function(t){var n=function(){try{var e=JSON.parse(sessionStorage.getItem(o));if("object"==typeof e&&"number"==typeof e.timestamp&&(new Date).valueOf()<e.timestamp+604800&&"object"==typeof e.supportTests)return e.supportTests}catch(e){}return null}();if(!n){if("undefined"!=typeof Worker&&"undefined"!=typeof OffscreenCanvas&&"undefined"!=typeof URL&&URL.createObjectURL&&"undefined"!=typeof Blob)try{var e="postMessage("+f.toString()+"("+[JSON.stringify(s),u.toString(),p.toString()].join(",")+"));",r=new Blob([e],{type:"text/javascript"}),a=new Worker(URL.createObjectURL(r),{name:"wpTestEmojiSupports"});return void(a.onmessage=function(e){c(n=e.data),a.terminate(),t(n)})}catch(e){}c(n=f(s,u,p))}t(n)}).then(function(e){for(var t in e)n.supports[t]=e[t],n.supports.everything=n.supports.everything&&n.supports[t],"flag"!==t&&(n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&n.supports[t]);n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&!n.supports.flag,n.DOMReady=!1,n.readyCallback=function(){n.DOMReady=!0}}).then(function(){return e}).then(function(){var e;n.supports.everything||(n.readyCallback(),(e=n.source||{}).concatemoji?t(e.concatemoji):e.wpemoji&&e.twemoji&&(t(e.twemoji),t(e.wpemoji)))}))}((window,document),window._wpemojiSettings);
</script>
<style id='wp-emoji-styles-inline-css'>

	img.wp-smiley, img.emoji {
		display: inline !important;
		border: none !important;
		box-shadow: none !important;
		height: 1em !important;
		width: 1em !important;
		margin: 0 0.07em !important;
		vertical-align: -0.1em !important;
		background: none !important;
		padding: 0 !important;
	}
</style>
<link rel='stylesheet' id='CF_block-cartflows-style-css-css' href='https://pay.desyne.pro/wp-content/plugins/cartflows/modules/gutenberg/build/style-blocks.css?ver=2.1.14' media='all' />
<link rel='stylesheet' id='CFP_block-cfp-style-css-css' href='https://pay.desyne.pro/wp-content/plugins/cartflows-pro/modules/gutenberg/build/style-blocks.css?ver=2.1.2' media='all' />
<style id='global-styles-inline-css'>
:root{--wp--preset--aspect-ratio--square: 1;--wp--preset--aspect-ratio--4-3: 4/3;--wp--preset--aspect-ratio--3-4: 3/4;--wp--preset--aspect-ratio--3-2: 3/2;--wp--preset--aspect-ratio--2-3: 2/3;--wp--preset--aspect-ratio--16-9: 16/9;--wp--preset--aspect-ratio--9-16: 9/16;--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #ffffff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 13px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 36px;--wp--preset--font-size--x-large: 42px;--wp--preset--font-family--inter: "Inter", sans-serif;--wp--preset--font-family--cardo: Cardo;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);}:root { --wp--style--global--content-size: 800px;--wp--style--global--wide-size: 1200px; }:where(body) { margin: 0; }.wp-site-blocks > .alignleft { float: left; margin-right: 2em; }.wp-site-blocks > .alignright { float: right; margin-left: 2em; }.wp-site-blocks > .aligncenter { justify-content: center; margin-left: auto; margin-right: auto; }:where(.wp-site-blocks) > * { margin-block-start: 24px; margin-block-end: 0; }:where(.wp-site-blocks) > :first-child { margin-block-start: 0; }:where(.wp-site-blocks) > :last-child { margin-block-end: 0; }:root { --wp--style--block-gap: 24px; }:root :where(.is-layout-flow) > :first-child{margin-block-start: 0;}:root :where(.is-layout-flow) > :last-child{margin-block-end: 0;}:root :where(.is-layout-flow) > *{margin-block-start: 24px;margin-block-end: 0;}:root :where(.is-layout-constrained) > :first-child{margin-block-start: 0;}:root :where(.is-layout-constrained) > :last-child{margin-block-end: 0;}:root :where(.is-layout-constrained) > *{margin-block-start: 24px;margin-block-end: 0;}:root :where(.is-layout-flex){gap: 24px;}:root :where(.is-layout-grid){gap: 24px;}.is-layout-flow > .alignleft{float: left;margin-inline-start: 0;margin-inline-end: 2em;}.is-layout-flow > .alignright{float: right;margin-inline-start: 2em;margin-inline-end: 0;}.is-layout-flow > .aligncenter{margin-left: auto !important;margin-right: auto !important;}.is-layout-constrained > .alignleft{float: left;margin-inline-start: 0;margin-inline-end: 2em;}.is-layout-constrained > .alignright{float: right;margin-inline-start: 2em;margin-inline-end: 0;}.is-layout-constrained > .aligncenter{margin-left: auto !important;margin-right: auto !important;}.is-layout-constrained > :where(:not(.alignleft):not(.alignright):not(.alignfull)){max-width: var(--wp--style--global--content-size);margin-left: auto !important;margin-right: auto !important;}.is-layout-constrained > .alignwide{max-width: var(--wp--style--global--wide-size);}body .is-layout-flex{display: flex;}.is-layout-flex{flex-wrap: wrap;align-items: center;}.is-layout-flex > :is(*, div){margin: 0;}body .is-layout-grid{display: grid;}.is-layout-grid > :is(*, div){margin: 0;}body{padding-top: 0px;padding-right: 0px;padding-bottom: 0px;padding-left: 0px;}a:where(:not(.wp-element-button)){text-decoration: underline;}:root :where(.wp-element-button, .wp-block-button__link){background-color: #32373c;border-width: 0;color: #fff;font-family: inherit;font-size: inherit;line-height: inherit;padding: calc(0.667em + 2px) calc(1.333em + 2px);text-decoration: none;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}.has-inter-font-family{font-family: var(--wp--preset--font-family--inter) !important;}.has-cardo-font-family{font-family: var(--wp--preset--font-family--cardo) !important;}
:root :where(.wp-block-pullquote){font-size: 1.5em;line-height: 1.6;}
</style>
<link rel='stylesheet' id='jquery-ui-css-css' href='https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css?ver=6.8.1' media='all' />
<link rel='stylesheet' id='select2-css' href='https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/css/select2.css?ver=9.9.5' media='all' />
<link rel='stylesheet' id='woocommerce-layout-css' href='https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/css/woocommerce-layout.css?ver=9.9.5' media='all' />
<link rel='stylesheet' id='woocommerce-smallscreen-css' href='https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/css/woocommerce-smallscreen.css?ver=9.9.5' media='only screen and (max-width: 768px)' />
<link rel='stylesheet' id='woocommerce-general-css' href='https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/css/woocommerce.css?ver=9.9.5' media='all' />
<style id='woocommerce-inline-inline-css'>
.woocommerce form .form-row .required { visibility: visible; }
</style>
<link rel='stylesheet' id='brands-styles-css' href='https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/css/brands.css?ver=9.9.5' media='all' />
<link rel='stylesheet' id='elementor-frontend-css' href='https://pay.desyne.pro/wp-content/plugins/elementor/assets/css/frontend.min.css?ver=3.30.0' media='all' />
<link rel='stylesheet' id='elementor-post-9-css' href='https://pay.desyne.pro/wp-content/uploads/elementor/css/post-9.css?ver=1751873039' media='all' />
<link rel='stylesheet' id='widget-heading-css' href='https://pay.desyne.pro/wp-content/plugins/elementor/assets/css/widget-heading.min.css?ver=3.30.0' media='all' />
<link rel='stylesheet' id='widget-icon-list-css' href='https://pay.desyne.pro/wp-content/plugins/elementor/assets/css/widget-icon-list.min.css?ver=3.30.0' media='all' />
<link rel='stylesheet' id='widget-icon-box-css' href='https://pay.desyne.pro/wp-content/plugins/elementor/assets/css/widget-icon-box.min.css?ver=3.30.0' media='all' />
<link rel='stylesheet' id='widget-image-css' href='https://pay.desyne.pro/wp-content/plugins/elementor/assets/css/widget-image.min.css?ver=3.30.0' media='all' />
<link rel='stylesheet' id='elementor-post-364-css' href='https://pay.desyne.pro/wp-content/uploads/elementor/css/post-364.css?ver=1752177663' media='all' />
<link rel='stylesheet' id='elementor-post-13-css' href='https://pay.desyne.pro/wp-content/uploads/elementor/css/post-13.css?ver=1751873063' media='all' />
<link rel='stylesheet' id='cartflows-elementor-style-css' href='https://pay.desyne.pro/wp-content/plugins/cartflows/modules/elementor/widgets-css/frontend.css?ver=2.1.14' media='all' />
<link rel='stylesheet' id='cartflows-pro-elementor-style-css' href='https://pay.desyne.pro/wp-content/plugins/cartflows-pro/modules/elementor/widgets-css/frontend.css?ver=2.1.2' media='all' />
<link rel='stylesheet' id='mercadopago_vars_css-css' href='https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/css/public/mp-vars.min.css?ver=8.2.0' media='all' />
<link rel='stylesheet' id='wc_mercadopago_checkout_components-css' href='https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/css/checkouts/mp-plugins-components.min.css?ver=8.2.0' media='all' />
<link rel='stylesheet' id='wcf-normalize-frontend-global-css' href='https://pay.desyne.pro/wp-content/plugins/cartflows/assets/css/cartflows-normalize.css?ver=2.1.14' media='all' />
<link rel='stylesheet' id='wcf-frontend-global-css' href='https://pay.desyne.pro/wp-content/plugins/cartflows/assets/css/frontend.css?ver=2.1.14' media='all' />
<style id='wcf-frontend-global-inline-css'>
:root { --e-global-color-wcfgcpprimarycolor: #f16334; --e-global-color-wcfgcpsecondarycolor: #000000; --e-global-color-wcfgcptextcolor: #4B5563; --e-global-color-wcfgcpaccentcolor: #1F2937;  }
</style>
<link rel='stylesheet' id='wcf-pro-frontend-global-css' href='https://pay.desyne.pro/wp-content/plugins/cartflows-pro/assets/css/frontend.css?ver=2.1.2' media='all' />
<link rel='stylesheet' id='wcf-checkout-template-css' href='https://pay.desyne.pro/wp-content/plugins/cartflows/assets/css/checkout-template.css?ver=2.1.14' media='all' />
<style id='wcf-checkout-template-inline-css'>

			.wcf-embed-checkout-form .woocommerce #payment #place_order:before{
				content: "\e902";
				font-family: "cartflows-icon" !important;
				margin-right: 10px;
				font-size: 16px;
				font-weight: 500;
				top: 0px;
    			position: relative;
				opacity: 1;
				display: block;
			}
</style>
<link rel='stylesheet' id='wcf-pro-checkout-css' href='https://pay.desyne.pro/wp-content/plugins/cartflows-pro/assets/css/checkout-styles.css?ver=2.1.2' media='all' />
<link rel='stylesheet' id='wcf-pro-multistep-checkout-css' href='https://pay.desyne.pro/wp-content/plugins/cartflows-pro/assets/css/multistep-checkout.css?ver=2.1.2' media='all' />
<link rel='stylesheet' id='dashicons-css' href='https://pay.desyne.pro/wp-includes/css/dashicons.min.css?ver=6.8.1' media='all' />
<link rel='stylesheet' id='elementor-gf-local-inter-css' href='https://pay.desyne.pro/wp-content/uploads/elementor/google-fonts/css/inter.css?ver=1743170576' media='all' />
<link rel='stylesheet' id='elementor-gf-local-publicsans-css' href='https://pay.desyne.pro/wp-content/uploads/elementor/google-fonts/css/publicsans.css?ver=1743170580' media='all' />
<script src="https://pay.desyne.pro/wp-includes/js/jquery/jquery.min.js?ver=3.7.1" id="jquery-core-js"></script>
<script src="https://pay.desyne.pro/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1" id="jquery-migrate-js"></script>
<script src="https://pay.desyne.pro/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6" id="wp-hooks-js"></script>
<script src="https://pay.desyne.pro/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6" id="wp-i18n-js"></script>
<script id="wp-i18n-js-after">
wp.i18n.setLocaleData( { 'text direction\u0004ltr': [ 'ltr' ] } );
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/jquery-blockui/jquery.blockUI.min.js?ver=2.7.0-wc.9.9.5" id="jquery-blockui-js" defer data-wp-strategy="defer"></script>
<script id="wc-add-to-cart-js-extra">
var wc_add_to_cart_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/step\/elemenia\/?wc-ajax=%%endpoint%%&wcf_checkout_id=364","i18n_view_cart":"Ver carrinho","cart_url":"https:\/\/pay.desyne.pro","is_cart":"","cart_redirect_after_add":"no"};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/frontend/add-to-cart.min.js?ver=9.9.5" id="wc-add-to-cart-js" defer data-wp-strategy="defer"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/selectWoo/selectWoo.full.min.js?ver=1.0.9-wc.9.9.5" id="selectWoo-js" defer data-wp-strategy="defer"></script>
<script id="zxcvbn-async-js-extra">
var _zxcvbnSettings = {"src":"https:\/\/pay.desyne.pro\/wp-includes\/js\/zxcvbn.min.js"};
</script>
<script src="https://pay.desyne.pro/wp-includes/js/zxcvbn-async.min.js?ver=1.0" id="zxcvbn-async-js"></script>
<script id="password-strength-meter-js-extra">
var pwsL10n = {"unknown":"N\u00edvel de seguran\u00e7a da senha desconhecido","short":"Muito fraca","bad":"Fraca","good":"M\u00e9dio","strong":"Forte","mismatch":"Incompat\u00edvel"};
</script>
<script id="password-strength-meter-js-translations">
( function( domain, translations ) {
	var localeData = translations.locale_data[ domain ] || translations.locale_data.messages;
	localeData[""].domain = domain;
	wp.i18n.setLocaleData( localeData, domain );
} )( "default", {"translation-revision-date":"2025-05-05 11:53:58+0000","generator":"GlotPress\/4.0.1","domain":"messages","locale_data":{"messages":{"":{"domain":"messages","plural-forms":"nplurals=2; plural=n > 1;","lang":"pt_BR"},"%1$s is deprecated since version %2$s! Use %3$s instead. Please consider writing more inclusive code.":["%1$s est\u00e1 obsoleta desde a vers\u00e3o %2$s. Use %3$s como alternativa. Considere escrever um c\u00f3digo mais inclusivo."]}},"comment":{"reference":"wp-admin\/js\/password-strength-meter.js"}} );
</script>
<script src="https://pay.desyne.pro/wp-admin/js/password-strength-meter.min.js?ver=6.8.1" id="password-strength-meter-js"></script>
<script id="wc-password-strength-meter-js-extra">
var wc_password_strength_meter_params = {"min_password_strength":"3","stop_checkout":"","i18n_password_error":"Digite uma senha segura.","i18n_password_hint":"Dica: A senha deve ter pelo menos doze caracteres. Para torn\u00e1-la mais forte, use letras mai\u00fasculas e min\u00fasculas, n\u00fameros e s\u00edmbolos como ! \" ? $ % ^ & )."};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/frontend/password-strength-meter.min.js?ver=9.9.5" id="wc-password-strength-meter-js" defer data-wp-strategy="defer"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/js-cookie/js.cookie.min.js?ver=2.1.4-wc.9.9.5" id="js-cookie-js" defer data-wp-strategy="defer"></script>
<script id="woocommerce-js-extra">
var woocommerce_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/step\/elemenia\/?wc-ajax=%%endpoint%%&wcf_checkout_id=364","i18n_password_show":"Mostrar senha","i18n_password_hide":"Ocultar senha"};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/frontend/woocommerce.min.js?ver=9.9.5" id="woocommerce-js" defer data-wp-strategy="defer"></script>
<script id="wc-country-select-js-extra">
var wc_country_select_params = {"countries":"{\"AF\":[],\"AL\":{\"AL-01\":\"Berat\",\"AL-09\":\"Dib\\u00ebr\",\"AL-02\":\"Durr\\u00ebs\",\"AL-03\":\"Elbasan\",\"AL-04\":\"Fier\",\"AL-05\":\"Gjirokast\\u00ebr\",\"AL-06\":\"Kor\\u00e7\\u00eb\",\"AL-07\":\"Kuk\\u00ebs\",\"AL-08\":\"Lezh\\u00eb\",\"AL-10\":\"Shkod\\u00ebr\",\"AL-11\":\"Tirana\",\"AL-12\":\"Vlor\\u00eb\"},\"AO\":{\"BGO\":\"Bengo\",\"BLU\":\"Benguela\",\"BIE\":\"Bi\\u00e9\",\"CAB\":\"Cabinda\",\"CNN\":\"Cunene\",\"HUA\":\"Huambo\",\"HUI\":\"Hu\\u00edla\",\"CCU\":\"Kuando Kubango\",\"CNO\":\"Kwanza-Norte\",\"CUS\":\"Kwanza-Sul\",\"LUA\":\"Luanda\",\"LNO\":\"Lunda-Norte\",\"LSU\":\"Lunda-Sul\",\"MAL\":\"Malanje\",\"MOX\":\"Moxico\",\"NAM\":\"Namibe\",\"UIG\":\"U\\u00edge\",\"ZAI\":\"Zaire\"},\"AR\":{\"C\":\"Cidade Aut\\u00f4noma de Buenos Aires\",\"B\":\"Buenos Aires\",\"K\":\"Catamarca\",\"H\":\"Chaco\",\"U\":\"Chubut\",\"X\":\"C\\u00f3rdoba\",\"W\":\"Corrientes\",\"E\":\"Entre R\\u00edos\",\"P\":\"Formosa\",\"Y\":\"Jujuy\",\"L\":\"La Pampa\",\"F\":\"La Rioja\",\"M\":\"Mendoza\",\"N\":\"Misiones\",\"Q\":\"Neuqu\\u00e9n\",\"R\":\"R\\u00edo Negro\",\"A\":\"Salta\",\"J\":\"San Juan\",\"D\":\"San Luis\",\"Z\":\"Santa Cruz\",\"S\":\"Santa F\\u00e9\",\"G\":\"Santiago del Estero\",\"V\":\"Tierra del Fuego\",\"T\":\"Tucum\\u00e1n\"},\"AT\":[],\"AU\":{\"ACT\":\"Territ\\u00f3rio da Capital Australiana\",\"NSW\":\"Nova Gales do Sul\",\"NT\":\"Territ\\u00f3rio do Norte\",\"QLD\":\"Queensland\",\"SA\":\"Austr\\u00e1lia Meridional\",\"TAS\":\"Tasm\\u00e2nia\",\"VIC\":\"Victoria\",\"WA\":\"Austr\\u00e1lia Ocidental\"},\"AX\":[],\"BD\":{\"BD-05\":\"Bagerhat\",\"BD-01\":\"Bandarban\",\"BD-02\":\"Barguna\",\"BD-06\":\"Barishal\",\"BD-07\":\"Bhola\",\"BD-03\":\"Bogura\",\"BD-04\":\"Brahmanbaria\",\"BD-09\":\"Chandpur\",\"BD-10\":\"Chattogram\",\"BD-12\":\"Chuadanga\",\"BD-11\":\"Cox's Bazar\",\"BD-08\":\"Cumilla\",\"BD-13\":\"Dhaka\",\"BD-14\":\"Dinajpur\",\"BD-15\":\"Faridpur \",\"BD-16\":\"Feni\",\"BD-19\":\"Gaibandha\",\"BD-18\":\"Gazipur\",\"BD-17\":\"Gopalganj\",\"BD-20\":\"Habiganj\",\"BD-21\":\"Jamalpur\",\"BD-22\":\"Jashore\",\"BD-25\":\"Jhalokati\",\"BD-23\":\"Jhenaidah\",\"BD-24\":\"Joypurhat\",\"BD-29\":\"Khagrachhari\",\"BD-27\":\"Khulna\",\"BD-26\":\"Kishoreganj\",\"BD-28\":\"Kurigram\",\"BD-30\":\"Kushtia\",\"BD-31\":\"Lakshmipur\",\"BD-32\":\"Lalmonirhat\",\"BD-36\":\"Madaripur\",\"BD-37\":\"Magura\",\"BD-33\":\"Manikganj \",\"BD-39\":\"Meherpur\",\"BD-38\":\"Moulvibazar\",\"BD-35\":\"Munshiganj\",\"BD-34\":\"Mymensingh\",\"BD-48\":\"Naogaon\",\"BD-43\":\"Narail\",\"BD-40\":\"Narayanganj\",\"BD-42\":\"Narsingdi\",\"BD-44\":\"Natore\",\"BD-45\":\"Nawabganj\",\"BD-41\":\"Netrakona\",\"BD-46\":\"Nilphamari\",\"BD-47\":\"Noakhali\",\"BD-49\":\"Pabna\",\"BD-52\":\"Panchagarh\",\"BD-51\":\"Patuakhali\",\"BD-50\":\"Pirojpur\",\"BD-53\":\"Rajbari\",\"BD-54\":\"Rajshahi\",\"BD-56\":\"Rangamati\",\"BD-55\":\"Rangpur\",\"BD-58\":\"Satkhira\",\"BD-62\":\"Shariatpur\",\"BD-57\":\"Sherpur\",\"BD-59\":\"Sirajganj\",\"BD-61\":\"Sunamganj\",\"BD-60\":\"Sylhet\",\"BD-63\":\"Tangail\",\"BD-64\":\"Thakurgaon\"},\"BE\":[],\"BG\":{\"BG-01\":\"Blagoevgrad\",\"BG-02\":\"Burgas\",\"BG-08\":\"Dobrich\",\"BG-07\":\"Gabrovo\",\"BG-26\":\"Haskovo\",\"BG-09\":\"Kardzhali\",\"BG-10\":\"Kyustendil\",\"BG-11\":\"Lovech\",\"BG-12\":\"Montana\",\"BG-13\":\"Pazardzhik\",\"BG-14\":\"Pernik\",\"BG-15\":\"Pleven\",\"BG-16\":\"Plovdiv\",\"BG-17\":\"Razgrad\",\"BG-18\":\"Ruse\",\"BG-27\":\"Shumen\",\"BG-19\":\"Silistra\",\"BG-20\":\"Sliven\",\"BG-21\":\"Smolyan\",\"BG-23\":\"Distrito de Sofia\",\"BG-22\":\"Sofia\",\"BG-24\":\"Stara Zagora\",\"BG-25\":\"Targovishte\",\"BG-03\":\"Varna\",\"BG-04\":\"Veliko Tarnovo\",\"BG-05\":\"Vidin\",\"BG-06\":\"Vratsa\",\"BG-28\":\"Yambol\"},\"BH\":[],\"BI\":[],\"BJ\":{\"AL\":\"Alibori\",\"AK\":\"Atakora\",\"AQ\":\"Atlantique\",\"BO\":\"Borgou\",\"CO\":\"Collines\",\"KO\":\"Kouffo\",\"DO\":\"Donga\",\"LI\":\"Littoral\",\"MO\":\"Mono\",\"OU\":\"Ou\\u00e9m\\u00e9\",\"PL\":\"Plateau\",\"ZO\":\"Zou\"},\"BO\":{\"BO-B\":\"Beni\",\"BO-H\":\"Chuquisaca\",\"BO-C\":\"Cochabamba\",\"BO-L\":\"La Paz\",\"BO-O\":\"Oruro\",\"BO-N\":\"Pando\",\"BO-P\":\"Potos\\u00ed\",\"BO-S\":\"Santa Cruz\",\"BO-T\":\"Tarija\"},\"BR\":{\"AC\":\"Acre\",\"AL\":\"Alagoas\",\"AP\":\"Amap\\u00e1\",\"AM\":\"Amazonas\",\"BA\":\"Bahia\",\"CE\":\"Cear\\u00e1\",\"DF\":\"Distrito Federal\",\"ES\":\"Esp\\u00edrito Santo\",\"GO\":\"Goi\\u00e1s\",\"MA\":\"Maranh\\u00e3o\",\"MT\":\"Mato Grosso\",\"MS\":\"Mato Grosso do Sul\",\"MG\":\"Minas Gerais\",\"PA\":\"Par\\u00e1\",\"PB\":\"Para\\u00edba\",\"PR\":\"Paran\\u00e1\",\"PE\":\"Pernambuco\",\"PI\":\"Piau\\u00ed\",\"RJ\":\"Rio de Janeiro\",\"RN\":\"Rio Grande do Norte\",\"RS\":\"Rio Grande do Sul\",\"RO\":\"Rond\\u00f4nia\",\"RR\":\"Roraima\",\"SC\":\"Santa Catarina\",\"SP\":\"S\\u00e3o Paulo\",\"SE\":\"Sergipe\",\"TO\":\"Tocantins\"},\"CA\":{\"AB\":\"Alberta\",\"BC\":\"Col\\u00fambia Brit\\u00e2nica\",\"MB\":\"Manitoba\",\"NB\":\"Nova Brunswick\",\"NL\":\"Terra Nova e Labrador\",\"NT\":\"Territ\\u00f3rios do Noroeste\",\"NS\":\"Nova Esc\\u00f3cia\",\"NU\":\"Nunavut\",\"ON\":\"Ont\\u00e1rio\",\"PE\":\"Ilha do Pr\\u00edncipe Eduardo\",\"QC\":\"Quebec\",\"SK\":\"Saskatchewan\",\"YT\":\"Yukon\"},\"CH\":{\"AG\":\"Aargau\",\"AR\":\"Appenzell Ausserrhoden\",\"AI\":\"Appenzell Innerrhoden\",\"BL\":\"Basel-Landschaft\",\"BS\":\"Basel-Stadt\",\"BE\":\"Bern\",\"FR\":\"Fribourg\",\"GE\":\"Geneva\",\"GL\":\"Glarus\",\"GR\":\"Graub\\u00fcnden\",\"JU\":\"Jura\",\"LU\":\"Luzern\",\"NE\":\"Neuch\\u00e2tel\",\"NW\":\"Nidwalden\",\"OW\":\"Obwalden\",\"SH\":\"Schaffhausen\",\"SZ\":\"Schwyz\",\"SO\":\"Solothurn\",\"SG\":\"St. Gallen\",\"TG\":\"Thurgau\",\"TI\":\"Ticino\",\"UR\":\"Uri\",\"VS\":\"Valais\",\"VD\":\"Vaud\",\"ZG\":\"Zug\",\"ZH\":\"Z\\u00fcrich\"},\"CL\":{\"CL-AI\":\"Ais\\u00e9n del General Carlos Iba\\u00f1ez del Campo\",\"CL-AN\":\"Antofagasta\",\"CL-AP\":\"Arica e Parinacota\",\"CL-AR\":\"La Araucan\\u00eda\",\"CL-AT\":\"Atacama\",\"CL-BI\":\"Biob\\u00edo\",\"CL-CO\":\"Coquimbo\",\"CL-LI\":\"Libertador General Bernardo O'Higgins\",\"CL-LL\":\"Los Lagos\",\"CL-LR\":\"Los R\\u00edos\",\"CL-MA\":\"Magallanes\",\"CL-ML\":\"Maule\",\"CL-NB\":\"\\u00d1uble\",\"CL-RM\":\"Regi\\u00e3o Metropolitana de Santiago\",\"CL-TA\":\"Tarapac\\u00e1\",\"CL-VS\":\"Valpara\\u00edso\"},\"CN\":{\"CN1\":\"Yunnan \\\/ \\u4e91\\u5357\",\"CN2\":\"Beijing \\\/ \\u5317\\u4eac\",\"CN3\":\"Tianjin \\\/ \\u5929\\u6d25\",\"CN4\":\"Hebei \\\/ \\u6cb3\\u5317\",\"CN5\":\"Shanxi \\\/ \\u5c71\\u897f\",\"CN6\":\"Inner Mongolia \\\/ \\u5167\\u8499\\u53e4\",\"CN7\":\"Liaoning \\\/ \\u8fbd\\u5b81\",\"CN8\":\"Jilin \\\/ \\u5409\\u6797\",\"CN9\":\"Heilongjiang \\\/ \\u9ed1\\u9f99\\u6c5f\",\"CN10\":\"Shanghai \\\/ \\u4e0a\\u6d77\",\"CN11\":\"Jiangsu \\\/ \\u6c5f\\u82cf\",\"CN12\":\"Zhejiang \\\/ \\u6d59\\u6c5f\",\"CN13\":\"Anhui \\\/ \\u5b89\\u5fbd\",\"CN14\":\"Fujian \\\/ \\u798f\\u5efa\",\"CN15\":\"Jiangxi \\\/ \\u6c5f\\u897f\",\"CN16\":\"Shandong \\\/ \\u5c71\\u4e1c\",\"CN17\":\"Henan \\\/ \\u6cb3\\u5357\",\"CN18\":\"Hubei \\\/ \\u6e56\\u5317\",\"CN19\":\"Hunan \\\/ \\u6e56\\u5357\",\"CN20\":\"Guangdong \\\/ \\u5e7f\\u4e1c\",\"CN21\":\"Guangxi Zhuang \\\/ \\u5e7f\\u897f\\u58ee\\u65cf\",\"CN22\":\"Hainan \\\/ \\u6d77\\u5357\",\"CN23\":\"Chongqing \\\/ \\u91cd\\u5e86\",\"CN24\":\"Sichuan \\\/ \\u56db\\u5ddd\",\"CN25\":\"Guizhou \\\/ \\u8d35\\u5dde\",\"CN26\":\"Shaanxi \\\/ \\u9655\\u897f\",\"CN27\":\"Gansu \\\/ \\u7518\\u8083\",\"CN28\":\"Qinghai \\\/ \\u9752\\u6d77\",\"CN29\":\"Ningxia Hui \\\/ \\u5b81\\u590f\",\"CN30\":\"Macao \\\/ \\u6fb3\\u95e8\",\"CN31\":\"Tibet \\\/ \\u897f\\u85cf\",\"CN32\":\"Xinjiang \\\/ \\u65b0\\u7586\"},\"CO\":{\"CO-AMA\":\"Amazonas\",\"CO-ANT\":\"Antioquia\",\"CO-ARA\":\"Arauca\",\"CO-ATL\":\"Atl\\u00e1ntico\",\"CO-BOL\":\"Bol\\u00edvar\",\"CO-BOY\":\"Boyac\\u00e1\",\"CO-CAL\":\"Caldas\",\"CO-CAQ\":\"Caquet\\u00e1\",\"CO-CAS\":\"Casanare\",\"CO-CAU\":\"Cauca\",\"CO-CES\":\"Cesar\",\"CO-CHO\":\"Choc\\u00f3\",\"CO-COR\":\"C\\u00f3rdoba\",\"CO-CUN\":\"Cundinamarca\",\"CO-DC\":\"Distrito Capital\",\"CO-GUA\":\"Guain\\u00eda\",\"CO-GUV\":\"Guaviare\",\"CO-HUI\":\"Huila\",\"CO-LAG\":\"La Guajira\",\"CO-MAG\":\"Magdalena\",\"CO-MET\":\"Meta\",\"CO-NAR\":\"Nari\\u00f1o\",\"CO-NSA\":\"Norte de Santander\",\"CO-PUT\":\"Putumayo\",\"CO-QUI\":\"Quind\\u00edo\",\"CO-RIS\":\"Risaralda\",\"CO-SAN\":\"Santander\",\"CO-SAP\":\"San Andr\\u00e9s & Providencia\",\"CO-SUC\":\"Sucre\",\"CO-TOL\":\"Tolima\",\"CO-VAC\":\"Valle del Cauca\",\"CO-VAU\":\"Vaup\\u00e9s\",\"CO-VID\":\"Vichada\"},\"CR\":{\"CR-A\":\"Alajuela\",\"CR-C\":\"Cartago\",\"CR-G\":\"Guanacaste\",\"CR-H\":\"Heredia\",\"CR-L\":\"Lim\\u00f3n\",\"CR-P\":\"Puntarenas\",\"CR-SJ\":\"San Jos\\u00e9\"},\"CZ\":[],\"DE\":{\"DE-BW\":\"Baden-W\\u00fcrttemberg\",\"DE-BY\":\"Baviera\",\"DE-BE\":\"Berlim\",\"DE-BB\":\"Brandemburgo\",\"DE-HB\":\"Bremen\",\"DE-HH\":\"Hamburgo\",\"DE-HE\":\"Hesse\",\"DE-MV\":\"Meclemburgo-Pomer\\u00e2nia Ocidental\",\"DE-NI\":\"Baixa Sax\\u00f4nia\",\"DE-NW\":\"Ren\\u00e2nia do Norte-Vestf\\u00e1lia\",\"DE-RP\":\"Ren\\u00e2nia-Palatinado\",\"DE-SL\":\"Sarre\",\"DE-SN\":\"Sax\\u00f4nia\",\"DE-ST\":\"Sax\\u00f4nia-Anhalt\",\"DE-SH\":\"Schleswig-Holstein\",\"DE-TH\":\"Tur\\u00edngia\"},\"DK\":[],\"DO\":{\"DO-01\":\"Distrito Nacional\",\"DO-02\":\"Azua\",\"DO-03\":\"Bahoruco\",\"DO-04\":\"Barahona\",\"DO-33\":\"Cibao Nordeste\",\"DO-34\":\"Cibao Noroeste\",\"DO-35\":\"Cibao Norte\",\"DO-36\":\"Cibao Sur\",\"DO-05\":\"Dajab\\u00f3n\",\"DO-06\":\"Duarte\",\"DO-08\":\"El Seibo\",\"DO-37\":\"El Valle\",\"DO-07\":\"El\\u00edas Pi\\u00f1a\",\"DO-38\":\"Enriquillo\",\"DO-09\":\"Espaillat\",\"DO-30\":\"Hato Mayor\",\"DO-19\":\"Hermanas Mirabal\",\"DO-39\":\"Hig\\u00fcamo\",\"DO-10\":\"Independencia\",\"DO-11\":\"La Altagracia\",\"DO-12\":\"La Romana\",\"DO-13\":\"La Vega\",\"DO-14\":\"Mar\\u00eda Trinidad S\\u00e1nchez\",\"DO-28\":\"Monse\\u00f1or Nouel\",\"DO-15\":\"Monte Cristi\",\"DO-29\":\"Monte Plata\",\"DO-40\":\"Ozama\",\"DO-16\":\"Pedernales\",\"DO-17\":\"Peravia\",\"DO-18\":\"Puerto Plata\",\"DO-20\":\"Saman\\u00e1\",\"DO-21\":\"San Crist\\u00f3bal\",\"DO-31\":\"San Jos\\u00e9 de Ocoa\",\"DO-22\":\"San Juan\",\"DO-23\":\"San Pedro de Macor\\u00eds\",\"DO-24\":\"S\\u00e1nchez Ram\\u00edrez\",\"DO-25\":\"Santiago\",\"DO-26\":\"Santiago Rodr\\u00edguez\",\"DO-32\":\"Santo Domingo\",\"DO-41\":\"Valdesia\",\"DO-27\":\"Valverde\",\"DO-42\":\"Yuma\"},\"DZ\":{\"DZ-01\":\"Adrar\",\"DZ-02\":\"Chlef\",\"DZ-03\":\"Laghouat\",\"DZ-04\":\"Oum El Bouaghi\",\"DZ-05\":\"Batna\",\"DZ-06\":\"B\\u00e9ja\\u00efa\",\"DZ-07\":\"Biskra\",\"DZ-08\":\"B\\u00e9char\",\"DZ-09\":\"Blida\",\"DZ-10\":\"Bouira\",\"DZ-11\":\"Tamanghasset\",\"DZ-12\":\"T\\u00e9bessa\",\"DZ-13\":\"Tlemcen\",\"DZ-14\":\"Tiaret\",\"DZ-15\":\"Tizi Ouzou\",\"DZ-16\":\"Algiers\",\"DZ-17\":\"Djelfa\",\"DZ-18\":\"Jijel\",\"DZ-19\":\"S\\u00e9tif\",\"DZ-20\":\"Sa\\u00efda\",\"DZ-21\":\"Skikda\",\"DZ-22\":\"Sidi Bel Abb\\u00e8s\",\"DZ-23\":\"Annaba\",\"DZ-24\":\"Guelma\",\"DZ-25\":\"Constantine\",\"DZ-26\":\"M\\u00e9d\\u00e9a\",\"DZ-27\":\"Mostaganem\",\"DZ-28\":\"M\\u2019Sila\",\"DZ-29\":\"Mascara\",\"DZ-30\":\"Ouargla\",\"DZ-31\":\"Oran\",\"DZ-32\":\"El Bayadh\",\"DZ-33\":\"Illizi\",\"DZ-34\":\"Bordj Bou Arr\\u00e9ridj\",\"DZ-35\":\"Boumerd\\u00e8s\",\"DZ-36\":\"El Tarf\",\"DZ-37\":\"Tindouf\",\"DZ-38\":\"Tissemsilt\",\"DZ-39\":\"El Oued\",\"DZ-40\":\"Khenchela\",\"DZ-41\":\"Souk Ahras\",\"DZ-42\":\"Tipasa\",\"DZ-43\":\"Mila\",\"DZ-44\":\"A\\u00efn Defla\",\"DZ-45\":\"Naama\",\"DZ-46\":\"A\\u00efn T\\u00e9mouchent\",\"DZ-47\":\"Gharda\\u00efa\",\"DZ-48\":\"Relizane\"},\"EE\":[],\"EC\":{\"EC-A\":\"Azuay\",\"EC-B\":\"Bol\\u00edvar\",\"EC-F\":\"Ca\\u00f1ar\",\"EC-C\":\"Carchi\",\"EC-H\":\"Chimborazo\",\"EC-X\":\"Cotopaxi\",\"EC-O\":\"El Oro\",\"EC-E\":\"Esmeraldas\",\"EC-W\":\"Gal\\u00e1pagos\",\"EC-G\":\"Guayas\",\"EC-I\":\"Imbabura\",\"EC-L\":\"Loja\",\"EC-R\":\"Los R\\u00edos\",\"EC-M\":\"Manab\\u00ed\",\"EC-S\":\"Morona-Santiago\",\"EC-N\":\"Napo\",\"EC-D\":\"Orellana\",\"EC-Y\":\"Pastaza\",\"EC-P\":\"Pichincha\",\"EC-SE\":\"Santa Elena\",\"EC-SD\":\"Santo Domingo de los Ts\\u00e1chilas\",\"EC-U\":\"Sucumb\\u00edos\",\"EC-T\":\"Tungurahua\",\"EC-Z\":\"Zamora-Chinchipe\"},\"EG\":{\"EGALX\":\"Alexandria\",\"EGASN\":\"Assu\\u00e3\",\"EGAST\":\"Assiute\",\"EGBA\":\"Mar Vermelho\",\"EGBH\":\"Boeira\",\"EGBNS\":\"Beni Suefe\",\"EGC\":\"Cairo\",\"EGDK\":\"Dacalia\",\"EGDT\":\"Damieta\",\"EGFYM\":\"Faium\",\"EGGH\":\"Ocidental\",\"EGGZ\":\"Guiz\\u00e9\",\"EGIS\":\"Isma\\u00edlia\",\"EGJS\":\"Sinai do Sul\",\"EGKB\":\"Caliubia\",\"EGKFS\":\"Cafrel Xeique\",\"EGKN\":\"Quena\",\"EGLX\":\"Luxor\",\"EGMN\":\"Minia\",\"EGMNF\":\"Monufia\",\"EGMT\":\"Matru\",\"EGPTS\":\"Porto Sa\\u00edde\",\"EGSHG\":\"Soague\",\"EGSHR\":\"Xarquia\",\"EGSIN\":\"Sinai do Norte\",\"EGSUZ\":\"Suez\",\"EGWAD\":\"Vale Novo\"},\"ES\":{\"C\":\"A Coru\\u00f1a\",\"VI\":\"Araba\\\/\\u00c1lava\",\"AB\":\"Albacete\",\"A\":\"Alicante\",\"AL\":\"Almer\\u00eda\",\"O\":\"Asturias\",\"AV\":\"\\u00c1vila\",\"BA\":\"Badajoz\",\"PM\":\"Baleares\",\"B\":\"Barcelona\",\"BU\":\"Burgos\",\"CC\":\"C\\u00e1ceres\",\"CA\":\"C\\u00e1diz\",\"S\":\"Cantabria\",\"CS\":\"Castell\\u00f3n\",\"CE\":\"Ceuta\",\"CR\":\"Ciudad Real\",\"CO\":\"C\\u00f3rdoba\",\"CU\":\"Cuenca\",\"GI\":\"Girona\",\"GR\":\"Granada\",\"GU\":\"Guadalajara\",\"SS\":\"Gipuzkoa\",\"H\":\"Huelva\",\"HU\":\"Huesca\",\"J\":\"Ja\\u00e9n\",\"LO\":\"La Rioja\",\"GC\":\"Las Palmas\",\"LE\":\"Le\\u00f3n\",\"L\":\"Lleida\",\"LU\":\"Lugo\",\"M\":\"Madrid\",\"MA\":\"M\\u00e1laga\",\"ML\":\"Melilla\",\"MU\":\"Murcia\",\"NA\":\"Navarra\",\"OR\":\"Ourense\",\"P\":\"Palencia\",\"PO\":\"Pontevedra\",\"SA\":\"Salamanca\",\"TF\":\"Santa Cruz de Tenerife\",\"SG\":\"Segovia\",\"SE\":\"Sevilla\",\"SO\":\"Soria\",\"T\":\"Tarragona\",\"TE\":\"Teruel\",\"TO\":\"Toledo\",\"V\":\"Valencia\",\"VA\":\"Valladolid\",\"BI\":\"Biscaia\",\"ZA\":\"Zamora\",\"Z\":\"Zaragoza\"},\"ET\":[],\"FI\":[],\"FR\":[],\"GF\":[],\"GH\":{\"AF\":\"Ahafo\",\"AH\":\"Ashanti\",\"BA\":\"Brong-Ahafo\",\"BO\":\"Bono\",\"BE\":\"Bono East\",\"CP\":\"Central\",\"EP\":\"Oriental\",\"AA\":\"Greater Accra\",\"NE\":\"Nordeste\",\"NP\":\"Norte\",\"OT\":\"Oti\",\"SV\":\"Savannah\",\"UE\":\"Upper East\",\"UW\":\"Upper West\",\"TV\":\"Volta\",\"WP\":\"Ocidental\",\"WN\":\"Norte ocidental\"},\"GP\":[],\"GR\":{\"I\":\"Attica\",\"A\":\"Maced\\u00f4nia Oriental e Tr\\u00e1cia\",\"B\":\"Maced\\u00f4nia Central\",\"C\":\"Maced\\u00f4nia do Norte\",\"D\":\"Epirus\",\"E\":\"Thessaly\",\"F\":\"Ionian Islands\",\"G\":\"Gr\\u00e9cia Ocidental\",\"H\":\"Gr\\u00e9cia Central\",\"J\":\"Peloponnese\",\"K\":\"Egeu do Norte\",\"L\":\"Egeu do Sul\",\"M\":\"Creta\"},\"GT\":{\"GT-AV\":\"Alta Verapaz\",\"GT-BV\":\"Baja Verapaz\",\"GT-CM\":\"Chimaltenango\",\"GT-CQ\":\"Chiquimula\",\"GT-PR\":\"El Progreso\",\"GT-ES\":\"Escuintla\",\"GT-GU\":\"Guatemala\",\"GT-HU\":\"Huehuetenango\",\"GT-IZ\":\"Izabal\",\"GT-JA\":\"Jalapa\",\"GT-JU\":\"Jutiapa\",\"GT-PE\":\"Pet\\u00e9n\",\"GT-QZ\":\"Quetzaltenango\",\"GT-QC\":\"Quich\\u00e9\",\"GT-RE\":\"Retalhuleu\",\"GT-SA\":\"Sacatep\\u00e9quez\",\"GT-SM\":\"San Marcos\",\"GT-SR\":\"Santa Rosa\",\"GT-SO\":\"Solol\\u00e1\",\"GT-SU\":\"Suchitep\\u00e9quez\",\"GT-TO\":\"Totonicap\\u00e1n\",\"GT-ZA\":\"Zacapa\"},\"HK\":{\"HONG KONG\":\"Ilha de Hong Kong\",\"KOWLOON\":\"Kowloon\",\"NEW TERRITORIES\":\"Novos Territ\\u00f3rios\"},\"HN\":{\"HN-AT\":\"Atl\\u00e1ntida\",\"HN-IB\":\"Ilhas da Ba\\u00eda\",\"HN-CH\":\"Choluteca\",\"HN-CL\":\"Col\\u00f3n\",\"HN-CM\":\"Comayagua\",\"HN-CP\":\"Cop\\u00e1n\",\"HN-CR\":\"Cort\\u00e9s\",\"HN-EP\":\"El Para\\u00edso\",\"HN-FM\":\"Francisco Moraz\\u00e1n\",\"HN-GD\":\"Gracias a Dios\",\"HN-IN\":\"Intibuc\\u00e1\",\"HN-LE\":\"Lempira\",\"HN-LP\":\"La Paz\",\"HN-OC\":\"Ocotepeque\",\"HN-OL\":\"Olancho\",\"HN-SB\":\"Santa B\\u00e1rbara\",\"HN-VA\":\"Valle\",\"HN-YO\":\"Yoro\"},\"HR\":{\"HR-01\":\"Condado de Zagreb\",\"HR-02\":\"Condado de Krapina-Zagorje\",\"HR-03\":\"Condado de Sisak-Moslavina\",\"HR-04\":\"Condado de Karlovac\",\"HR-05\":\"Condado de Vara\\u017edin\",\"HR-06\":\"Condado de Koprivnica-Kri\\u017eevci\",\"HR-07\":\"Condado de Bjelovar-Bilogora\",\"HR-08\":\"Condado de Primorje-Gorski Kotar\",\"HR-09\":\"Condado de Lika-Senj\",\"HR-10\":\"Condado de Virovitica-Podravina\",\"HR-11\":\"Condado de Po\\u017eega-Eslav\\u00f4nia\",\"HR-12\":\"Condado de Brod-Posavina\",\"HR-13\":\"Condado de Zadar\",\"HR-14\":\"Condado de Osijek-Baranja\",\"HR-15\":\"Condado de \\u0160ibenik-Knin\",\"HR-16\":\"Condado de Vukovar-Srijem\",\"HR-17\":\"Condado de Split-Dalm\\u00e1cia\",\"HR-18\":\"Condado de \\u00cdstria\",\"HR-19\":\"Condado de Dubrovnik-Neretva\",\"HR-20\":\"Condado de Me\\u0111imurje\",\"HR-21\":\"Cidade de Zagreb\"},\"HU\":{\"BK\":\"B\\u00e1cs-Kiskun\",\"BE\":\"B\\u00e9k\\u00e9s\",\"BA\":\"Baranya\",\"BZ\":\"Borsod-Aba\\u00faj-Zempl\\u00e9n\",\"BU\":\"Budapeste\",\"CS\":\"Csongr\\u00e1d-Csan\\u00e1d\",\"FE\":\"Fej\\u00e9r\",\"GS\":\"Gy\\u0151r-Moson-Sopron\",\"HB\":\"Hajd\\u00fa-Bihar\",\"HE\":\"Heves\",\"JN\":\"J\\u00e1sz-Nagykun-Szolnok\",\"KE\":\"Kom\\u00e1rom-Esztergom\",\"NO\":\"N\\u00f3gr\\u00e1d\",\"PE\":\"Pest\",\"SO\":\"Somogy\",\"SZ\":\"Szabolcs-Szatm\\u00e1r-Bereg\",\"TO\":\"Tolna\",\"VA\":\"Vas\",\"VE\":\"Veszpr\\u00e9m\",\"ZA\":\"Zala\"},\"ID\":{\"AC\":\"Daerah Istimewa Aceh\",\"SU\":\"Sumatera Utara\",\"SB\":\"Sumatera Barat\",\"RI\":\"Riau\",\"KR\":\"Kepulauan Riau\",\"JA\":\"Jambi\",\"SS\":\"Sumatera Selatan\",\"BB\":\"Bangka Belitung\",\"BE\":\"Bengkulu\",\"LA\":\"Lampung\",\"JK\":\"DKI Jakarta\",\"JB\":\"Jawa Barat\",\"BT\":\"Banten\",\"JT\":\"Jawa Tengah\",\"JI\":\"Jawa Timur\",\"YO\":\"Daerah Istimewa Yogyakarta\",\"BA\":\"Bali\",\"NB\":\"Nusa Tenggara Barat\",\"NT\":\"Nusa Tenggara Timur\",\"KB\":\"Kalimantan Barat\",\"KT\":\"Kalimantan Tengah\",\"KI\":\"Kalimantan Timur\",\"KS\":\"Kalimantan Selatan\",\"KU\":\"Kalimantan Utara\",\"SA\":\"Sulawesi Utara\",\"ST\":\"Sulawesi Tengah\",\"SG\":\"Sulawesi Tenggara\",\"SR\":\"Sulawesi Barat\",\"SN\":\"Sulawesi Selatan\",\"GO\":\"Gorontalo\",\"MA\":\"Maluku\",\"MU\":\"Maluku Utara\",\"PA\":\"Papua\",\"PB\":\"Barat da Papua\"},\"IE\":{\"CW\":\"Carlow\",\"CN\":\"Cavan\",\"CE\":\"Clare\",\"CO\":\"Cork\",\"DL\":\"Donegal\",\"D\":\"Dublin\",\"G\":\"Galway\",\"KY\":\"Kerry\",\"KE\":\"Kildare\",\"KK\":\"Kilkenny\",\"LS\":\"Laois\",\"LM\":\"Leitrim\",\"LK\":\"Limerick\",\"LD\":\"Longford\",\"LH\":\"Louth\",\"MO\":\"Mayo\",\"MH\":\"Meath\",\"MN\":\"Monaghan\",\"OY\":\"Offaly\",\"RN\":\"Roscommon\",\"SO\":\"Sligo\",\"TA\":\"Tipperary\",\"WD\":\"Waterford\",\"WH\":\"Westmeath\",\"WX\":\"Wexford\",\"WW\":\"Wicklow\"},\"IN\":{\"AP\":\"Andhra Pradesh\",\"AR\":\"Arunachal Pradesh\",\"AS\":\"Assam\",\"BR\":\"Bihar\",\"CT\":\"Chhattisgarh\",\"GA\":\"Goa\",\"GJ\":\"Gujarat\",\"HR\":\"Haryana\",\"HP\":\"Himachal Pradesh\",\"JK\":\"Jammu and Kashmir\",\"JH\":\"Jharkhand\",\"KA\":\"Karnataka\",\"KL\":\"Kerala\",\"LA\":\"Ladaque\",\"MP\":\"Madhya Pradesh\",\"MH\":\"Maharashtra\",\"MN\":\"Manipur\",\"ML\":\"Meghalaya\",\"MZ\":\"Mizoram\",\"NL\":\"Nagaland\",\"OD\":\"Odisha\",\"PB\":\"Punjab\",\"RJ\":\"Rajasthan\",\"SK\":\"Sikkim\",\"TN\":\"Tamil Nadu\",\"TS\":\"Telangana\",\"TR\":\"Tripura\",\"UK\":\"Uttarakhand\",\"UP\":\"Uttar Pradesh\",\"WB\":\"West Bengal\",\"AN\":\"Andaman and Nicobar Islands\",\"CH\":\"Chandigarh\",\"DN\":\"Dadr\\u00e1 e Nagar-Aveli\",\"DD\":\"Daman and Diu\",\"DL\":\"Delhi\",\"LD\":\"Lakshadeep\",\"PY\":\"Pondicherry (Puducherry)\"},\"IR\":{\"KHZ\":\"Cuzist\\u00e3o (\\u062e\\u0648\\u0632\\u0633\\u062a\\u0627\\u0646)\",\"THR\":\"Teer\\u00e3 (\\u062a\\u0647\\u0631\\u0627\\u0646)\",\"ILM\":\"Ilam (\\u0627\\u06cc\\u0644\\u0627\\u0645)\",\"BHR\":\"Bushehr (\\u0628\\u0648\\u0634\\u0647\\u0631)\",\"ADL\":\"Ardabil (\\u0627\\u0631\\u062f\\u0628\\u06cc\\u0644)\",\"ESF\":\"Isfahan (\\u0627\\u0635\\u0641\\u0647\\u0627\\u0646)\",\"YZD\":\"Yazd (\\u06cc\\u0632\\u062f)\",\"KRH\":\"Kermanshah (\\u06a9\\u0631\\u0645\\u0627\\u0646\\u0634\\u0627\\u0647)\",\"KRN\":\"Kerman (\\u06a9\\u0631\\u0645\\u0627\\u0646)\",\"HDN\":\"Hamad\\u00e3 (\\u0647\\u0645\\u062f\\u0627\\u0646)\",\"GZN\":\"Qazvin (\\u0642\\u0632\\u0648\\u06cc\\u0646)\",\"ZJN\":\"Zanjan (\\u0632\\u0646\\u062c\\u0627\\u0646)\",\"LRS\":\"Lorest\\u00e3o (\\u0644\\u0631\\u0633\\u062a\\u0627\\u0646)\",\"ABZ\":\"Alborz (\\u0627\\u0644\\u0628\\u0631\\u0632)\",\"EAZ\":\"Azerbaij\\u00e3o Oriental (\\u0622\\u0630\\u0631\\u0628\\u0627\\u06cc\\u062c\\u0627\\u0646 \\u0634\\u0631\\u0642\\u06cc)\",\"WAZ\":\"Azerbaij\\u00e3o Ocidental (\\u0622\\u0630\\u0631\\u0628\\u0627\\u06cc\\u062c\\u0627\\u0646 \\u063a\\u0631\\u0628\\u06cc)\",\"CHB\":\"Chahar Mahaal e Bakhtiari (\\u0686\\u0647\\u0627\\u0631\\u0645\\u062d\\u0627\\u0644 \\u0648 \\u0628\\u062e\\u062a\\u06cc\\u0627\\u0631\\u06cc)\",\"SKH\":\"Cora\\u00e7\\u00e3o do Sul (\\u062e\\u0631\\u0627\\u0633\\u0627\\u0646 \\u062c\\u0646\\u0648\\u0628\\u06cc)\",\"RKH\":\"Cora\\u00e7\\u00e3o Razavi (\\u062e\\u0631\\u0627\\u0633\\u0627\\u0646 \\u0631\\u0636\\u0648\\u06cc)\",\"NKH\":\"Khorasan do norte (\\u062e\\u0631\\u0627\\u0633\\u0627\\u0646 \\u0634\\u0645\\u0627\\u0644\\u06cc)\",\"SMN\":\"Semnan (\\u0633\\u0645\\u0646\\u0627\\u0646)\",\"FRS\":\"Fars (\\u0641\\u0627\\u0631\\u0633)\",\"QHM\":\"Qom (\\u0642\\u0645)\",\"KRD\":\"Curdist\\u00e3o \\\/ \\u06a9\\u0631\\u062f\\u0633\\u062a\\u0627\\u0646)\",\"KBD\":\"Kohkiluyeh e Buyer Ahmad (\\u06a9\\u0647\\u06af\\u06cc\\u0644\\u0648\\u06cc\\u06cc\\u0647 \\u0648 \\u0628\\u0648\\u06cc\\u0631\\u0627\\u062d\\u0645\\u062f)\",\"GLS\":\"Golestan (\\u06af\\u0644\\u0633\\u062a\\u0627\\u0646)\",\"GIL\":\"Gilan (\\u06af\\u06cc\\u0644\\u0627\\u0646)\",\"MZN\":\"Mazandaran (\\u0645\\u0627\\u0632\\u0646\\u062f\\u0631\\u0627\\u0646)\",\"MKZ\":\"Markazi (\\u0645\\u0631\\u06a9\\u0632\\u06cc)\",\"HRZ\":\"Hormozgan (\\u0647\\u0631\\u0645\\u0632\\u06af\\u0627\\u0646)\",\"SBN\":\"Sist\\u00e3o-Baluchist\\u00e3o (\\u0633\\u06cc\\u0633\\u062a\\u0627\\u0646 \\u0648 \\u0628\\u0644\\u0648\\u0686\\u0633\\u062a\\u0627\\u0646)\"},\"IS\":[],\"IT\":{\"AG\":\"Agrigento\",\"AL\":\"Alessandria\",\"AN\":\"Ancona\",\"AO\":\"Aosta\",\"AR\":\"Arezzo\",\"AP\":\"Ascoli Piceno\",\"AT\":\"Asti\",\"AV\":\"Avellino\",\"BA\":\"Bari\",\"BT\":\"Barletta-Andria-Trani\",\"BL\":\"Belluno\",\"BN\":\"Benevento\",\"BG\":\"Bergamo\",\"BI\":\"Biella\",\"BO\":\"Bologna\",\"BZ\":\"Bolzano\",\"BS\":\"Brescia\",\"BR\":\"Brindisi\",\"CA\":\"Cagliari\",\"CL\":\"Caltanissetta\",\"CB\":\"Campobasso\",\"CE\":\"Caserta\",\"CT\":\"Catania\",\"CZ\":\"Catanzaro\",\"CH\":\"Chieti\",\"CO\":\"Como\",\"CS\":\"Cosenza\",\"CR\":\"Cremona\",\"KR\":\"Crotone\",\"CN\":\"Cuneo\",\"EN\":\"Enna\",\"FM\":\"Fermo\",\"FE\":\"Ferrara\",\"FI\":\"Firenze\",\"FG\":\"Foggia\",\"FC\":\"Forl\\u00ec-Cesena\",\"FR\":\"Frosinone\",\"GE\":\"Genova\",\"GO\":\"Gorizia\",\"GR\":\"Grosseto\",\"IM\":\"Imperia\",\"IS\":\"Isernia\",\"SP\":\"La Spezia\",\"AQ\":\"\\u00c1quila\",\"LT\":\"Latina\",\"LE\":\"Lecce\",\"LC\":\"Lecco\",\"LI\":\"Livorno\",\"LO\":\"Lodi\",\"LU\":\"Lucca\",\"MC\":\"Macerata\",\"MN\":\"Mantova\",\"MS\":\"Massa-Carrara\",\"MT\":\"Matera\",\"ME\":\"Messina\",\"MI\":\"Milano\",\"MO\":\"Modena\",\"MB\":\"Monza e della Brianza\",\"NA\":\"Napoli\",\"NO\":\"Novara\",\"NU\":\"Nuoro\",\"OR\":\"Oristano\",\"PD\":\"Padova\",\"PA\":\"Palermo\",\"PR\":\"Parma\",\"PV\":\"Pavia\",\"PG\":\"Perugia\",\"PU\":\"Pesaro e Urbino\",\"PE\":\"Pescara\",\"PC\":\"Piacenza\",\"PI\":\"Pisa\",\"PT\":\"Pistoia\",\"PN\":\"Pordenone\",\"PZ\":\"Potenza\",\"PO\":\"Prato\",\"RG\":\"Ragusa\",\"RA\":\"Ravenna\",\"RC\":\"Reggio Calabria\",\"RE\":\"Reggio Emilia\",\"RI\":\"Rieti\",\"RN\":\"Rimini\",\"RM\":\"Roma\",\"RO\":\"Rovigo\",\"SA\":\"Salerno\",\"SS\":\"Sassari\",\"SV\":\"Savona\",\"SI\":\"Siena\",\"SR\":\"Siracusa\",\"SO\":\"Sondrio\",\"SU\":\"Sud Sardegna\",\"TA\":\"Taranto\",\"TE\":\"Teramo\",\"TR\":\"Terni\",\"TO\":\"Torino\",\"TP\":\"Trapani\",\"TN\":\"Trento\",\"TV\":\"Treviso\",\"TS\":\"Trieste\",\"UD\":\"Udine\",\"VA\":\"Varese\",\"VE\":\"Venezia\",\"VB\":\"Verbano-Cusio-Ossola\",\"VC\":\"Vercelli\",\"VR\":\"Verona\",\"VV\":\"Vibo Valentia\",\"VI\":\"Vicenza\",\"VT\":\"Viterbo\"},\"IL\":[],\"IM\":[],\"JM\":{\"JM-01\":\"Kingston\",\"JM-02\":\"Saint Andrew\",\"JM-03\":\"Saint Thomas\",\"JM-04\":\"Portland\",\"JM-05\":\"Saint Mary\",\"JM-06\":\"Saint Ann\",\"JM-07\":\"Trelawny\",\"JM-08\":\"Saint James\",\"JM-09\":\"Hanover\",\"JM-10\":\"Westmoreland\",\"JM-11\":\"Saint Elizabeth\",\"JM-12\":\"Manchester\",\"JM-13\":\"Clarendon\",\"JM-14\":\"Saint Catherine\"},\"JP\":{\"JP01\":\"Hokkaido\",\"JP02\":\"Aomori\",\"JP03\":\"Iwate\",\"JP04\":\"Miyagi\",\"JP05\":\"Akita\",\"JP06\":\"Yamagata\",\"JP07\":\"Fukushima\",\"JP08\":\"Ibaraki\",\"JP09\":\"Tochigi\",\"JP10\":\"Gunma\",\"JP11\":\"Saitama\",\"JP12\":\"Chiba\",\"JP13\":\"Tokyo\",\"JP14\":\"Kanagawa\",\"JP15\":\"Niigata\",\"JP16\":\"Toyama\",\"JP17\":\"Ishikawa\",\"JP18\":\"Fukui\",\"JP19\":\"Yamanashi\",\"JP20\":\"Nagano\",\"JP21\":\"Gifu\",\"JP22\":\"Shizuoka\",\"JP23\":\"Aichi\",\"JP24\":\"Mie\",\"JP25\":\"Shiga\",\"JP26\":\"Kyoto\",\"JP27\":\"Osaka\",\"JP28\":\"Hyogo\",\"JP29\":\"Nara\",\"JP30\":\"Wakayama\",\"JP31\":\"Tottori\",\"JP32\":\"Shimane\",\"JP33\":\"Okayama\",\"JP34\":\"Hiroshima\",\"JP35\":\"Yamaguchi\",\"JP36\":\"Tokushima\",\"JP37\":\"Kagawa\",\"JP38\":\"Ehime\",\"JP39\":\"Kochi\",\"JP40\":\"Fukuoka\",\"JP41\":\"Saga\",\"JP42\":\"Nagasaki\",\"JP43\":\"Kumamoto\",\"JP44\":\"Oita\",\"JP45\":\"Miyazaki\",\"JP46\":\"Kagoshima\",\"JP47\":\"Okinawa\"},\"KE\":{\"KE01\":\"Baringo\",\"KE02\":\"Bomet\",\"KE03\":\"Bungoma\",\"KE04\":\"Busia\",\"KE05\":\"Elgeyo-Marakwet\",\"KE06\":\"Embu\",\"KE07\":\"Garissa\",\"KE08\":\"Homa Bay\",\"KE09\":\"Isiolo\",\"KE10\":\"Kajiado\",\"KE11\":\"Kakamega\",\"KE12\":\"Kericho\",\"KE13\":\"Kiambu\",\"KE14\":\"Kilifi\",\"KE15\":\"Kirinyaga\",\"KE16\":\"Kisii\",\"KE17\":\"Kisumu\",\"KE18\":\"Kitui\",\"KE19\":\"Kwale\",\"KE20\":\"Laikipia\",\"KE21\":\"Lamu\",\"KE22\":\"Machakos\",\"KE23\":\"Makueni\",\"KE24\":\"Mandera\",\"KE25\":\"Marsabit\",\"KE26\":\"Meru\",\"KE27\":\"Migori\",\"KE28\":\"Mombasa\",\"KE29\":\"Murang\\u2019a\",\"KE30\":\"Nairobi County\",\"KE31\":\"Nakuru\",\"KE32\":\"Nandi\",\"KE33\":\"Narok\",\"KE34\":\"Nyamira\",\"KE35\":\"Nyandarua\",\"KE36\":\"Nyeri\",\"KE37\":\"Samburu\",\"KE38\":\"Siaya\",\"KE39\":\"Taita-Taveta\",\"KE40\":\"Tana River\",\"KE41\":\"Tharaka-Nithi\",\"KE42\":\"Trans Nzoia\",\"KE43\":\"Turkana\",\"KE44\":\"Uasin Gishu\",\"KE45\":\"Vihiga\",\"KE46\":\"Wajir\",\"KE47\":\"West Pokot\"},\"KN\":{\"KNK\":\"S\\u00e3o Crist\\u00f3v\\u00e3o\",\"KNN\":\"N\\u00e3o\",\"KN01\":\"Igreja de Cristo Nichola Town\",\"KN02\":\"Santa Ana Sandy Point\",\"KN03\":\"S\\u00e3o Jorge Basseterre\",\"KN04\":\"S\\u00e3o Jorge Gingerland\",\"KN05\":\"S\\u00e3o Tiago Barlavento\",\"KN06\":\"S\\u00e3o Jo\\u00e3o Capisterra\",\"KN07\":\"S\\u00e3o Jo\\u00e3o Figueira\",\"KN08\":\"Santa Maria Cayon\",\"KN09\":\"S\\u00e3o Paulo Capisterre\",\"KN10\":\"S\\u00e3o Paulo Charlestown\",\"KN11\":\"S\\u00e3o Pedro Basseterre\",\"KN12\":\"Plan\\u00edcie de S\\u00e3o Tom\\u00e1s\",\"KN13\":\"Ilha M\\u00e9dia de S\\u00e3o Tom\\u00e1s\",\"KN15\":\"Trinity Palmetto Point\"},\"KR\":[],\"KW\":[],\"LA\":{\"AT\":\"Attapeu\",\"BK\":\"Bokeo\",\"BL\":\"Bolikhamsai\",\"CH\":\"Champasak\",\"HO\":\"Houaphanh\",\"KH\":\"Khammouane\",\"LM\":\"Luang Namtha\",\"LP\":\"Luang Prabang\",\"OU\":\"Oudomxay\",\"PH\":\"Phongsaly\",\"SL\":\"Salavan\",\"SV\":\"Savannakhet\",\"VI\":\"Vientiane Province\",\"VT\":\"Vientiane\",\"XA\":\"Sainyabuli\",\"XE\":\"Sekong\",\"XI\":\"Xiangkhouang\",\"XS\":\"Xaisomboun\"},\"LB\":[],\"LI\":[],\"LR\":{\"BM\":\"Bomi\",\"BN\":\"Bong\",\"GA\":\"Gbarpolu\",\"GB\":\"Grand Bassa\",\"GC\":\"Grand Cape Mount\",\"GG\":\"Grand Gedeh\",\"GK\":\"Grand Kru\",\"LO\":\"Lofa\",\"MA\":\"Margibi\",\"MY\":\"Maryland\",\"MO\":\"Montserrado\",\"NM\":\"Nimba\",\"RV\":\"Rivercess\",\"RG\":\"River Gee\",\"SN\":\"Sinoe\"},\"LU\":[],\"MA\":{\"maagd\":\"Agadir-ida ou tanane\",\"maazi\":\"Azilal\",\"mabem\":\"B\\u00e9ni-mellal\",\"maber\":\"Berkane\",\"mabes\":\"Ben slimane\",\"mabod\":\"Boujdour\",\"mabom\":\"Boulemane\",\"mabrr\":\"Berrechid\",\"macas\":\"Casablanca\",\"mache\":\"Chefchaouen\",\"machi\":\"Chichaoua\",\"macht\":\"Chtouka a\\u00eft baha\",\"madri\":\"Driouch\",\"maedi\":\"Esseouira\",\"maerr\":\"Errachidia\",\"mafah\":\"Fahs-beni makada\",\"mafes\":\"F\\u00e8s-dar-dbibegh\",\"mafig\":\"Figue\",\"mafqh\":\"Fquih ben salah\",\"mague\":\"Guelmim\",\"maguf\":\"Guercif\",\"mahaj\":\"El hajeb\",\"mahao\":\"Al haouz\",\"mahoc\":\"Al hoce\\u00efma\",\"maifr\":\"Ifrane\",\"maine\":\"Inezgane-a\\u00eft melloul\",\"majdi\":\"El jadida\",\"majra\":\"Jerada\",\"maken\":\"K\\u00e9nitra\",\"makes\":\"Kelaat sraghna\",\"makhe\":\"Khemisset\",\"makhn\":\"Kh\\u00e9nifra\",\"makho\":\"Khouribga\",\"malaa\":\"La\\u00e2youne\",\"malar\":\"Larache\",\"mamar\":\"Marrakech\",\"mamdf\":\"M'diq-fnideq\",\"mamed\":\"M\\u00e9diouna\",\"mamek\":\"Mekn\\u00e8s\",\"mamid\":\"Midelt\",\"mammd\":\"Marrakech-medina\",\"mammn\":\"Marrakech-menara\",\"mamoh\":\"Mohammedia\",\"mamou\":\"Moulay yacoub\",\"manad\":\"Nador\",\"manou\":\"Nouaceur\",\"maoua\":\"Ouarzazate\",\"maoud\":\"Oued ed-dahab\",\"maouj\":\"Oujda-angad\",\"maouz\":\"Ouezzane\",\"marab\":\"Rabat\",\"mareh\":\"Rehamna\",\"masaf\":\"Safi\",\"masal\":\"Oferta\",\"masef\":\"Sefrou\",\"maset\":\"Settat\",\"masib\":\"Sidi bennour\",\"masif\":\"Sidi ifni\",\"masik\":\"Sidi kacem\",\"masil\":\"Sidi slimane\",\"maskh\":\"Skhirat-t\\u00e9mara\",\"masyb\":\"Sidi youssef ben ali\",\"mataf\":\"Tarfaya (eh-parcial)\",\"matai\":\"Taourirt\",\"matao\":\"Taounate\",\"matar\":\"Taroudant\",\"matat\":\"Tata\",\"mataz\":\"Taza\",\"matet\":\"T\\u00e9touan\",\"matin\":\"Tinghir\",\"matiz\":\"Tiznit\",\"matng\":\"T\\u00e2nger-assilah\",\"matnt\":\"Tan-tan\",\"mayus\":\"Yssoufia\",\"mazag\":\"Zagora\"},\"MD\":{\"C\":\"Chi\\u0219in\\u0103u\",\"BL\":\"B\\u0103l\\u021bi\",\"AN\":\"Anenii Noi\",\"BS\":\"Basarabeasca\",\"BR\":\"Briceni\",\"CH\":\"Cahul\",\"CT\":\"Cantemir\",\"CL\":\"C\\u0103l\\u0103ra\\u0219i\",\"CS\":\"C\\u0103u\\u0219eni\",\"CM\":\"Cimi\\u0219lia\",\"CR\":\"Criuleni\",\"DN\":\"Dondu\\u0219eni\",\"DR\":\"Drochia\",\"DB\":\"Dub\\u0103sari\",\"ED\":\"Edine\\u021b\",\"FL\":\"F\\u0103le\\u0219ti\",\"FR\":\"Flore\\u0219ti\",\"GE\":\"UTA G\\u0103g\\u0103uzia\",\"GL\":\"Glodeni\",\"HN\":\"H\\u00eence\\u0219ti\",\"IL\":\"Ialoveni\",\"LV\":\"Leova\",\"NS\":\"Nisporeni\",\"OC\":\"Ocni\\u021ba\",\"OR\":\"Orhei\",\"RZ\":\"Rezina\",\"RS\":\"R\\u00ee\\u0219cani\",\"SG\":\"S\\u00eengerei\",\"SR\":\"Soroca\",\"ST\":\"Str\\u0103\\u0219eni\",\"SD\":\"\\u0218old\\u0103ne\\u0219ti\",\"SV\":\"\\u0218tefan Vod\\u0103\",\"TR\":\"Taraclia\",\"TL\":\"Telene\\u0219ti\",\"UN\":\"Ungheni\"},\"MF\":[],\"MQ\":[],\"MT\":[],\"MX\":{\"DF\":\"Cidade do M\\u00e9xico\",\"JA\":\"Jalisco\",\"NL\":\"Nuevo Le\\u00f3n\",\"AG\":\"Aguascalientes\",\"BC\":\"Baja California\",\"BS\":\"Baja California Sur\",\"CM\":\"Campeche\",\"CS\":\"Chiapas\",\"CH\":\"Chihuahua\",\"CO\":\"Coahuila\",\"CL\":\"Colima\",\"DG\":\"Durango\",\"GT\":\"Guanajuato\",\"GR\":\"Guerrero\",\"HG\":\"Hidalgo\",\"MX\":\"Estado de M\\u00e9xico\",\"MI\":\"Michoac\\u00e1n\",\"MO\":\"Morelos\",\"NA\":\"Nayarit\",\"OA\":\"Oaxaca\",\"PU\":\"Puebla\",\"QT\":\"Quer\\u00e9taro\",\"QR\":\"Quintana Roo\",\"SL\":\"San Luis Potos\\u00ed\",\"SI\":\"Sinaloa\",\"SO\":\"Sonora\",\"TB\":\"Tabasco\",\"TM\":\"Tamaulipas\",\"TL\":\"Tlaxcala\",\"VE\":\"Veracruz\",\"YU\":\"Yucat\\u00e1n\",\"ZA\":\"Zacatecas\"},\"MY\":{\"JHR\":\"Johor\",\"KDH\":\"Kedah\",\"KTN\":\"Kelantan\",\"LBN\":\"Labuan\",\"MLK\":\"Malaca\",\"NSN\":\"Negeri Sembilan\",\"PHG\":\"Pahang\",\"PNG\":\"Penang (Pulau Pinang)\",\"PRK\":\"Perak\",\"PLS\":\"Perlis\",\"SBH\":\"Sabah\",\"SWK\":\"Sarawak\",\"SGR\":\"Selangor\",\"TRG\":\"Terengganu\",\"PJY\":\"Putrajaya\",\"KUL\":\"Kuala Lumpur\"},\"MZ\":{\"MZP\":\"Cabo Delgado\",\"MZG\":\"Gaza\",\"MZI\":\"Inhambane\",\"MZB\":\"Manica\",\"MZL\":\"Maputo Province\",\"MZMPM\":\"Maputo\",\"MZN\":\"Nampula\",\"MZA\":\"Niassa\",\"MZS\":\"Sofala\",\"MZT\":\"Tete\",\"MZQ\":\"Zamb\\u00e9zia\"},\"NA\":{\"ER\":\"Erongo\",\"HA\":\"Hardap\",\"KA\":\"Karas\",\"KE\":\"Kavango East\",\"KW\":\"Kavango West\",\"KH\":\"Khomas\",\"KU\":\"Kunene\",\"OW\":\"Ohangwena\",\"OH\":\"Omaheke\",\"OS\":\"Omusati\",\"ON\":\"Oshana\",\"OT\":\"Oshikoto\",\"OD\":\"Otjozondjupa\",\"CA\":\"Zambezi\"},\"NG\":{\"AB\":\"Abia\",\"FC\":\"Abuja\",\"AD\":\"Adamawa\",\"AK\":\"Akwa Ibom\",\"AN\":\"Anambra\",\"BA\":\"Bauchi\",\"BY\":\"Bayelsa\",\"BE\":\"Benue\",\"BO\":\"Borno\",\"CR\":\"Cross River\",\"DE\":\"Delta\",\"EB\":\"Ebonyi\",\"ED\":\"Edo\",\"EK\":\"Ekiti\",\"EN\":\"Enugu\",\"GO\":\"Gombe\",\"IM\":\"Imo\",\"JI\":\"Jigawa\",\"KD\":\"Kaduna\",\"KN\":\"Kano\",\"KT\":\"Katsina\",\"KE\":\"Kebbi\",\"KO\":\"Kogi\",\"KW\":\"Kwara\",\"LA\":\"Lagos\",\"NA\":\"Nasarawa\",\"NI\":\"N\\u00edger\",\"OG\":\"Ogun\",\"ON\":\"Ondo\",\"OS\":\"Osun\",\"OY\":\"Oyo\",\"PL\":\"Plateau\",\"RI\":\"Rivers\",\"SO\":\"Sokoto\",\"TA\":\"Taraba\",\"YO\":\"Yobe\",\"ZA\":\"Zamfara\"},\"NL\":[],\"NO\":[],\"NP\":{\"BAG\":\"Bagmati\",\"BHE\":\"Bheri\",\"DHA\":\"Dhaulagiri\",\"GAN\":\"Gandaki\",\"JAN\":\"Janakpur\",\"KAR\":\"Karnali\",\"KOS\":\"Koshi\",\"LUM\":\"Lumbini\",\"MAH\":\"Mahakali\",\"MEC\":\"Mechi\",\"NAR\":\"Narayani\",\"RAP\":\"Rapti\",\"SAG\":\"Sagarmatha\",\"SET\":\"Seti\"},\"NI\":{\"NI-AN\":\"Atl\\u00e1ntico Norte\",\"NI-AS\":\"Atl\\u00e1ntico Sur\",\"NI-BO\":\"Boaco\",\"NI-CA\":\"Carazo\",\"NI-CI\":\"Chinandega\",\"NI-CO\":\"Chontales\",\"NI-ES\":\"Estel\\u00ed\",\"NI-GR\":\"Granada\",\"NI-JI\":\"Jinotega\",\"NI-LE\":\"Le\\u00f3n\",\"NI-MD\":\"Madriz\",\"NI-MN\":\"Managua\",\"NI-MS\":\"Masaya\",\"NI-MT\":\"Matagalpa\",\"NI-NS\":\"Nueva Segovia\",\"NI-RI\":\"Rivas\",\"NI-SJ\":\"R\\u00edo San Juan\"},\"NZ\":{\"NTL\":\"Northland\",\"AUK\":\"Auckland\",\"WKO\":\"Waikato\",\"BOP\":\"Bay of Plenty\",\"TKI\":\"Taranaki\",\"GIS\":\"Gisborne\",\"HKB\":\"Hawke\\u2019s Bay\",\"MWT\":\"Manawatu-Whanganui\",\"WGN\":\"Wellington\",\"NSN\":\"Nelson\",\"MBH\":\"Marlborough\",\"TAS\":\"Tasman\",\"WTC\":\"Costa Oeste dos Estados Unidos\",\"CAN\":\"Canterbury\",\"OTA\":\"Otago\",\"STL\":\"Southland\"},\"PA\":{\"PA-1\":\"Bocas del Toro\",\"PA-2\":\"Cocl\\u00e9\",\"PA-3\":\"Col\\u00f3n\",\"PA-4\":\"Chiriqu\\u00ed\",\"PA-5\":\"Dari\\u00e9n\",\"PA-6\":\"Herrera\",\"PA-7\":\"Los Santos\",\"PA-8\":\"Panam\\u00e1\",\"PA-9\":\"Veraguas\",\"PA-10\":\"Panam\\u00e1 Oeste\",\"PA-EM\":\"Ember\\u00e1\",\"PA-KY\":\"Guna Yala\",\"PA-NB\":\"Ng\\u00f6be-Bugl\\u00e9\"},\"PE\":{\"CAL\":\"El Callao\",\"LMA\":\"Municipalidade Metropolitana de Lima\",\"AMA\":\"Amazonas\",\"ANC\":\"Ancash\",\"APU\":\"Apur\\u00edmac\",\"ARE\":\"Arequipa\",\"AYA\":\"Ayacucho\",\"CAJ\":\"Cajamarca\",\"CUS\":\"Cusco\",\"HUV\":\"Huancavelica\",\"HUC\":\"Hu\\u00e1nuco\",\"ICA\":\"Ica\",\"JUN\":\"Jun\\u00edn\",\"LAL\":\"La Libertad\",\"LAM\":\"Lambayeque\",\"LIM\":\"Lima\",\"LOR\":\"Loreto\",\"MDD\":\"Madre de Dios\",\"MOQ\":\"Moquegua\",\"PAS\":\"Pasco\",\"PIU\":\"Piura\",\"PUN\":\"Puno\",\"SAM\":\"San Mart\\u00edn\",\"TAC\":\"Tacna\",\"TUM\":\"Tumbes\",\"UCA\":\"Ucayali\"},\"PH\":{\"ABR\":\"Abra\",\"AGN\":\"Agusan del Norte\",\"AGS\":\"Agusan del Sur\",\"AKL\":\"Aklan\",\"ALB\":\"Albay\",\"ANT\":\"Antique\",\"APA\":\"Apayao\",\"AUR\":\"Aurora\",\"BAS\":\"Basilan\",\"BAN\":\"Bataan\",\"BTN\":\"Batanes\",\"BTG\":\"Batangas\",\"BEN\":\"Benguet\",\"BIL\":\"Biliran\",\"BOH\":\"Bohol\",\"BUK\":\"Bukidnon\",\"BUL\":\"Bulacan\",\"CAG\":\"Cagayan\",\"CAN\":\"Camarines Norte\",\"CAS\":\"Camarines Sur\",\"CAM\":\"Camiguin\",\"CAP\":\"Capiz\",\"CAT\":\"Catanduanes\",\"CAV\":\"Cavite\",\"CEB\":\"Cebu\",\"COM\":\"Compostela Valley\",\"NCO\":\"Cotabato\",\"DAV\":\"Davao del Norte\",\"DAS\":\"Davao del Sur\",\"DAC\":\"Davao Occidental\",\"DAO\":\"Davao Oriental\",\"DIN\":\"Dinagat Islands\",\"EAS\":\"Eastern Samar\",\"GUI\":\"Guimaras\",\"IFU\":\"Ifugao\",\"ILN\":\"Ilocos Norte\",\"ILS\":\"Ilocos Sur\",\"ILI\":\"Iloilo\",\"ISA\":\"Isabela\",\"KAL\":\"Kalinga\",\"LUN\":\"La Union\",\"LAG\":\"Laguna\",\"LAN\":\"Lanao del Norte\",\"LAS\":\"Lanao del Sur\",\"LEY\":\"Leyte\",\"MAG\":\"Maguindanao\",\"MAD\":\"Marinduque\",\"MAS\":\"Masbate\",\"MSC\":\"Misamis Occidental\",\"MSR\":\"Misamis Oriental\",\"MOU\":\"Mountain Province\",\"NEC\":\"Negros Occidental\",\"NER\":\"Negros Oriental\",\"NSA\":\"Northern Samar\",\"NUE\":\"Nueva Ecija\",\"NUV\":\"Nueva Vizcaya\",\"MDC\":\"Occidental Mindoro\",\"MDR\":\"Oriental Mindoro\",\"PLW\":\"Palawan\",\"PAM\":\"Pampanga\",\"PAN\":\"Pangasinan\",\"QUE\":\"Quezon\",\"QUI\":\"Quirino\",\"RIZ\":\"Rizal\",\"ROM\":\"Romblon\",\"WSA\":\"Samar\",\"SAR\":\"Sarangani\",\"SIQ\":\"Siquijor\",\"SOR\":\"Sorsogon\",\"SCO\":\"South Cotabato\",\"SLE\":\"Southern Leyte\",\"SUK\":\"Sultan Kudarat\",\"SLU\":\"Sulu\",\"SUN\":\"Surigao del Norte\",\"SUR\":\"Surigao del Sur\",\"TAR\":\"Tarlac\",\"TAW\":\"Tawi-Tawi\",\"ZMB\":\"Zambales\",\"ZAN\":\"Zamboanga del Norte\",\"ZAS\":\"Zamboanga del Sur\",\"ZSI\":\"Zamboanga Sibugay\",\"00\":\"Metro Manila\"},\"PK\":{\"JK\":\"Caxemira Livre\",\"BA\":\"Baluchist\\u00e3o\",\"TA\":\"Territ\\u00f3rio Federal das \\u00c1reas Tribais (FATA)\",\"GB\":\"Gilgit-Baltist\\u00e3o\",\"IS\":\"Territ\\u00f3rio da Capital Islamabad\",\"KP\":\"Khyber Pakhtunkhwa\",\"PB\":\"Punjab\",\"SD\":\"Sind\"},\"PL\":[],\"PR\":[],\"PT\":[],\"PY\":{\"PY-ASU\":\"Asunci\\u00f3n\",\"PY-1\":\"Concepci\\u00f3n\",\"PY-2\":\"San Pedro\",\"PY-3\":\"Cordillera\",\"PY-4\":\"Guair\\u00e1\",\"PY-5\":\"Caaguaz\\u00fa\",\"PY-6\":\"Caazap\\u00e1\",\"PY-7\":\"Itap\\u00faa\",\"PY-8\":\"Misiones\",\"PY-9\":\"Paraguar\\u00ed\",\"PY-10\":\"Alto Paran\\u00e1\",\"PY-11\":\"Central\",\"PY-12\":\"\\u00d1eembuc\\u00fa\",\"PY-13\":\"Amambay\",\"PY-14\":\"Canindey\\u00fa\",\"PY-15\":\"Presidente Hayes\",\"PY-16\":\"Alto Paraguay\",\"PY-17\":\"Boquer\\u00f3n\"},\"RE\":[],\"RO\":{\"AB\":\"Alba\",\"AR\":\"Arad\",\"AG\":\"Arge\\u0219\",\"BC\":\"Bac\\u0103u\",\"BH\":\"Bihor\",\"BN\":\"Bistri\\u021ba-N\\u0103s\\u0103ud\",\"BT\":\"Boto\\u0219ani\",\"BR\":\"Br\\u0103ila\",\"BV\":\"Bra\\u0219ov\",\"B\":\"Bucure\\u0219ti\",\"BZ\":\"Buz\\u0103u\",\"CL\":\"C\\u0103l\\u0103ra\\u0219i\",\"CS\":\"Cara\\u0219-Severin\",\"CJ\":\"Cluj\",\"CT\":\"Constan\\u021ba\",\"CV\":\"Covasna\",\"DB\":\"D\\u00e2mbovi\\u021ba\",\"DJ\":\"Dolj\",\"GL\":\"Gala\\u021bi\",\"GR\":\"Giurgiu\",\"GJ\":\"Gorj\",\"HR\":\"Harghita\",\"HD\":\"Hunedoara\",\"IL\":\"Ialomi\\u021ba\",\"IS\":\"Ia\\u0219i\",\"IF\":\"Ilfov\",\"MM\":\"Maramure\\u0219\",\"MH\":\"Mehedin\\u021bi\",\"MS\":\"Mure\\u0219\",\"NT\":\"Neam\\u021b\",\"OT\":\"Olt\",\"PH\":\"Prahova\",\"SJ\":\"S\\u0103laj\",\"SM\":\"Satu Mare\",\"SB\":\"Sibiu\",\"SV\":\"Suceava\",\"TR\":\"Teleorman\",\"TM\":\"Timi\\u0219\",\"TL\":\"Tulcea\",\"VL\":\"V\\u00e2lcea\",\"VS\":\"Vaslui\",\"VN\":\"Vrancea\"},\"SN\":{\"SNDB\":\"Diourbel\",\"SNDK\":\"Dacar\",\"SNFK\":\"Fatick\",\"SNKA\":\"Kaffrine\",\"SNKD\":\"Kolda\",\"SNKE\":\"Kedougou\",\"SNKL\":\"Kaolack\",\"SNLG\":\"Louga\",\"SNMT\":\"Matam\",\"SNSE\":\"S\\u00e9dhiou\",\"SNSL\":\"S\\u00e3o Lu\\u00eds\",\"SNTC\":\"Tambacounda\",\"SNTH\":\"Thies\",\"SNZG\":\"Ziguinchor\"},\"SG\":[],\"SK\":[],\"SI\":[],\"SV\":{\"SV-AH\":\"Ahuachap\\u00e1n\",\"SV-CA\":\"Caba\\u00f1as\",\"SV-CH\":\"Chalatenango\",\"SV-CU\":\"Cuscatl\\u00e1n\",\"SV-LI\":\"La Libertad\",\"SV-MO\":\"Moraz\\u00e1n\",\"SV-PA\":\"La Paz\",\"SV-SA\":\"Santa Ana\",\"SV-SM\":\"San Miguel\",\"SV-SO\":\"Sonsonate\",\"SV-SS\":\"San Salvador\",\"SV-SV\":\"San Vicente\",\"SV-UN\":\"La Uni\\u00f3n\",\"SV-US\":\"Usulut\\u00e1n\"},\"TH\":{\"TH-37\":\"Amnat Charoen\",\"TH-15\":\"Ang Thong\",\"TH-14\":\"Ayutthaya\",\"TH-10\":\"Bangkok\",\"TH-38\":\"Bueng Kan\",\"TH-31\":\"Buri Ram\",\"TH-24\":\"Chachoengsao\",\"TH-18\":\"Chai Nat\",\"TH-36\":\"Chaiyaphum\",\"TH-22\":\"Chanthaburi\",\"TH-50\":\"Chiang Mai\",\"TH-57\":\"Chiang Rai\",\"TH-20\":\"Chonburi\",\"TH-86\":\"Chumphon\",\"TH-46\":\"Kalasin\",\"TH-62\":\"Kamphaeng Phet\",\"TH-71\":\"Kanchanaburi\",\"TH-40\":\"Khon Kaen\",\"TH-81\":\"Krabi\",\"TH-52\":\"Lampang\",\"TH-51\":\"Lamphun\",\"TH-42\":\"Loei\",\"TH-16\":\"Lopburi\",\"TH-58\":\"Mae Hong Son\",\"TH-44\":\"Maha Sarakham\",\"TH-49\":\"Mukdahan\",\"TH-26\":\"Nakhon Nayok\",\"TH-73\":\"Nakhon Pathom\",\"TH-48\":\"Nakhon Phanom\",\"TH-30\":\"Nakhon Ratchasima\",\"TH-60\":\"Nakhon Sawan\",\"TH-80\":\"Nakhon Si Thammarat\",\"TH-55\":\"Nan\",\"TH-96\":\"Narathiwat\",\"TH-39\":\"Nong Bua Lam Phu\",\"TH-43\":\"Nong Khai\",\"TH-12\":\"Nonthaburi\",\"TH-13\":\"Pathum Thani\",\"TH-94\":\"Pattani\",\"TH-82\":\"Phang Nga\",\"TH-93\":\"Phatthalung\",\"TH-56\":\"Phayao\",\"TH-67\":\"Phetchabun\",\"TH-76\":\"Phetchaburi\",\"TH-66\":\"Phichit\",\"TH-65\":\"Phitsanulok\",\"TH-54\":\"Phrae\",\"TH-83\":\"Phuket\",\"TH-25\":\"Prachin Buri\",\"TH-77\":\"Prachuap Khiri Khan\",\"TH-85\":\"Ranong\",\"TH-70\":\"Ratchaburi\",\"TH-21\":\"Rayong\",\"TH-45\":\"Roi Et\",\"TH-27\":\"Sa Kaeo\",\"TH-47\":\"Sakon Nakhon\",\"TH-11\":\"Samut Prakan\",\"TH-74\":\"Samut Sakhon\",\"TH-75\":\"Samut Songkhram\",\"TH-19\":\"Saraburi\",\"TH-91\":\"Satun\",\"TH-17\":\"Sing Buri\",\"TH-33\":\"Sisaket\",\"TH-90\":\"Songkhla\",\"TH-64\":\"Sukhothai\",\"TH-72\":\"Suphan Buri\",\"TH-84\":\"Surat Thani\",\"TH-32\":\"Surin\",\"TH-63\":\"Tak\",\"TH-92\":\"Trang\",\"TH-23\":\"Trat\",\"TH-34\":\"Ubon Ratchathani\",\"TH-41\":\"Udon Thani\",\"TH-61\":\"Uthai Thani\",\"TH-53\":\"Uttaradit\",\"TH-95\":\"Yala\",\"TH-35\":\"Yasothon\"},\"TR\":{\"TR01\":\"Adana\",\"TR02\":\"Ad\\u0131yaman\",\"TR03\":\"Afyonkarahisar\",\"TR04\":\"A\\u011fr\\u0131\",\"TR05\":\"Amasya\",\"TR06\":\"Ancara\",\"TR07\":\"Ant\\u00e1lia\",\"TR08\":\"Artvin\",\"TR09\":\"Ayd\\u0131n\",\"TR10\":\"Bal\\u0131kesir\",\"TR11\":\"Bilecik\",\"TR12\":\"Bing\\u00f6l\",\"TR13\":\"Bitlis\",\"TR14\":\"Bolu\",\"TR15\":\"Burdur\",\"TR16\":\"Bursa\",\"TR17\":\"\\u00c7anakkale\",\"TR18\":\"\\u00c7ank\\u0131r\\u0131\",\"TR19\":\"\\u00c7orum\",\"TR20\":\"Denizli\",\"TR21\":\"Diyarbak\\u0131r\",\"TR22\":\"Edirne\",\"TR23\":\"Elaz\\u0131\\u011f\",\"TR24\":\"Erzincan\",\"TR25\":\"Erzurum\",\"TR26\":\"Eski\\u015fehir\",\"TR27\":\"Gaziantep\",\"TR28\":\"Giresun\",\"TR29\":\"G\\u00fcm\\u00fc\\u015fhane\",\"TR30\":\"Hakk\\u00e2ri\",\"TR31\":\"Hatay\",\"TR32\":\"Isparta\",\"TR33\":\"\\u0130\\u00e7el\",\"TR34\":\"Istanbul\",\"TR35\":\"\\u0130zmir\",\"TR36\":\"Kars\",\"TR37\":\"Kastamonu\",\"TR38\":\"Kayseri\",\"TR39\":\"K\\u0131rklareli\",\"TR40\":\"K\\u0131r\\u015fehir\",\"TR41\":\"Kocaeli\",\"TR42\":\"Konya\",\"TR43\":\"K\\u00fctahya\",\"TR44\":\"Malatya\",\"TR45\":\"Manisa\",\"TR46\":\"Kahramanmara\\u015f\",\"TR47\":\"Mardin\",\"TR48\":\"Mu\\u011fla\",\"TR49\":\"Mu\\u015f\",\"TR50\":\"Nev\\u015fehir\",\"TR51\":\"Ni\\u011fde\",\"TR52\":\"Ordu\",\"TR53\":\"Rize\",\"TR54\":\"Sakarya\",\"TR55\":\"Samsun\",\"TR56\":\"Siirt\",\"TR57\":\"Sinop\",\"TR58\":\"Sivas\",\"TR59\":\"Tekirda\\u011f\",\"TR60\":\"Tokat\",\"TR61\":\"Trabzon\",\"TR62\":\"Tunceli\",\"TR63\":\"\\u015eanl\\u0131urfa\",\"TR64\":\"U\\u015fak\",\"TR65\":\"Van\",\"TR66\":\"Yozgat\",\"TR67\":\"Zonguldak\",\"TR68\":\"Aksaray\",\"TR69\":\"Bayburt\",\"TR70\":\"Karaman\",\"TR71\":\"K\\u0131r\\u0131kkale\",\"TR72\":\"Batman\",\"TR73\":\"\\u015e\\u0131rnak\",\"TR74\":\"Bart\\u0131n\",\"TR75\":\"Ardahan\",\"TR76\":\"I\\u011fd\\u0131r\",\"TR77\":\"Yalova\",\"TR78\":\"Karab\\u00fck\",\"TR79\":\"Kilis\",\"TR80\":\"Osmaniye\",\"TR81\":\"D\\u00fczce\"},\"TZ\":{\"TZ01\":\"Arusha\",\"TZ02\":\"Dar es Salaam\",\"TZ03\":\"Dodoma\",\"TZ04\":\"Iringa\",\"TZ05\":\"Kagera\",\"TZ06\":\"Pemba North\",\"TZ07\":\"Zanzibar North\",\"TZ08\":\"Kigoma\",\"TZ09\":\"Kilimanjaro\",\"TZ10\":\"Pemba South\",\"TZ11\":\"Zanzibar South\",\"TZ12\":\"Lindi\",\"TZ13\":\"Mara\",\"TZ14\":\"Mbeya\",\"TZ15\":\"Zanzibar West\",\"TZ16\":\"Morogoro\",\"TZ17\":\"Mtwara\",\"TZ18\":\"Mwanza\",\"TZ19\":\"Coast\",\"TZ20\":\"Rukwa\",\"TZ21\":\"Ruvuma\",\"TZ22\":\"Shinyanga\",\"TZ23\":\"Singida\",\"TZ24\":\"Tabora\",\"TZ25\":\"Tanga\",\"TZ26\":\"Manyara\",\"TZ27\":\"Geita\",\"TZ28\":\"Katavi\",\"TZ29\":\"Njombe\",\"TZ30\":\"Simiyu\"},\"LK\":[],\"RS\":{\"RS00\":\"Belgrado\",\"RS14\":\"Bor\",\"RS11\":\"Brani\\u010devo\",\"RS02\":\"Banato Central\",\"RS10\":\"Danube\",\"RS23\":\"Jablanica\",\"RS09\":\"Kolubara\",\"RS08\":\"Ma\\u010dva\",\"RS17\":\"Moravica\",\"RS20\":\"Ni\\u0161ava\",\"RS01\":\"North Ba\\u010dka\",\"RS03\":\"North Banat\",\"RS24\":\"P\\u010dinja\",\"RS22\":\"Pirot\",\"RS13\":\"Pomoravlje\",\"RS19\":\"Rasina\",\"RS18\":\"Ra\\u0161ka\",\"RS06\":\"South Ba\\u010dka\",\"RS04\":\"South Banat\",\"RS07\":\"Srem\",\"RS12\":\"\\u0160umadija\",\"RS21\":\"Toplica\",\"RS05\":\"West Ba\\u010dka\",\"RS15\":\"Zaje\\u010dar\",\"RS16\":\"Zlatibor\",\"RS25\":\"Kosovo\",\"RS26\":\"Pe\\u0107\",\"RS27\":\"Prizren\",\"RS28\":\"Kosovska Mitrovica\",\"RS29\":\"Kosovo-Pomoravlje\",\"RSKM\":\"Kosovo-Metohija\",\"RSVO\":\"Voivodina\"},\"RW\":[],\"SE\":[],\"UA\":{\"UA05\":\"Vinnychchyna\",\"UA07\":\"Volyn\",\"UA09\":\"Luhanshchyna\",\"UA12\":\"Dnipropetrovshchyna\",\"UA14\":\"Donechchyna\",\"UA18\":\"Zhytomyrshchyna\",\"UA21\":\"Zakarpattia\",\"UA23\":\"Zaporizhzhya\",\"UA26\":\"Prykarpattia\",\"UA30\":\"Kyiv\",\"UA32\":\"Kyivshchyna\",\"UA35\":\"Kirovohradschyna\",\"UA40\":\"Sebastopol\",\"UA43\":\"Crimeia\",\"UA46\":\"Lvivshchyna\",\"UA48\":\"Mykolayivschyna\",\"UA51\":\"Odeshchyna\",\"UA53\":\"Poltavshchyna\",\"UA56\":\"Rivnenshchyna\",\"UA59\":\"Sumshchyna\",\"UA61\":\"Ternopilshchyna\",\"UA63\":\"Kharkivshchyna\",\"UA65\":\"Khersonshchyna\",\"UA68\":\"Khmelnychchyna\",\"UA71\":\"Cherkashchyna\",\"UA74\":\"Chernihivshchyna\",\"UA77\":\"Chernivtsi Oblast\"},\"UG\":{\"UG314\":\"Abim\",\"UG301\":\"Adjumani\",\"UG322\":\"Agago\",\"UG323\":\"Alebtong\",\"UG315\":\"Amolatar\",\"UG324\":\"Amudat\",\"UG216\":\"Amuria\",\"UG316\":\"Amuru\",\"UG302\":\"Apac\",\"UG303\":\"Arua\",\"UG217\":\"Budaka\",\"UG218\":\"Bududa\",\"UG201\":\"Bugiri\",\"UG235\":\"Bugweri\",\"UG420\":\"Buhweju\",\"UG117\":\"Buikwe\",\"UG219\":\"Bukedea\",\"UG118\":\"Bukomansimbi\",\"UG220\":\"Bukwa\",\"UG225\":\"Bulambuli\",\"UG416\":\"Buliisa\",\"UG401\":\"Bundibugyo\",\"UG430\":\"Bunyangabu\",\"UG402\":\"Bushenyi\",\"UG202\":\"Busia\",\"UG221\":\"Butaleja\",\"UG119\":\"Butambala\",\"UG233\":\"Butebo\",\"UG120\":\"Buvuma\",\"UG226\":\"Buyende\",\"UG317\":\"Dokolo\",\"UG121\":\"Gomba\",\"UG304\":\"Gulu\",\"UG403\":\"Hoima\",\"UG417\":\"Ibanda\",\"UG203\":\"Iganga\",\"UG418\":\"Isingiro\",\"UG204\":\"Jinja\",\"UG318\":\"Kaabong\",\"UG404\":\"Kabale\",\"UG405\":\"Kabarole\",\"UG213\":\"Kaberamaido\",\"UG427\":\"Kagadi\",\"UG428\":\"Kakumiro\",\"UG101\":\"Kalangala\",\"UG222\":\"Kaliro\",\"UG122\":\"Kalungu\",\"UG102\":\"Kampala\",\"UG205\":\"Kamuli\",\"UG413\":\"Kamwenge\",\"UG414\":\"Kanungu\",\"UG206\":\"Kapchorwa\",\"UG236\":\"Kapelebyong\",\"UG126\":\"Kasanda\",\"UG406\":\"Kasese\",\"UG207\":\"Katakwi\",\"UG112\":\"Kayunga\",\"UG407\":\"Kibaale\",\"UG103\":\"Kiboga\",\"UG227\":\"Kibuku\",\"UG432\":\"Kikuube\",\"UG419\":\"Kiruhura\",\"UG421\":\"Kiryandongo\",\"UG408\":\"Kisoro\",\"UG305\":\"Kitgum\",\"UG319\":\"Koboko\",\"UG325\":\"Kole\",\"UG306\":\"Kotido\",\"UG208\":\"Kumi\",\"UG333\":\"Kwania\",\"UG228\":\"Kween\",\"UG123\":\"Kyankwanzi\",\"UG422\":\"Kyegegwa\",\"UG415\":\"Kyenjojo\",\"UG125\":\"Kyotera\",\"UG326\":\"Lamwo\",\"UG307\":\"Lira\",\"UG229\":\"Luuka\",\"UG104\":\"Luwero\",\"UG124\":\"Lwengo\",\"UG114\":\"Lyantonde\",\"UG223\":\"Manafwa\",\"UG320\":\"Maracha\",\"UG105\":\"Masaka\",\"UG409\":\"Masindi\",\"UG214\":\"Mayuge\",\"UG209\":\"Mbale\",\"UG410\":\"Mbarara\",\"UG423\":\"Mitooma\",\"UG115\":\"Mityana\",\"UG308\":\"Moroto\",\"UG309\":\"Moyo\",\"UG106\":\"Mpigi\",\"UG107\":\"Mubende\",\"UG108\":\"Mukono\",\"UG334\":\"Nabilatuk\",\"UG311\":\"Nakapiripirit\",\"UG116\":\"Nakaseke\",\"UG109\":\"Nakasongola\",\"UG230\":\"Namayingo\",\"UG234\":\"Namisindwa\",\"UG224\":\"Namutumba\",\"UG327\":\"Napak\",\"UG310\":\"Nebbi\",\"UG231\":\"Ngora\",\"UG424\":\"Ntoroko\",\"UG411\":\"Ntungamo\",\"UG328\":\"Nwoya\",\"UG331\":\"Omoro\",\"UG329\":\"Otuke\",\"UG321\":\"Oyam\",\"UG312\":\"Pader\",\"UG332\":\"Pakwach\",\"UG210\":\"Pallisa\",\"UG110\":\"Rakai\",\"UG429\":\"Rubanda\",\"UG425\":\"Rubirizi\",\"UG431\":\"Rukiga\",\"UG412\":\"Rukungiri\",\"UG111\":\"Sembabule\",\"UG232\":\"Serere\",\"UG426\":\"Sheema\",\"UG215\":\"Sironko\",\"UG211\":\"Soroti\",\"UG212\":\"Tororo\",\"UG113\":\"Wakiso\",\"UG313\":\"Yumbe\",\"UG330\":\"Zombo\"},\"UM\":{\"81\":\"Ilha Baker\",\"84\":\"Ilha Howland\",\"86\":\"Ilha Jarvis\",\"67\":\"Atol Johnston\",\"89\":\"Recife Kingman\",\"71\":\"Atol Midway\",\"76\":\"Ilha Navassa\",\"95\":\"Atol Palmyra\",\"79\":\"Ilha Wake\"},\"US\":{\"AL\":\"Alabama\",\"AK\":\"Alasca\",\"AZ\":\"Arizona\",\"AR\":\"Arkansas\",\"CA\":\"Calif\\u00f3rnia\",\"CO\":\"Colorado\",\"CT\":\"Connecticut\",\"DE\":\"Delaware\",\"DC\":\"Distrito de Columbia\",\"FL\":\"Fl\\u00f3rida\",\"GA\":\"Ge\\u00f3rgia\",\"HI\":\"Hava\\u00ed\",\"ID\":\"Idaho\",\"IL\":\"Illinois\",\"IN\":\"Indiana\",\"IA\":\"Iowa\",\"KS\":\"Kansas\",\"KY\":\"Kentucky\",\"LA\":\"Louisiana\",\"ME\":\"Maine\",\"MD\":\"Maryland\",\"MA\":\"Massachusetts\",\"MI\":\"Michigan\",\"MN\":\"Minnesota\",\"MS\":\"Mississippi\",\"MO\":\"Missouri\",\"MT\":\"Montana\",\"NE\":\"Nebraska\",\"NV\":\"Nevada\",\"NH\":\"Nova Hampshire\",\"NJ\":\"Nova Jersey\",\"NM\":\"Novo M\\u00e9xico\",\"NY\":\"Nova Iorque\",\"NC\":\"Carolina do Norte\",\"ND\":\"Dakota do Norte\",\"OH\":\"Ohio\",\"OK\":\"Oklahoma\",\"OR\":\"Oregon\",\"PA\":\"Pensilv\\u00e2nia\",\"RI\":\"Rhode Island\",\"SC\":\"Carolina do Sul\",\"SD\":\"Dakota do Sul\",\"TN\":\"Tennessee\",\"TX\":\"Texas\",\"UT\":\"Utah\",\"VT\":\"Vermont\",\"VA\":\"Virg\\u00ednia\",\"WA\":\"Washington\",\"WV\":\"Virg\\u00ednia Ocidental\",\"WI\":\"Wisconsin\",\"WY\":\"Wyoming\",\"AA\":\"For\\u00e7as Armadas (AA)\",\"AE\":\"For\\u00e7as Armadas (AE)\",\"AP\":\"For\\u00e7as Armadas (AP)\"},\"UY\":{\"UY-AR\":\"Artigas\",\"UY-CA\":\"Canelones\",\"UY-CL\":\"Cerro Largo\",\"UY-CO\":\"Colonia\",\"UY-DU\":\"Durazno\",\"UY-FS\":\"Flores\",\"UY-FD\":\"Fl\\u00f3rida\",\"UY-LA\":\"Lavalleja\",\"UY-MA\":\"Maldonado\",\"UY-MO\":\"Montevid\\u00e9u\",\"UY-PA\":\"Paysand\\u00fa\",\"UY-RN\":\"R\\u00edo Negro\",\"UY-RV\":\"Rivera\",\"UY-RO\":\"Rocha\",\"UY-SA\":\"Salto\",\"UY-SJ\":\"San Jos\\u00e9\",\"UY-SO\":\"Soriano\",\"UY-TA\":\"Tacuaremb\\u00f3\",\"UY-TT\":\"Treinta y Tres\"},\"VE\":{\"VE-A\":\"Capital\",\"VE-B\":\"Anzo\\u00e1tegui\",\"VE-C\":\"Apure\",\"VE-D\":\"Aragua\",\"VE-E\":\"Barinas\",\"VE-F\":\"Bol\\u00edvar\",\"VE-G\":\"Carabobo\",\"VE-H\":\"Cojedes\",\"VE-I\":\"Falc\\u00f3n\",\"VE-J\":\"Gu\\u00e1rico\",\"VE-K\":\"Lara\",\"VE-L\":\"M\\u00e9rida\",\"VE-M\":\"Miranda\",\"VE-N\":\"Monagas\",\"VE-O\":\"Nueva Esparta\",\"VE-P\":\"Portuguesa\",\"VE-R\":\"Sucre\",\"VE-S\":\"T\\u00e1chira\",\"VE-T\":\"Trujillo\",\"VE-U\":\"Yaracuy\",\"VE-V\":\"Zulia\",\"VE-W\":\"Depend\\u00eancias Federais da Venezuela\",\"VE-X\":\"La Guaira (Vargas)\",\"VE-Y\":\"Delta Amacuro\",\"VE-Z\":\"Amazonas\"},\"VN\":[],\"YT\":[],\"ZA\":{\"EC\":\"Cabo Oriental\",\"FS\":\"Estado Livre\",\"GP\":\"Gauteng\",\"KZN\":\"KwaZulu-Natal\",\"LP\":\"Limpopo\",\"MP\":\"Mpumalanga\",\"NC\":\"Cabo Norte\",\"NW\":\"Noroeste\",\"WC\":\"Cabo Ocidental\"},\"ZM\":{\"ZM-01\":\"Ocidental\",\"ZM-02\":\"Central\",\"ZM-03\":\"Oriental\",\"ZM-04\":\"Luapula\",\"ZM-05\":\"Norte\",\"ZM-06\":\"Noroeste\",\"ZM-07\":\"Sul\",\"ZM-08\":\"Copperbelt\",\"ZM-09\":\"Lusaka\",\"ZM-10\":\"Muchinga\"}}","i18n_select_state_text":"Selecione uma op\u00e7\u00e3o\u2026","i18n_no_matches":"Nenhuma combina\u00e7\u00e3o foi encontrada","i18n_ajax_error":"O carregando falhou","i18n_input_too_short_1":"Digite 1 ou mais caracteres","i18n_input_too_short_n":"Digite %qty% ou mais caracteres","i18n_input_too_long_1":"Exclua 1 caracter","i18n_input_too_long_n":"Exclua %qty% caracteres","i18n_selection_too_long_1":"Voc\u00ea pode apenas selecionar 1 item","i18n_selection_too_long_n":"Voc\u00ea pode apenas selecionar %qty% itens","i18n_load_more":"Carregando mais resultados\u2026","i18n_searching":"Procurando\u2026"};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/frontend/country-select.min.js?ver=9.9.5" id="wc-country-select-js" defer data-wp-strategy="defer"></script>
<script id="wc-address-i18n-js-extra">
var wc_address_i18n_params = {"locale":"{\"AE\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"required\":false}},\"AF\":{\"state\":{\"required\":false,\"hidden\":true}},\"AL\":{\"state\":{\"label\":\"Pa\\u00eds\"}},\"AO\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"label\":\"Prov\\u00edncia\"}},\"AT\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"AU\":{\"city\":{\"label\":\"Bairro\"},\"postcode\":{\"label\":\"CEP\"},\"state\":{\"label\":\"Estado\"}},\"AX\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"BA\":{\"postcode\":{\"priority\":65},\"state\":{\"label\":\"Canton\",\"required\":false,\"hidden\":true}},\"BD\":{\"postcode\":{\"required\":false},\"state\":{\"label\":\"Estado\"}},\"BE\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"BG\":{\"state\":{\"required\":false}},\"BH\":{\"postcode\":{\"required\":false},\"state\":{\"required\":false,\"hidden\":true}},\"BI\":{\"state\":{\"required\":false,\"hidden\":true}},\"BO\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"label\":\"Departamento\"}},\"BS\":{\"postcode\":{\"required\":false,\"hidden\":true}},\"BW\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"required\":false,\"hidden\":true,\"label\":\"Estado\"}},\"BZ\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"required\":false}},\"CA\":{\"postcode\":{\"label\":\"C\\u00f3digo postal\"},\"state\":{\"label\":\"Prov\\u00edncia\"}},\"CH\":{\"postcode\":{\"priority\":65},\"state\":{\"label\":\"Canton\",\"required\":false}},\"CL\":{\"city\":{\"required\":true},\"postcode\":{\"required\":false,\"hidden\":false},\"state\":{\"label\":\"Regi\\u00e3o\"}},\"CN\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"CO\":{\"postcode\":{\"required\":false},\"state\":{\"label\":\"Departamento\"}},\"CR\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"CW\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"required\":false}},\"CY\":{\"state\":{\"required\":false,\"hidden\":true}},\"CZ\":{\"state\":{\"required\":false,\"hidden\":true}},\"DE\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false}},\"DK\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"DO\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"EC\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"EE\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"ET\":{\"state\":{\"required\":false,\"hidden\":true}},\"FI\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"FR\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"GG\":{\"state\":{\"required\":false,\"label\":\"Par\\u00f3quia\"}},\"GH\":{\"postcode\":{\"required\":false},\"state\":{\"label\":\"Regi\\u00e3o\"}},\"GP\":{\"state\":{\"required\":false,\"hidden\":true}},\"GF\":{\"state\":{\"required\":false,\"hidden\":true}},\"GR\":{\"state\":{\"required\":false}},\"GT\":{\"postcode\":{\"required\":false},\"state\":{\"label\":\"Departamento\"}},\"HK\":{\"postcode\":{\"required\":false},\"city\":{\"label\":\"Cidade \\\/ Estado\"},\"state\":{\"label\":\"Regi\\u00e3o\"}},\"HN\":{\"state\":{\"label\":\"Departamento\"}},\"HU\":{\"last_name\":{\"class\":[\"form-row-first\"],\"priority\":10},\"first_name\":{\"class\":[\"form-row-last\"],\"priority\":20},\"postcode\":{\"class\":[\"form-row-first\",\"address-field\"],\"priority\":65},\"city\":{\"class\":[\"form-row-last\",\"address-field\"]},\"address_1\":{\"priority\":71},\"address_2\":{\"priority\":72},\"state\":{\"label\":\"Pa\\u00eds\",\"required\":false}},\"ID\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"IE\":{\"postcode\":{\"required\":true,\"label\":\"Eircode\"},\"state\":{\"label\":\"Pa\\u00eds\"}},\"IS\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"IL\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"IM\":{\"state\":{\"required\":false,\"hidden\":true}},\"IN\":{\"postcode\":{\"label\":\"C\\u00f3digo PIN\"},\"state\":{\"label\":\"Estado\"}},\"IR\":{\"state\":{\"priority\":50},\"city\":{\"priority\":60},\"address_1\":{\"priority\":70},\"address_2\":{\"priority\":80}},\"IT\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":true,\"label\":\"Prov\\u00edncia\"}},\"JM\":{\"city\":{\"label\":\"Cidade \\\/ Correio\"},\"postcode\":{\"required\":false,\"label\":\"CEP\"},\"state\":{\"required\":true,\"label\":\"Par\\u00f3quia\"}},\"JP\":{\"last_name\":{\"class\":[\"form-row-first\"],\"priority\":10},\"first_name\":{\"class\":[\"form-row-last\"],\"priority\":20},\"postcode\":{\"class\":[\"form-row-first\",\"address-field\"],\"priority\":65},\"state\":{\"label\":\"Prefeitura\",\"class\":[\"form-row-last\",\"address-field\"],\"priority\":66},\"city\":{\"priority\":67},\"address_1\":{\"priority\":68},\"address_2\":{\"priority\":69}},\"KN\":{\"postcode\":{\"required\":false,\"label\":\"C\\u00f3digo postal\"},\"state\":{\"required\":true,\"label\":\"Par\\u00f3quia\"}},\"KR\":{\"state\":{\"required\":false,\"hidden\":true}},\"KW\":{\"state\":{\"required\":false,\"hidden\":true}},\"LV\":{\"state\":{\"label\":\"Munic\\u00edpio\",\"required\":false}},\"LB\":{\"state\":{\"required\":false,\"hidden\":true}},\"MF\":{\"state\":{\"required\":false,\"hidden\":true}},\"MQ\":{\"state\":{\"required\":false,\"hidden\":true}},\"MT\":{\"state\":{\"required\":false,\"hidden\":true}},\"MZ\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"label\":\"Prov\\u00edncia\"}},\"NI\":{\"state\":{\"label\":\"Departamento\"}},\"NL\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"NG\":{\"postcode\":{\"label\":\"CEP\",\"required\":false,\"hidden\":true},\"state\":{\"label\":\"Estado\"}},\"NZ\":{\"postcode\":{\"label\":\"CEP\"},\"state\":{\"required\":false,\"label\":\"Regi\\u00e3o\"}},\"NO\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"NP\":{\"state\":{\"label\":\"Estado\"},\"postcode\":{\"required\":false}},\"PA\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"PL\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"PR\":{\"city\":{\"label\":\"Munic\\u00edpio\"},\"state\":{\"required\":false,\"hidden\":true}},\"PT\":{\"state\":{\"required\":false,\"hidden\":true}},\"PY\":{\"state\":{\"label\":\"Departamento\"}},\"RE\":{\"state\":{\"required\":false,\"hidden\":true}},\"RO\":{\"state\":{\"label\":\"Pa\\u00eds\",\"required\":true}},\"RS\":{\"city\":{\"required\":true},\"postcode\":{\"required\":true},\"state\":{\"label\":\"Estado\",\"required\":false}},\"RW\":{\"state\":{\"required\":false,\"hidden\":true}},\"SG\":{\"state\":{\"required\":false,\"hidden\":true},\"city\":{\"required\":false}},\"SK\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"SI\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"SR\":{\"postcode\":{\"required\":false,\"hidden\":true}},\"SV\":{\"state\":{\"label\":\"Departamento\"}},\"ES\":{\"postcode\":{\"priority\":65},\"state\":{\"label\":\"Prov\\u00edncia\"}},\"LI\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"LK\":{\"state\":{\"required\":false,\"hidden\":true}},\"LU\":{\"state\":{\"required\":false,\"hidden\":true}},\"MD\":{\"state\":{\"label\":\"Munic\\u00edpio\\\/distrito\"}},\"SE\":{\"postcode\":{\"priority\":65},\"state\":{\"required\":false,\"hidden\":true}},\"TR\":{\"postcode\":{\"priority\":65},\"state\":{\"label\":\"Prov\\u00edncia\"}},\"UG\":{\"postcode\":{\"required\":false,\"hidden\":true},\"city\":{\"label\":\"Cidade \\\/ vilarejo\",\"required\":true},\"state\":{\"label\":\"Estado\",\"required\":true}},\"US\":{\"postcode\":{\"label\":\"CEP\"},\"state\":{\"label\":\"Estado\"}},\"UY\":{\"state\":{\"label\":\"Departamento\"}},\"GB\":{\"postcode\":{\"label\":\"CEP\"},\"state\":{\"label\":\"Pa\\u00eds\",\"required\":false}},\"ST\":{\"postcode\":{\"required\":false,\"hidden\":true},\"state\":{\"label\":\"Estado\"}},\"VN\":{\"state\":{\"required\":false,\"hidden\":true},\"postcode\":{\"priority\":65,\"required\":false,\"hidden\":false},\"address_2\":{\"required\":false,\"hidden\":false}},\"WS\":{\"postcode\":{\"required\":false,\"hidden\":true}},\"YT\":{\"state\":{\"required\":false,\"hidden\":true}},\"ZA\":{\"state\":{\"label\":\"Prov\\u00edncia\"}},\"ZW\":{\"postcode\":{\"required\":false,\"hidden\":true}},\"default\":{\"first_name\":{\"required\":true,\"class\":[\"form-row-first\"],\"autocomplete\":\"given-name\"},\"last_name\":{\"required\":true,\"class\":[\"form-row-last\"],\"autocomplete\":\"family-name\"},\"company\":{\"class\":[\"form-row-wide\"],\"autocomplete\":\"organization\",\"required\":false},\"country\":{\"type\":\"country\",\"required\":true,\"class\":[\"form-row-wide\",\"address-field\",\"update_totals_on_change\"],\"autocomplete\":\"country\"},\"address_1\":{\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"autocomplete\":\"address-line1\"},\"address_2\":{\"label_class\":[\"screen-reader-text\"],\"class\":[\"form-row-wide\",\"address-field\"],\"autocomplete\":\"address-line2\",\"required\":false},\"city\":{\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"autocomplete\":\"address-level2\"},\"state\":{\"type\":\"state\",\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"validate\":[\"state\"],\"autocomplete\":\"address-level1\"},\"postcode\":{\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"validate\":[\"postcode\"],\"autocomplete\":\"postal-code\"}},\"BR\":{\"first_name\":{\"required\":true,\"class\":[\"form-row-first\"],\"autocomplete\":\"given-name\"},\"last_name\":{\"required\":true,\"class\":[\"form-row-last\"],\"autocomplete\":\"family-name\"},\"company\":{\"class\":[\"form-row-wide\"],\"autocomplete\":\"organization\",\"required\":false},\"country\":{\"type\":\"country\",\"required\":true,\"class\":[\"form-row-wide\",\"address-field\",\"update_totals_on_change\"],\"autocomplete\":\"country\"},\"address_1\":{\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"autocomplete\":\"address-line1\"},\"address_2\":{\"label_class\":[\"screen-reader-text\"],\"class\":[\"form-row-wide\",\"address-field\"],\"autocomplete\":\"address-line2\",\"required\":false},\"city\":{\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"autocomplete\":\"address-level2\"},\"state\":{\"type\":\"state\",\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"validate\":[\"state\"],\"autocomplete\":\"address-level1\"},\"postcode\":{\"required\":true,\"class\":[\"form-row-wide\",\"address-field\"],\"validate\":[\"postcode\"],\"autocomplete\":\"postal-code\"}}}","locale_fields":"{\"address_1\":\"#billing_address_1_field, #shipping_address_1_field\",\"address_2\":\"#billing_address_2_field, #shipping_address_2_field\",\"state\":\"#billing_state_field, #shipping_state_field, #calc_shipping_state_field\",\"postcode\":\"#billing_postcode_field, #shipping_postcode_field, #calc_shipping_postcode_field\",\"city\":\"#billing_city_field, #shipping_city_field, #calc_shipping_city_field\"}","i18n_required_text":"obrigat\u00f3rio","i18n_optional_text":"opcional"};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/frontend/address-i18n.min.js?ver=9.9.5" id="wc-address-i18n-js" defer data-wp-strategy="defer"></script>
<script id="wc-checkout-js-extra">
var wc_checkout_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/step\/elemenia\/?wc-ajax=%%endpoint%%&wcf_checkout_id=364","update_order_review_nonce":"51fc096189","apply_coupon_nonce":"716bc83fe8","remove_coupon_nonce":"0583b24c69","option_guest_checkout":"yes","checkout_url":"\/?wc-ajax=checkout&wcf_checkout_id=364","is_checkout":"1","debug_mode":"1","i18n_checkout_error":"Houve um erro ao processar sua compra. Por favor verifique por qualquer cobran\u00e7a no seu m\u00e9todo de pagamento e revise o seu <a href=\"https:\/\/pay.desyne.pro\/orders\/\">hist\u00f3rico de compra<\/a> antes de refazer a compra."};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/frontend/checkout.min.js?ver=9.9.5" id="wc-checkout-js" defer data-wp-strategy="defer"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/jquery-cookie/jquery.cookie.min.js?ver=1.4.1-wc.9.9.5" id="jquery-cookie-js" data-wp-strategy="defer"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/cartflows/assets/js/frontend.js?ver=2.1.14" id="wcf-frontend-global-js"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/cartflows-pro/assets/js/frontend.js?ver=2.1.2" id="wcf-pro-frontend-global-js"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/cartflows-pro/assets/js/analytics.js?ver=2.1.2" id="wcf-pro-analytics-global-js"></script>
<link rel="https://api.w.org/" href="https://pay.desyne.pro/wp-json/" /><link rel="alternate" title="JSON" type="application/json" href="https://pay.desyne.pro/wp-json/wp/v2/cartflows_step/364" /><link rel="EditURI" type="application/rsd+xml" title="RSD" href="https://pay.desyne.pro/xmlrpc.php?rsd" />
<meta name="generator" content="WordPress 6.8.1" />
<meta name="generator" content="WooCommerce 9.9.5" />
<link rel="canonical" href="https://pay.desyne.pro/step/elemenia/" />
<link rel='shortlink' href='https://pay.desyne.pro/?p=364' />
<link rel="alternate" title="oEmbed (JSON)" type="application/json+oembed" href="https://pay.desyne.pro/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fpay.desyne.pro%2Fstep%2Felemenia%2F" />
<link rel="alternate" title="oEmbed (XML)" type="text/xml+oembed" href="https://pay.desyne.pro/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fpay.desyne.pro%2Fstep%2Felemenia%2F&#038;format=xml" />
	<noscript><style>.woocommerce-product-gallery{ opacity: 1 !important; }</style></noscript>
	<meta name="generator" content="Elementor 3.30.0; features: e_font_icon_svg, additional_custom_breakpoints, e_element_cache; settings: css_print_method-external, google_font-enabled, font_display-swap">
			<style>
				.e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
				.e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
					background-image: none !important;
				}
				@media screen and (max-height: 1024px) {
					.e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
					.e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
						background-image: none !important;
					}
				}
				@media screen and (max-height: 640px) {
					.e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
					.e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
						background-image: none !important;
					}
				}
			</style>
			<script>// Aguarda o DOM estar completamente carregado
document.addEventListener('DOMContentLoaded', function() {
    // Seleciona o campo de email pelo ID
    const emailField = document.getElementById('billing_email');
    
    // Verifica se o elemento existe antes de modificar
    if (emailField) {
        // Altera o placeholder para o novo texto desejado
        emailField.placeholder = 'Digite seu melhor e-mail';
        
        // Adiciona um evento de foco para melhorar a experiência do usuário
        emailField.addEventListener('focus', function() {
            this.placeholder = '';
        });
        
        // Adiciona um evento de perda de foco
        emailField.addEventListener('blur', function() {
            if (!this.value) {
                this.placeholder = 'Digite seu melhor e-mail';
            }
        });
    }
});

document.addEventListener('DOMContentLoaded', function() {
    // Encontra o elemento pelo ID
    const paymentHeading = document.getElementById('payment_options_heading');
    
    // Verifica se o elemento existe
    if (paymentHeading) {
        // Altera o texto para "Pagamento"
        paymentHeading.textContent = "Pagamento";
    }
});
</script><script>document.addEventListener("DOMContentLoaded", function () {

    let desktopAccordionInitialized = false; // Flag to track if desktop setup is active
    let currentObserver = null; // Variable to hold the MutationObserver instance

    // --- Function to Update Accordion Header Total ---
    function updateAccordionHeaderTotal() {
        // Target the total *within* the table, wherever it is (likely inside accordion content)
        const totalElement = document.querySelector('.custom-order-summary-container .shop_table.woocommerce-checkout-review-order-table .order-total .woocommerce-Price-amount');
        // Target the display span in the accordion header
        const headerTotalSpan = document.querySelector('.custom-order-summary-container .accordion-header .total-amount');

        if (totalElement && headerTotalSpan) {
            headerTotalSpan.textContent = totalElement.textContent;
            // console.log('Header total updated:', totalElement.textContent); // For debugging
        } else {
            // console.log('Update total failed: Element(s) not found.', totalElement, headerTotalSpan); // For debugging
        }
    }

    // --- Function to Setup Mutation Observer ---
    function setupMutationObserver() {
        if (currentObserver) {
            currentObserver.disconnect(); // Disconnect previous if exists
        }

        // Observe the container where updates are expected
        const containerToObserve = document.querySelector('.custom-order-summary-container');

        if (containerToObserve) {
            currentObserver = new MutationObserver(function(mutations) {
                // Check if relevant nodes changed (e.g., price amount)
                 let relevantChange = false;
                 mutations.forEach(mutation => {
                     // Simple check: look for changes within the table or if nodes containing prices were added/removed
                     if (mutation.target.closest('.shop_table') || mutation.target.querySelector('.woocommerce-Price-amount')) {
                         relevantChange = true;
                     }
                      // More robust: check specifically if '.order-total .woocommerce-Price-amount' text changed
                     if (mutation.type === 'characterData' && mutation.target.parentElement?.closest('.order-total')) {
                         relevantChange = true;
                     }
                 });

                 if (relevantChange) {
                    // Use setTimeout to allow WC AJAX potentially finish updates before grabbing total
                    setTimeout(updateAccordionHeaderTotal, 150);
                 }
            });

            currentObserver.observe(containerToObserve, {
                subtree: true,
                childList: true,
                characterData: true,
                attributes: true, // Observe attribute changes too, might be needed for some AJAX updates
                attributeFilter: ['class', 'style'] // Optional: filter specific attributes if needed
            });
            // console.log('Observer attached to .custom-order-summary-container'); // For debugging
        } else {
            // console.log('Observer setup failed: .custom-order-summary-container not found'); // For debugging
        }
    }

    // --- Function to Initialize Desktop Layout (Move Table + Add Accordion) ---
    function initializeDesktopLayout() {
        if (desktopAccordionInitialized) {
            // console.log('Desktop init aborted: already initialized.'); // For debugging
            return; // Prevent re-initialization
        }

        // === Part 1: Find, Clean Up, and Move the Table ===
        const tables = document.querySelectorAll(".shop_table.woocommerce-checkout-review-order-table");
        const customerInfoWrapper = document.querySelector(".wcf-customer-info-main-wrapper");

        if (tables.length === 0 || !customerInfoWrapper) {
             // console.log('Desktop init aborted: missing table or customer info wrapper.'); // For debugging
            return; // Essential elements missing
        }

        // Remove duplicates (keep the first one)
        for (let i = tables.length - 1; i > 0; i--) { // Iterate backwards for safe removal
             if (tables[i].parentNode) {
                tables[i].parentNode.removeChild(tables[i]);
                // console.log('Removed duplicate table:', i); // For debugging
             }
        }

        const tableToModify = tables[0]; // The single table we will work with

        // Create or find the custom container
        let tableContainer = document.querySelector(".custom-order-summary-container");
        if (!tableContainer) {
            tableContainer = document.createElement("div");
            tableContainer.classList.add("custom-order-summary-container");
            tableContainer.style.marginBottom = "20px";
            tableContainer.style.width = "100%";
            tableContainer.style.boxSizing = "border-box";
            customerInfoWrapper.parentNode.insertBefore(tableContainer, customerInfoWrapper);
            // console.log('.custom-order-summary-container created.'); // For debugging
        }

        // Ensure the table is inside the container
        if (tableToModify.parentNode !== tableContainer) {
            tableContainer.appendChild(tableToModify);
            // console.log('Table moved into .custom-order-summary-container.'); // For debugging
        }

        // === Part 2: Wrap the Table in an Accordion ===
        // Check if it's already wrapped (e.g., if resize happens fast)
        if (tableToModify.closest('.accordion-wrapper')) {
             // console.log('Accordion init aborted: table already wrapped.'); // For debugging
            return;
        }

        const accordionWrapper = document.createElement('div');
        accordionWrapper.className = 'accordion-wrapper';

        // Get total amount *from the table element*
        const totalElement = tableToModify.querySelector('.order-total .woocommerce-Price-amount');
        const totalAmountText = totalElement ? totalElement.textContent.trim() : ''; // Use trim()

        const accordionHeader = document.createElement('div');
        accordionHeader.className = 'accordion-header';
        accordionHeader.innerHTML = `
            <div class="header-content" style="display: flex; justify-content: space-between; align-items: center; width: 100%; cursor: pointer;">
                <div class="header-left" style="display: flex; align-items: center;">
                    <svg class="cart-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 8px;">
                        <circle cx="9" cy="21" r="1"></circle>
                        <circle cx="20" cy="21" r="1"></circle>
                        <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                    </svg>
                    <span>Detalhes da compra</span>
                </div>
                <div class="header-right" style="display: flex; align-items: center;">
                    <span class="total-amount" style="margin-right: 8px;">${totalAmountText}</span>
                    <svg class="chevron-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; transition: transform 0.3s ease;">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </div>
            </div>
        `;

        const accordionContent = document.createElement('div');
        accordionContent.className = 'accordion-content';
        // Style for initial state (hidden) - relies on CSS `.active` class to override
        accordionContent.style.display = 'none';
        accordionContent.style.overflow = 'hidden'; // Helps with transitions

        // Move the actual table element into the content div
        accordionContent.appendChild(tableToModify);

        // Assemble the wrapper
        accordionWrapper.appendChild(accordionHeader);
        accordionWrapper.appendChild(accordionContent);

        // Replace the table's original position (now empty) within the container with the new wrapper
        // Since tableToModify was moved into accordionContent, its original spot is effectively empty.
        // We append the wrapper to the container.
        tableContainer.appendChild(accordionWrapper);
        // console.log('Accordion wrapper added, containing the table.'); // For debugging


        // === Part 3: Add Click Listener ===
        // Add listener AFTER the header is part of the wrapper and potentially in the DOM
        const headerElement = accordionWrapper.querySelector('.accordion-header'); // Target specifically
        if (headerElement) {
             headerElement.addEventListener('click', function() {
                 // console.log('Accordion header clicked!'); // For debugging
                 accordionWrapper.classList.toggle('active');

                 // Toggle content visibility directly and chevron rotation
                 const content = accordionWrapper.querySelector('.accordion-content');
                 const chevron = headerElement.querySelector('.chevron-icon');
                 if (accordionWrapper.classList.contains('active')) {
                     content.style.display = 'block'; // Or 'grid', 'flex', etc., depending on table display
                     if(chevron) chevron.style.transform = 'rotate(180deg)';
                     // Optional: Animate height using JS if CSS transitions aren't sufficient
                 } else {
                     content.style.display = 'none';
                      if(chevron) chevron.style.transform = 'rotate(0deg)';
                     // Optional: Animate height back to 0
                 }
             });
             // console.log('Click listener added to accordion header.'); // For debugging
        } else {
             // console.log('Error: Accordion header not found for adding click listener.'); // For debugging
        }


        desktopAccordionInitialized = true; // Mark as initialized

        // === Part 4: Setup Observer ===
        setupMutationObserver();
    }

    // --- Function to Teardown Desktop Layout (Remove Accordion) ---
    function teardownDesktopLayout() {
        if (!desktopAccordionInitialized) {
            // console.log('Desktop teardown aborted: not initialized.'); // For debugging
            return;
        }

        const accordionWrapper = document.querySelector('.custom-order-summary-container .accordion-wrapper');
        const container = document.querySelector('.custom-order-summary-container');

        if (accordionWrapper && container) {
            // Find the original table *inside* the accordion content
            const originalTable = accordionWrapper.querySelector('.accordion-content > .shop_table.woocommerce-checkout-review-order-table'); // More specific selector

            if (originalTable) {
                // Put the table back into the container directly
                container.appendChild(originalTable);
                 // Remove the now-empty accordion wrapper
                accordionWrapper.remove();
                // console.log('Accordion wrapper removed, table restored in container.'); // For debugging
            } else {
                 // Fallback if table not found inside: just remove wrapper
                 accordionWrapper.remove();
                 // console.log('Accordion wrapper removed (table not found inside during teardown).'); // For debugging
            }
        }

        // Disconnect the observer when leaving desktop view
        if (currentObserver) {
            currentObserver.disconnect();
            currentObserver = null;
            // console.log('Observer disconnected.'); // For debugging
        }

        desktopAccordionInitialized = false; // Mark as ready for re-initialization if needed
    }

    // --- Initial Check and Resize Handling ---
    let isCurrentlyDesktop = window.innerWidth > 768;

    // Run on initial load if desktop
    if (isCurrentlyDesktop) {
        initializeDesktopLayout();
    }

    // Debounce resize function to avoid excessive calls
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            const wasDesktop = isCurrentlyDesktop;
            isCurrentlyDesktop = window.innerWidth > 768;

            // console.log(`Resize detected. Was Desktop: ${wasDesktop}, Is Desktop: ${isCurrentlyDesktop}`); // For debugging

            if (isCurrentlyDesktop && !wasDesktop) {
                // Transitioned from Mobile to Desktop
                // console.log('Resize: Mobile -> Desktop. Initializing...'); // For debugging
                initializeDesktopLayout();
            } else if (!isCurrentlyDesktop && wasDesktop) {
                // Transitioned from Desktop to Mobile
                // console.log('Resize: Desktop -> Mobile. Tearing down...'); // For debugging
                teardownDesktopLayout();
            }
        }, 250); // 250ms delay
    });

}); // End DOMContentLoaded</script><style class="wpcode-css-snippet">/* Estilo Moderno e Minimalista para Página de Pagamento PIX */

@media (min-width: 768px) {
img.mp-pix-template-image {
    width: 36% !important;
}
}



.mp-pix-template-image {
   
 padding-bottom:24px !important;
   
}




/* Container principal */
.mp-details-pix {
  max-width: 500px !important;
  margin: 0 auto !important;
  background-color: #ffffff !important;
  border-radius: 16px !important;

  overflow: hidden !important;
  font-family: 'Poppins', sans-serif !important;
}

/* Layout da seção PIX */
.mp-row-checkout-pix {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  padding: 16px 20px !important;
}

/* Esconder elementos desnecessários */
.mp-col-md-4, 
.mp-details-pix-qr-subtitle,
.mp-details-pix-qr-description {
  display: none !important;
}

/* Ajustar coluna do QR code para ocupar toda largura e centralizar */
.mp-col-md-8 {
  width: 100% !important;
  padding: 0 !important;
  background-color: transparent !important;
  text-align: center !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}

h3#billing_fields_heading {
    padding-bottom: 12px;
}

/* Título principal */
.mp-details-title {
  font-size: 22px !important;
  font-weight: 600 !important;
  color: #333333 !important;
  text-align: center !important;
  margin-bottom: 25px !important;
}

/* Valor a pagar */
.mp-details-pix-amount {
  margin-bottom: 30px !important;
  text-align: center !important;
  width: 100% !important;
}

.mp-details-pix-qr {
  font-size: 16px !important;
  color: #666666 !important;
  display: block !important;
  margin-bottom: 8px !important;
  text-align: center !important;
}

.mp-details-pix-qr-value {
  font-size: 28px !important;
  font-weight: 700 !important;
  color: #333333 !important;
  text-align: center !important;
}

/* Título do QR code */
.mp-details-pix-qr-title {
  font-size: 16px !important;
  color: #666666 !important;
  margin-bottom: 20px !important;
  text-align: center !important;
  width: 100% !important;
}

.mp-details-pix-button:hover {
  background-color: #28a745 !important;
}
/* QR Code - Centralização corrigida */
.mp-details-pix-qr-img {
  width: 200px !important;
  height: 200px !important;
  padding: 15px !important;
  background-color: #ffffff !important;
  border: 1px solid #e6e6e6 !important;
  border-radius: 12px !important;
  margin: 0 auto 30px !important;
  display: block !important;
}

/* Container do código PIX */
.mp-details-pix-container {
  width: 100% !important;
  max-width: 350px !important;
  margin: 0 auto !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

/* Container do input e botão */
.mp-row-checkout-pix-container {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  width: 100% !important;
}

/* CORRIGIDO*/

/* Input do código PIX */
#mp-qr-code {
  width: 100% !important;
  padding: 12px 15px !important;
  border: 1px solid #e6e6e6 !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  color: #666666 !important;
  background-color: #f9f9f9 !important;
  margin-bottom: 15px !important;
  text-align: center !important;
}

/* Botão de copiar */
.mp-details-pix-button {
  width: 100% !important;
  padding: 14px 20px !important;
  background-color: #32CD32 !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
/* Adicionar ícone de cópia ao botão */
.mp-details-pix-button::before {
  content: "" !important;
  display: inline-block !important;
  width: 18px !important;
  height: 18px !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  margin-right: 8px !important;
}

/* Tempo restante */
.mp-row-checkout-pix::after {
  content: "Tempo restante: 30 minutos" !important;
  display: block !important;
  margin-top: 0px !important;
  font-size: 14px !important;
  color: #666666 !important;
  text-align: center !important;
  width: 100% !important;
}






/* Melhorias para a tabela de detalhes do pedido (imagem 2) */
.woocommerce-order-overview {
  display: flex !important;
  flex-wrap: wrap !important;
  justify-content: space-between !important;
  background-color: #f8f9fa !important;

  padding: 15px !important;
  margin: 0 0 30px 0 !important;
  border: none !important;

}

.woocommerce-order-overview li {
  flex: 1 1 45% !important;
  margin: 5px 0 !important;
  padding: 8px !important;
  border: none !important;
  font-size: 14px !important;
  color: #666666 !important;
  display: flex !important;
  flex-direction: column !important;
}

.woocommerce-order-overview li strong {
  font-size: 16px !important;
  color: #333333 !important;
  font-weight: 600 !important;
  margin-top: 4px !important;
}

/* Melhorias para a área de status do pagamento (imagem 3) */
#transaction-status-message {
  background-color: #f8f9fa !important;

  padding: 15px !important;
  margin: 20px auto !important;
  max-width: 400px !important;

}
/* CORRIGIDO*/

#transaction-status-message div {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

#transaction-status-message img {
  width: 24px !important;
  height: 24px !important;
  margin-right: 10px !important;
}

#transaction-status-message p {
  margin: 0 !important;
  color: #666666 !important;
  font-size: 15px !important;
  font-weight: 500 !important;
}
.
@media (max-width: 480px) {
mp-row-checkout-pix {
    padding: 0px !important;
}
}
/* Responsividade para dispositivos móveis */
@media (max-width: 480px) {
.mp-details-pix {
    padding: 0px !important;
} 
}
    max-width: 100% !important;
    border-radius: 12px !important;
  }
  
  .mp-row-checkout-pix {
    padding: 25px 15px !important;
  }
@media (max-width: 767.98px) {
    .mp-pix-right {
       
        margin-bottom: 0px !important;
    }
}

  
  .mp-details-pix-qr-value {
    font-size: 24px !important;
  }
  
  .mp-details-pix-qr-img {
    width: 180px !important;
    height: 180px !important;
  }
  
  .woocommerce-order-overview li {
    flex: 1 1 100% !important;
  }
}

/* Estilo específico para centralizar o código PIX abaixo do QR code */
.mp-details-pix-container p,
.mp-pix-image-qr-code p,
#mp-qr-code,
.mp-details-pix-qr-code p {
  text-align: center !important;
  width: 100% !important;
  display: block !important;
  margin: 0 auto 15px !important;
}

/* Ajuste para o input que contém o código */
#mp-qr-code {
  width: 100% !important;
  max-width: 350px !important;
  padding: 12px 15px !important;
  border: 1px solid #e6e6e6 !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  color: #666666 !important;
  background-color: #f9f9f9 !important;
  margin: 0 auto 15px !important;
  text-align: center !important;
  display: block !important;
}


.mp-details-pix {
    display: flex !important;
    justify-content: center !important;
    text-align: center !important;
}
.mp-col-md-8.mp-text-center.mp-pix-right {
    border: none !important;
}
ul.woocommerce-order-overview.woocommerce-thankyou-order-details.order_details {
    display: none !important;
}
p.mp-details-title {
    display: none !important;
}

/* CORRIGIDO*/
/* Container principal do PIX */
.mp-details-pix {
    display: flex;
    justify-content: center;
    text-align: center;
    width: 100%;
    max-width: 1100px;
    margin: 0 auto;
    padding: 10px;
}

/* Linha de checkout do PIX */
.mp-row-checkout-pix {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Colunas */
.mp-col-md-4,
.mp-col-md-8 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 550px;
    margin: 0 auto;
    padding: 5px;
}

/* Container do código PIX */
.mp-details-pix-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    text-align: center;
    margin: 0;
    padding: 5px 0;
}

/* Linha do container de checkout PIX */
.mp-row-checkout-pix-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 400px;
    margin: 5px auto;
}

/* Input do código PIX */
#mp-qr-code {
    width: 100%;
    max-width: 400px;
    text-align: center;
    margin: 5px auto;
    padding: 8px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #f8f8f8;
    font-size: 13px;
    color: #333;
}

/* Botão de copiar código */
.mp-details-pix-button {
    width: 100%;
    max-width: 400px;
    margin: 5px auto;
    padding: 8px;
    background-color: #32CD32;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 13px;
    display: block;
}

/* Imagem do QR code */
.mp-details-pix-qr-img {
    display: block;
    margin: 5px auto;
    max-width: 200px;
}


/* CORRIGIDO*/

/* Textos e títulos */
.mp-details-pix-title,
.mp-details-pix-qr-title,
.mp-details-pix-qr-subtitle,
.mp-details-pix-qr-description,
.mp-details-pix-amount,
.mp-details-pix p {
    text-align: center;
    width: 100%;
    margin: 5px auto;
    padding: 0;
    font-size: 14px;
}

/* Lista de passos */
.mp-steps-congrats {
    padding-left: 0;
    margin: 5px 0;
}

.mp-details-list {
    margin-bottom: 5px;
    padding: 0;
}

.mp-details-pix-number-p {
    margin: 0 0 2px 0;
}

.mp-details-list-description {
    margin: 0;
    font-size: 13px;
}

/* Valor a pagar */
.mp-details-pix-amount {
    margin: 5px 0;
}

.mp-details-pix-qr,
.mp-details-pix-qr-value {
    margin: 0;
    padding: 0;
}

/* Responsividade para dispositivos móveis */
@media (max-width: 768px) {
    .mp-row-checkout-pix {
        flex-direction: column-reverse;
    }
    
    .mp-col-md-4,
    .mp-col-md-8 {
        width: 100%;
        max-width: 100%;
        padding: 5px;
    }
    
    #mp-qr-code,
    .mp-details-pix-button {
        max-width: 100%;
    }
    
    .mp-details-pix-img {
        max-width: 80px;
    }
}
.mp-row-checkout-pix-container {

  padding: 0px !important;
}
.mp-details-pix-amount {
   margin-bottom: 10px !important; 
  
}


/* CORRIGIDO*/

/* Para garantir que qualquer sombra nos contêineres seja removida */
.mp-details-pix {
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    background-color: transparent !important;
}
.elementor-366 .elementor-element.elementor-element {
 
    padding:0px !important;

}


.elementor-element.elementor-element-6a543efd.elementor-widget.elementor-widget-image {
    display: none !important;
}






















/* Estilo para aumentar o tamanho do QR code e torná-lo responsivo */
.woocommerce-order .mp-details-pix-qr-img {
  width: 280px !important;
  height: 280px !important;
  max-width: 90% !important;
  padding: 15px !important;
  background-color: #ffffff !important;
  border: 1px solid #e6e6e6 !important;
  border-radius: 12px !important;
  margin: 0 auto 30px !important;
  display: block !important;
  transition: transform 0.3s ease !important;
}

/* Efeito de hover para o QR code */
.woocommerce-order .mp-details-pix-qr-img:hover {
  transform: scale(1.05) !important;
}

/* Responsividade para diferentes tamanhos de tela */
@media (min-width: 768px) {
  .woocommerce-order .mp-details-pix-qr-img {
    width: 250px !important;
    height: 250px !important;
  }
}

@media (min-width: 768px) {
.mp-details-pix-qr-value {
    font-size: 28px !important;
}
}
/* Para telas menores */
@media (max-width: 480px) {
  .woocommerce-order .mp-details-pix-qr-img {
    width: 180px !important;
    height: 180px !important;
  }
  
  /* Ajustes para melhor visualização em telas pequenas */
  .woocommerce-order .mp-details-pix {
    padding: 10px 5px !important;
  }
  
  .woocommerce-order .mp-row-checkout-pix {
    padding: 15px 10px !important;
  }
}

/* Para telas muito pequenas */
@media (max-width: 320px) {
  .woocommerce-order .mp-details-pix-qr-img {
    width: 220px !important;
    height: 220px !important;
    padding: 10px !important;
  }
}

/* Melhorar a centralização do QR code */
.woocommerce-order .mp-col-md-8.mp-text-center.mp-pix-right {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
}

/* Ajustar o container principal para melhor responsividade */
.woocommerce-order .mp-details-pix {
  width: 100% !important;
  max-width: 600px !important;
  margin: 0 auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

@media only screen and (max-width: 768px) {
.header-right {
    display: flex !important;

    gap: 0px !important;

}
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout h3, .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #ship-to-different-address, .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #order_review_heading {
   
    margin: 22px 0 0px !important;
}

</style><script>
// Script para remover todos os elementos duplicados no order bump
(function() {
    function limparOrderBump() {
        // Verificar se já existe algum header
        var headers = document.querySelectorAll('.wcf-bump-order-header');
        
        // Se existir mais de um header, manter apenas o primeiro
        if (headers.length > 1) {
            for (var i = 1; i < headers.length; i++) {
                if (headers[i].parentNode) {
                    headers[i].parentNode.removeChild(headers[i]);
                }
            }
        }
        
        // Se não existir nenhum header, criar um novo
        if (headers.length === 0) {
            criarNovoHeader();
        }
        
        // Esconder a oferta original
        var ofertaOriginal = document.querySelector('.wcf-bump-order-offer');
        if (ofertaOriginal) {
            ofertaOriginal.style.display = 'none';
        }
        
        // Remover todos os títulos duplicados fora do header
        var header = document.querySelector('.wcf-bump-order-header');
        if (header) {
            var tituloNoHeader = header.querySelector('.wcf-bump-order-bump-highlight');
            var todosTitulos = document.querySelectorAll('.wcf-bump-order-bump-highlight');
            
            for (var i = 0; i < todosTitulos.length; i++) {
                if (todosTitulos[i] !== tituloNoHeader && todosTitulos[i].closest('.wcf-bump-order-header') !== header) {
                    var elementoPai = todosTitulos[i].parentNode;
                    if (elementoPai && !elementoPai.classList.contains('wcf-bump-order-header')) {
                        elementoPai.style.display = 'none';
                    }
                }
            }
        }
        
        // Remover todos os preços duplicados
        var precoNoHeader = header ? header.querySelector('.wcf-normal-price') : null;
        var todosPrecos = document.querySelectorAll('.wcf-normal-price');
        
        for (var i = 0; i < todosPrecos.length; i++) {
            if (todosPrecos[i] !== precoNoHeader) {
                if (todosPrecos[i].parentNode) {
                    todosPrecos[i].parentNode.removeChild(todosPrecos[i]);
                }
            }
        }
    }
    
    function criarNovoHeader() {
        // Encontrar os elementos originais
        var ofertaDiv = document.querySelector('.wcf-bump-order-offer');
        var precoSpan = document.querySelector('.wcf-normal-price');
        
        if (!ofertaDiv || !precoSpan) return;
        
        // Criar o header
        var header = document.createElement('div');
        header.className = 'wcf-bump-order-header';
        header.style.display = 'flex';
        header.style.justifyContent = 'space-between';
        header.style.alignItems = 'center';
        header.style.marginBottom = '10px';
        header.style.width = '100%';
        
        // Pegar o título
        var titulo = ofertaDiv.querySelector('.wcf-bump-order-bump-highlight');
        if (!titulo) return;
        
        // Criar clones dos elementos
        var tituloClone = titulo.cloneNode(true);
        var precoClone = precoSpan.cloneNode(true);
        
        // Adicionar os elementos ao header
        header.appendChild(tituloClone);
        header.appendChild(precoClone);
        
        // Inserir o header antes da oferta
        ofertaDiv.parentNode.insertBefore(header, ofertaDiv);
    }
    
    // Executar a função imediatamente
    limparOrderBump();
    
    // Executar a cada 500ms para garantir que o layout permaneça correto
    setInterval(limparOrderBump, 500);
    
    // Executar após cliques
    document.addEventListener('click', function() {
        setTimeout(limparOrderBump, 100);
        setTimeout(limparOrderBump, 500);
    });
    
    // Executar após mudanças no DOM
    var observer = new MutationObserver(function() {
        limparOrderBump();
    });
    
    // Iniciar o observador
    if (document.body) {
        observer.observe(document.body, { childList: true, subtree: true });
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            observer.observe(document.body, { childList: true, subtree: true });
        });
    }
})();</script><script>// Script para colocar o título e o preço na mesma linha e remover o preço duplicado
(function() {
    // Função para executar quando o DOM estiver carregado
    function ajustarOrderBump() {
        // Selecionar os elementos
        var ofertaUnica = document.querySelector('.wcf-bump-order-offer');
        var precoElement = document.querySelector('.wcf-normal-price');
        
        // Verificar se os elementos existem
        if (!ofertaUnica || !precoElement) {
            console.log("Elementos não encontrados, tentando novamente em 500ms");
            setTimeout(ajustarOrderBump, 500);
            return;
        }
        
        // Criar um novo container para ambos os elementos
        var novoContainer = document.createElement('div');
        novoContainer.className = 'wcf-bump-order-header';
        novoContainer.style.display = 'flex';
        novoContainer.style.justifyContent = 'center';
        novoContainer.style.alignItems = 'center';
        novoContainer.style.marginBottom = '10px';
        novoContainer.style.width = '100%';
        
        // Pegar o título
        var titulo = ofertaUnica.querySelector('.wcf-bump-order-bump-highlight');
        
        // Criar um clone do título para o novo container
        var tituloClone = titulo.cloneNode(true);
        tituloClone.style.marginRight = '15px';
        
        // Criar um clone do preço para o novo container
        var precoClone = precoElement.cloneNode(true);
        
        // Adicionar os elementos ao novo container
        novoContainer.appendChild(tituloClone);
        novoContainer.appendChild(precoClone);
        
        // Encontrar o elemento pai onde inserir o novo container
        var parentElement = ofertaUnica.parentNode;
        
        // Inserir o novo container antes do elemento original
        parentElement.insertBefore(novoContainer, ofertaUnica);
        
        // Esconder os elementos originais
        ofertaUnica.style.display = 'none';
        
        // Encontrar e esconder o elemento <br> se existir
        var descElement = document.querySelector('.wcf-bump-order-desc p');
        if (descElement) {
            var br = descElement.querySelector('br');
            if (br) br.style.display = 'none';
            
            // Esconder o preço original na descrição
            var precoOriginal = descElement.querySelector('.wcf-normal-price');
            if (precoOriginal) {
                // Remover completamente o preço original
                precoOriginal.parentNode.removeChild(precoOriginal);
            }
        }
        
        // Procurar e remover qualquer outro preço duplicado na página
        var todosPrecos = document.querySelectorAll('.wcf-normal-price');
        if (todosPrecos.length > 1) {
            for (var i = 1; i < todosPrecos.length; i++) {
                todosPrecos[i].parentNode.removeChild(todosPrecos[i]);
            }
        }
        
        console.log("Ajuste do order bump concluído com sucesso");
    }
    
    // Executar a função quando o DOM estiver carregado
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', ajustarOrderBump);
    } else {
        ajustarOrderBump();
    }
    
    // Executar novamente após um atraso para garantir que todos os elementos foram carregados
    setTimeout(ajustarOrderBump, 1000);
})();






</script><style class="wpcode-css-snippet">.wcf-bump-order-style-1 .wcf-bump-order-offer-content-left img {
    padding: 0 !important;
}
.wcf-bump-order-wrap .wcf-bump-order-field-wrap label {
    display: flex !important;
    align-items: center !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    font-size: 1em !important;
    color: #495057 !important;
}
li.wc_payment_method.payment_method_woo-mercado-pago-pix {
    margin: 0px !important;
}



.wcf-bump-order-header {
    padding: 0px !important;
}



/* --- Container dos Detalhes da Oferta (O Card Branco) --- */
/* Estilos Desktop */
.wcf-bump-order-wrap .wcf-content-container {
    display: flex !important;
    flex-direction: row !important; /* Lado a lado no Desktop */
    align-items: flex-start !important;
    gap: 20px !important;           /* Espaço entre imagem e texto no Desktop */
    background-color: #ffffff !important;
   
    border-radius: 8px !important;
    padding: 25px !important;      /* Padding Desktop */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06) !important;
    width: 100% !important;
    box-sizing: border-box !important;
}
/* --- Coluna Esquerda (Imagem) - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-offer-content-left.wcf-bump-order-image {
    flex-shrink: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    width: auto !important;
}

/* --- Imagem - Desktop --- */
.wcf-bump-order-wrap .wcf-image {
    display: block !important;
    /* Mantém estilos padrão ou anteriores da imagem no desktop */
    /* (Tamanho padrão do plugin ou tema) */
    max-width: 100%; /* Garante não estourar container */
    height: auto;    /* Mantem proporção */
    border-radius: 6px; /* Arredondamento padrão */
}

/* --- Coluna Direita (Bloco de Texto) - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-offer-content-right {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important; /* Empilha Título -> Preço -> Descrição */
    margin: 0 !important;
    padding: 0 !important;
}

/* --- Área do Título ('Oferta Única') - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-offer {
    width: 100% !important;
    margin: 0 0 8px 0 !important;
    padding: 0 !important;
    order: 1 !important;            /* Título Primeiro */
    text-align: left !important;
}

.wcf-bump-order-wrap .wcf-bump-order-bump-highlight {
    font-weight: 600 !important;

    color: #343a40 !important;
    display: block !important;
    line-height: 1.3 !important;
}

/* --- Container da Descrição - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-desc {
    order: 2 !important;            /* Descrição (com preço) vem depois */
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    font-size: 0.95em !important;
    line-height: 1.6 !important;
    color: #6c757d !important;
}

/* --- Parágrafo DENTRO da Descrição - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-desc p {
    margin: 0 !important;
    padding: 0 !important;
}

/* --- Span do Preço DENTRO do Parágrafo - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
    display: block !important;      /* Preço na linha de cima */
 
    color: #212529 !important;
    line-height: 1.2 !important;
    margin-bottom: 12px !important; /* Espaço abaixo */
}

/* Ocultar <br> extras */
.wcf-bump-order-wrap .wcf-bump-order-desc p br {
    display: none !important;
}
.wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price + br,
.wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price + .nbsp {
    display: none !important;
}



}

/* Ajustes finos para telas MUITO pequenas (Ex: abaixo de 400px) */
@media (max-width: 480px) {
    .wcf-bump-order-wrap .wcf-content-container {
        padding: 15px !important;
        gap: 12px !important;
    }
     .wcf-bump-order-wrap .wcf-image {
        width: 0px !important; /* Pode reduzir um pouco mais se necessário */
     }
     .wcf-bump-order-wrap .wcf-bump-order-bump-highlight {
        font-size: 1.05em !important;
     }
    .wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
        font-size: 1.4em !important;
     }
     .wcf-bump-order-wrap .wcf-bump-order-desc p { /* Aplica ao texto da descrição no <p> */
        font-size: 0.85em !important;
     }
}
@media (max-width: 600px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
        padding: 15px 16px 0 16px !important;
        margin: 0px !important;
    }
}

@media only screen and (min-width: 768px) {
    @media only screen and (min-width: 768px) {
        .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
            padding: 16px 24px 0 24px !important;
            margin: 0px !important;
        }
    }
}
@m

@media only screen and (max-width: 768px) {
    @media only screen and (min-width: 768px) {
        .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
            padding: 16px 24px 0 24px !important;
            margin: 0px !important;
        }
    }
}
@media only screen and (max-width: 768px) {
.mp-checkout-custom-card-form .mp-checkout-custom-card-row {

    padding-bottom: 16px !important;
}
}
@media only screen and (max-width: 768px) {
div#mp-card-holder-div {
	
    padding-bottom: 16px !important;
	
}
}


@media only screen and (max-width: 768px) {
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment label {

    padding: 12px !important;
}
}


/* Ajustes finos para telas MUITO pequenas (Ex: abaixo de 400px) */
@media (max-width: 480px) {
    .wcf-bump-order-wrap .wcf-content-container {
        padding: 15px !important;
        gap: 12px !important;
    }
     .wcf-bump-order-wrap .wcf-image {
        width: 60px !important; /* Pode reduzir um pouco mais se necessário */
     }
     .wcf-bump-order-wrap .wcf-bump-order-bump-highlight {
        font-size: 1.05em !important;
     }
    .wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
        font-size: 1.4em !important;
     }
     .wcf-bump-order-wrap .wcf-bump-order-desc p { /* Aplica ao texto da descrição no <p> */
        font-size: 0.85em !important;
     }
}


@media (max-width: 480px) {
.wcf-bump-order-field-wrap {
    padding: 14px !important;
}
}





/* CSS de suporte para o JavaScript */
.wcf-bump-order-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
    margin-bottom: 10px !important;
}

.wcf-bump-order-header .wcf-bump-order-offer {
    display: block !important;
    margin: 0 !important;
}

.wcf-bump-order-header .wcf-normal-price {
    display: block !important;
    margin: 0 !important;
    font-weight: bold !important;
}

/* Ajustes para mobile */
@media only screen and (max-width: 767px) {
    .wcf-bump-order-header {
        padding: 0 5px !important;
    }
}

@media only screen and (max-width: 767px) {
span.wcf-normal-price {
    text-align: left !important;
}
}
@media only screen and (max-width: 767px) {
.wcf-bump-order-desc {
    text-align: left !important;
}
}


@media (max-width: 480px) {
    .wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
        font-size: 14px !important;
    }
}
@media (max-width: 480px) {
span.woocommerce-Price-amount.amount {
    font-size: 14px !important;
}
}


.wc_payment_method > input[type="radio"]:checked + label {
    background: #f9fafb !important;
    border-color: #3BAE7E !important;
    /* border-width: 2px !important; */
    border-style: solid !important;
    background-color: #e8f5e99e !important;
}























/* CSS para o aviso de pagamento seguro */
.secure-payment-notice {
    display: flex;
    align-items: center;
    color: #2ecc71;
    margin: 15px 0;
    font-size: 14px;
    line-height: 1.4;
}

.secure-payment-notice svg {
    margin-right: 8px;
    flex-shrink: 0;
}

.secure-payment-notice span {
    color: #2ecc71;
}

/* Versão mobile - ícone acima do texto centralizado */
@media only screen and (max-width: 767px) {
    .secure-payment-notice {
        flex-direction: column !important;
        text-align: center !important;
        justify-content: center !important;
    }
    
    .secure-payment-notice svg {
        margin-right: 0 !important;
        margin-bottom: 8px !important;
    }
    
    .secure-payment-notice span {
        text-align: center !important;
        width: 100% !important;
    }
}



li.wc_payment_method.payment_method_woo-mercado-pago-pix {
    margin: 0px !important;
}

</style><style class="wpcode-css-snippet">/* Estilos para a seção de pagamento */
#payment {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-top: 20px;
}
@media (max-width: 600px) {
#payment {
  
    padding: 0px;
  
}
}


@media (max-width: 600px) {
.mp-checkout-custom-container {
    padding: 0px !important;
}
}
div#mp-checkout-custom-installments {
    padding: 0px !important;
}


@media (max-width: 600px) {
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
    padding: 15px 16px 0 16px !important;

}
}
.wcf-embed-checkout-form .woocommerce-checkout #payment {
   
    border-radius: 8px !important;
 
}


</style><style class="wpcode-css-snippet">/* --- Reset Básico para o Container LI --- */
li.wc_payment_method.payment_method_woo-mercado-pago-pix {
    background: none !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 0 20px 0 !important; /* Aumentar margem inferior */
    list-style: none !important;
}

/* --- Esconder o Radio Button Original --- */
li.payment_method_woo-mercado-pago-pix input[type="radio"] {
    position: absolute !important;
    opacity: 0 !important;
    pointer-events: none !important;
    width: 1px !important;
    height: 1px !important;
}

/* --- Estilo do Label (Botão de Seleção PIX) --- */
li.payment_method_woo-mercado-pago-pix label {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    padding: 14px 22px !important; /* Levemente mais padding */
    border: 1px solid #dee2e6 !important; /* Borda cinza um pouco mais visível */
    border-radius: 8px !important;
    cursor: pointer;
    background-color: #ffffff !important;
    font-weight: 500 !important;
    font-size: 1rem !important;
    color: #495057 !important;
    transition: all 0.2s ease-in-out !important;
    box-sizing: border-box !important;
    margin-bottom: 15px !important; /* Espaço antes da caixa */
}

/* --- Estilo do Ícone Pequeno Dentro do Label (Mercado Pago) --- */
li.payment_method_woo-mercado-pago-pix label img {
    width: 22px !important; /* Ligeiramente maior */
    height: 22px !important;
    margin-right: 12px !important;
    flex-shrink: 0;
}

/* --- Estilo do Label QUANDO SELECIONADO (Mais Refinado) --- */
li.payment_method_woo-mercado-pago-pix input[type="radio"]:checked + label {
            border-color: #3BAE7E !important;
            /* border-width: 2px !important; */
            border-style: solid !important;
                  background-color: #e8f5e99e !important;
            /* box-shadow: 0 0 0 2px rgba(59, 174, 126, 0.25) !important;
   
}

/* --- Caixa de Detalhes do Pagamento (Com mais presença) --- */
.payment_box.payment_method_woo-mercado-pago-pix {
    padding: 35px 25px !important; /* Mais padding interno */
    background-color: #f8f9fa !important; /* Fundo cinza claro */
    border: 1px solid #e9ecef !important; /* Borda sutil um pouco mais escura que o fundo */
    border-radius: 8px !important;
    margin-top: 0 !important;
    text-align: center !important;
    clear: both !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important; /* Sombra externa muito sutil */
    position: relative; /* Para posicionamento de elementos internos se necessário */
}

/* --- Container Interno (Flexbox para alinhar conteúdo) --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-container {
    border: none !important;
    padding: 0 !important;
    background-color: transparent !important;
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

/* --- SEU SVG Principal --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-image {
    content: url('data:image/svg+xml;utf8,<svg width="80" height="80" viewBox="0 0 193 193" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M143.749 141.252C140.491 141.259 137.264 140.621 134.253 139.375C131.243 138.128 128.509 136.298 126.21 133.99L100.879 108.659C99.982 107.807 98.7923 107.332 97.5555 107.332C96.3186 107.332 95.129 107.807 94.2323 108.659L68.8045 134.087C66.5059 136.396 63.7724 138.227 60.7619 139.473C57.7515 140.72 54.5239 141.357 51.2656 141.348H46.2718L78.358 173.435C88.3699 183.446 104.618 183.446 114.63 173.435L146.801 141.252H143.749ZM51.2656 51.6516C57.9 51.6516 64.1243 54.233 68.8045 58.9132L94.2323 84.341C94.6692 84.7787 95.1882 85.1259 95.7594 85.3628C96.3307 85.5997 96.9431 85.7216 97.5615 85.7216C98.18 85.7216 98.7923 85.5997 99.3636 85.3628C99.9349 85.1259 100.454 84.7787 100.891 84.341L126.222 59.0097C128.519 56.702 131.251 54.872 134.259 53.6257C137.267 52.3793 140.493 51.7412 143.749 51.7481H146.801L114.63 19.5774C109.819 14.7696 103.296 12.0689 96.494 12.0689C89.6924 12.0689 83.1691 14.7696 78.358 19.5774L46.2718 51.6637L51.2656 51.6516Z" fill="%2330BEAF"/><path d="M173.423 78.358L153.978 58.9132C153.541 59.0925 153.075 59.1867 152.603 59.1907H143.761C139.189 59.1907 134.714 61.0483 131.493 64.281L106.162 89.6123C105.036 90.7447 103.696 91.6434 102.221 92.2566C100.746 92.8698 99.1649 93.1855 97.5675 93.1855C95.9702 93.1855 94.3886 92.8698 92.9137 92.2566C91.4387 91.6434 90.0995 90.7447 88.973 89.6123L63.5453 64.1725C60.2829 60.9236 55.8698 59.0941 51.2656 59.0821H40.4094C39.9593 59.0785 39.5137 58.9927 39.0946 58.8288L19.5774 78.358C9.56557 88.3699 9.56557 104.618 19.5774 114.642L39.0946 134.159C39.5091 133.992 39.9506 133.902 40.3973 133.894H51.2656C55.8494 133.894 60.3125 132.048 63.5453 128.815L88.9609 103.376C91.28 101.168 94.3594 99.9361 97.5615 99.9361C100.764 99.9361 103.843 101.168 106.162 103.376L131.493 128.707C134.714 131.94 139.189 133.785 143.761 133.785H152.603C153.085 133.785 153.556 133.906 153.978 134.075L173.423 114.63C183.434 104.618 183.434 88.3699 173.423 78.358Z" fill="%2330BEAF"/></svg>') !important;
    width: 65px !important; /* Tamanho um pouco menor para mais respiro */
    height: 65px !important;
    display: block !important;
    margin: 0 auto 25px auto !important; /* Garante centralização e margem inferior */
    padding: 0 !important;
    background: none !important;
    border: none !important;
}

/* --- Estilo do Título Principal na Caixa PIX (Mais Presença) --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-title {
    font-size: 1.25em !important; /* Um pouco maior */
    font-weight: 600 !important;
    color: #343a40 !important; /* Cinza mais escuro */
    margin: 0 0 12px 0 !important; /* Mais espaço abaixo */
    line-height: 1.4 !important;
}


        li.payment_method_woo-mercado-pago-custom input[type="radio"]:checked + label {
            border-color: #3BAE7E !important;
            /* border-width: 2px !important; */
            border-style: solid !important;
            background-color: #e8f5e99e !important;
            /* box-shadow: 0 0 0 2px rgba(59, 174, 126, 0.25) !important; */
        }
  
/* --- Estilo do Subtítulo/Descrição na Caixa PIX (Clareza) --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-subtitle {
    font-size: 1em !important; /* Tamanho padrão para boa leitura */
    color: #495057 !important;
    line-height: 1.6 !important;
    margin: 0 auto !important;
    max-width: 90% !important;
}

/* --- Ocultar Termos e Condições (Mantido opcional) --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-checkout-pix-terms-and-conditions {
    display: none !important;
}

.secure-payment-notice {
  display: flex;          /* Alinha o ícone e o texto na mesma linha */
  align-items: center;    /* Centraliza verticalmente o ícone e o texto */
  color: #3BAE7E;       /* Define a cor para o texto e para o SVG (via currentColor) */
  font-size: 0.9em;       /* Tamanho de fonte um pouco menor para aviso */
  margin-top: 15px;       /* Adiciona um espaço acima do aviso (ajuste conforme necessário) */
  gap: 8px;               /* Espaço entre o ícone e o texto (alternativa a margin) */
}

/* Opcional: Se precisar ajustar o tamanho do ícone especificamente */
.secure-payment-notice svg {
  width: 16px;            /* Garante o tamanho */
  height: 16px;
  flex-shrink: 0;       /* Impede que o ícone encolha se o espaço for limitado */
}

/* Não é estritamente necessário se a cor for definida no container, mas para garantir: */
.secure-payment-notice span {
  line-height: 1.4;     /* Melhora a leitura se o texto quebrar linha */
}

</style><script>/**
 * Função para remover os asteriscos (*) dos campos obrigatórios.
 */
function removerAsteriscosObrigatorios() {
    // Seleciona todos os spans com a classe 'required' dentro da área principal do formulário
    // Usamos #customer_details como um container comum, pode ajustar se necessário
    const asteriscos = document.querySelectorAll('#customer_details span.required');
    let contador = 0;

    asteriscos.forEach(span => {
        // Verificação extra: remove apenas se o conteúdo for exatamente '*' (ignorando espaços)
        // E se tiver o atributo aria-hidden="true" (mais específico do WooCommerce)
        if (span.textContent.trim() === '*' && span.getAttribute('aria-hidden') === 'true') {
            span.remove(); // Remove o elemento <span> inteiro
            contador++;
        }
    });

    if (contador > 0) {
        console.log(`Removidos ${contador} asteriscos de campos obrigatórios.`);
    }
}

/**
 * Inicializa a remoção dos asteriscos e observa mudanças futuras.
 */
function inicializarRemocaoAsteriscos() {
    console.log('Inicializando remoção de asteriscos...');

    // 1. Remove na carga inicial
    removerAsteriscosObrigatorios();

    // 2. Remove em atualizações AJAX do WooCommerce (comum)
    if (window.jQuery) {
        jQuery(document.body).on('updated_checkout init_checkout', function() {
            console.log('Evento de atualização do checkout detectado, removendo asteriscos novamente...');
            // Pequeno delay para garantir que o DOM foi atualizado pelo WooCommerce
            setTimeout(removerAsteriscosObrigatorios, 100);
        });
    } else {
        console.warn('jQuery não encontrado, não é possível observar eventos do WooCommerce de forma ideal.');
    }

    // 3. Usa MutationObserver para robustez máxima contra scripts que adicionam campos depois
    const observerAlvo = document.getElementById('customer_details'); // Observa a área principal
    if (observerAlvo) {
        const observer = new MutationObserver(function(mutationsList) {
            // Otimização simples: Se qualquer nó foi adicionado na subárvore,
            // apenas re-executa a função de remoção.
            for (let mutation of mutationsList) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Verifica se algum dos nós adicionados (ou seus descendentes) contêm um span.required
                    let precisaVerificar = false;
                    mutation.addedNodes.forEach(node => {
                        // Verifica se o próprio nó é um elemento e contém a classe ou se tem descendentes com a classe
                        if (node.nodeType === Node.ELEMENT_NODE) {
                           if (node.querySelector('span.required')) {
                               precisaVerificar = true;
                           }
                        }
                    });

                    if (precisaVerificar) {
                        console.log('Mutação no DOM detectada (nós adicionados), verificando asteriscos...');
                        // Espera um instante mínimo caso múltiplos scripts estejam agindo
                        setTimeout(removerAsteriscosObrigatorios, 50);
                        // Não precisa continuar checando outras mutações neste ciclo
                        return;
                    }

                }
            }
        });

        observer.observe(observerAlvo, {
            childList: true, // Observa adição/remoção de filhos diretos
            subtree: true    // Observa também toda a subárvore (importante!)
        });
        console.log('MutationObserver configurado para remoção de asteriscos.');

    } else {
        console.warn('Não foi possível encontrar #customer_details para configurar o MutationObserver.');
    }
}

// --- Execução ---
// Garante que o DOM esteja pronto antes de rodar
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', inicializarRemocaoAsteriscos);
} else {
    // DOM já está pronto
    inicializarRemocaoAsteriscos();
}</script><style class="wpcode-css-snippet">/* Garante que os itens fiquem empilhados verticalmente (se for flex) */
.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper {
  display: block !important; /* Tente block ou flex */
  /* Se usar flex, garanta a direção: */
  /* flex-direction: column !important; */
}

/* Reseta a ordem de TODOS os campos dentro do wrapper */
.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > p.form-row {
   order: initial !important; /* Tenta resetar a ordem */
   position: static !important; /* Tenta resetar posição */
   float: none !important; /* Tenta resetar float */
   margin-top: initial !important; /* Tenta resetar margem */
}

/* OU, você pode definir a ordem explicitamente (menos recomendado, mas funciona) */
/* Dê números sequenciais para a ordem visual desejada */
/* #billing_first_name_field { order: 1 !important; } */
/* #billing_email_field { order: 2 !important; } */
/* #billing_cpf_cnpj_field { order: 3 !important; } */
/* #billing_cellphone_field { order: 4 !important; } */
/* Continue para outros campos visíveis se necessário... */</style><script>/**
 * Function to arrange checkout fields: Put email right after name.
 */
function arrangeMyCheckoutFields_FinalAttempt() {
    console.log('Attempting to arrange checkout fields...');

    const nameFieldContainer = document.getElementById('billing_first_name_field');
    const emailFieldContainer = document.getElementById('billing_email_field');
    // Target the specific wrapper within the 'Dados Pessoais' section
    const wrapper = document.querySelector('.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper');

    if (nameFieldContainer && emailFieldContainer && wrapper) {
        // Check if the email field is DIRECTLY after the name field in the current DOM
        if (nameFieldContainer.nextElementSibling !== emailFieldContainer) {
            console.log('Email field NOT immediately after name. MOVING email field.');
            // Move the email field to be the direct next sibling of the name field
            nameFieldContainer.insertAdjacentElement('afterend', emailFieldContainer);
        } else {
            console.log('Email field already positioned correctly after name in HTML.');
        }

        // Ensure consistent full-width styling (helps prevent weird floats/wraps)
        nameFieldContainer.classList.add('form-row-wide');
        nameFieldContainer.classList.remove('form-row-first', 'wcf-column-50');
        emailFieldContainer.classList.add('form-row-wide');
        emailFieldContainer.classList.remove('form-row-fill');

    } else {
        console.error('Could not find all required elements (name, email, or wrapper).');
        if (!nameFieldContainer) console.error('- Name field (billing_first_name_field) missing.');
        if (!emailFieldContainer) console.error('- Email field (billing_email_field) missing.');
        if (!wrapper) console.error('- Wrapper (.woocommerce-billing-fields__field-wrapper) missing.');
    }
}

/**
 * Function to initialize all attempts to fix the layout.
 */
function initializeFieldArrangement() {
    console.log('Initializing field arrangement attempts...');

    // 1. Run immediately on DOM ready
    arrangeMyCheckoutFields_FinalAttempt();

    // 2. Run after a short delay (catch scripts running slightly after DOMContentLoaded)
    setTimeout(arrangeMyCheckoutFields_FinalAttempt, 500); // 0.5 seconds
    setTimeout(arrangeMyCheckoutFields_FinalAttempt, 1500); // 1.5 seconds (extra safe)

    // 3. Run on WooCommerce Checkout Update (Handles AJAX changes)
    if (window.jQuery) {
        jQuery(document.body).on('updated_checkout', function() {
            console.log('WooCommerce "updated_checkout" event triggered. Re-arranging fields...');
            arrangeMyCheckoutFields_FinalAttempt();
        });
    } else {
        console.warn('jQuery not found, cannot bind to WooCommerce update events reliably.');
    }

    // 4. Use MutationObserver to watch for ANY changes within the wrapper
    //    This is the strongest defense against other scripts rearranging things later.
    const targetWrapperNode = document.querySelector('.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper');
    if (targetWrapperNode) {
        console.log('Setting up MutationObserver to watch the billing fields wrapper.');
        const observerConfig = {
            childList: true // Watch for additions/removals/reordering of direct children
        };

        const observerCallback = function(mutationsList, observer) {
            // We don't need to inspect the mutations, just re-run our check
            console.log('Mutation detected in wrapper. Re-checking field order...');
            arrangeMyCheckoutFields_FinalAttempt();
        };

        const observer = new MutationObserver(observerCallback);
        observer.observe(targetWrapperNode, observerConfig);

        // Optional: You might want to disconnect the observer later if needed,
        // but for a checkout page, letting it run is usually fine.
        // Example: window.addEventListener('beforeunload', () => observer.disconnect());

    } else {
        console.error('Could not find wrapper node to attach MutationObserver.');
    }
}

// --- Start Execution ---
// Run the initialization function when the DOM is ready
if (document.readyState === 'loading') { // Loading hasn't finished yet
    document.addEventListener('DOMContentLoaded', initializeFieldArrangement);
} else { // `DOMContentLoaded` has already fired
    initializeFieldArrangement();
}</script><style class="wpcode-css-snippet">ul.wc_payment_methods.payment_methods.methods {
    border-radius: 8px !important;
}

.elementor-364 .elementor-element.elementor-element-2faaddc4 .wcf-embed-checkout-form .woocommerce-checkout #payment ul.payment_methods {
    border: 1px solid #bdbdbd5c !important;
}

span.wcf-field-required-error {
    position: absolute !important;
    opacity: 0 !important;
    pointer-events: none !important;
    height: 0 !important;
    overflow: hidden !important;
    visibility: hidden !important;
    /* Preserva a funcionalidade, mas oculta visualmente */
}

@media only screen and (max-width: 768px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce {
        padding: 0 0px !important;
    }
}

div#mp-doc-div {
    position: absolute !important;
    opacity: 0 !important;
    pointer-events: none !important;
    height: 0 !important;
    overflow: hidden !important;
    visibility: hidden !important;
    /* Preserva a funcionalidade, mas oculta visualmente */
}

.wcf-customer-info-main-wrapper {
    border: 1px solid #73737338 !important;
    background: #fff;
    padding-top: 0px !important; 
    padding-bottom: 28px !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box {
    border: none !important;
}

@media only screen and (min-width: 768px) {
    div#mp-doc-div {
        margin-top: -22px;
        position: absolute !important;
        opacity: 0 !important;
        pointer-events: none !important;
        height: 0 !important;
        visibility: hidden !important;
        /* Preserva a funcionalidade, mas oculta visualmente */
    }
	
    /* Mantemos posição absoluta e zero opacidade em vez de display:none */
    p#mp-security-code-info {
        position: absolute !important;
        opacity: 0 !important;
        pointer-events: none !important;
        height: 0 !important;
        overflow: hidden !important;
        visibility: hidden !important;
        /* Preserva a funcionalidade, mas oculta visualmente */
    }
}

.mp-checkout-custom-container {
    padding: 0px !important; 
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
    padding: 14px 16px 0 16px !important;
}

input#billing_first_name {
    color: black;
}

input#billing_cpf_cnpj {
    color: black;
}

input#billing_cellphone {
    color: black;
}

input#billing_email {
    color: black;
}

@media only screen and (min-width: 768px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
        padding: 0px 24px 0 24px !important;
    }

    .mp-checkout-custom-card-form .mp-checkout-custom-card-row {
        padding-bottom: 14px !important;
    }

    li.payment_method_woo-mercado-pago-custom input[type="radio"]:checked + label {
        border-color: #3BAE7E !important;
        border-style: solid !important;
        background-color: #e8f5e99e !important;
    }
}

table.shop_table.woocommerce-checkout-review-order-table {
    background-color: #f1f9f196 !important;
    border-radius: 6px !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout h3, 
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #ship-to-different-address, 
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #order_review_heading {
    margin: 22px 0 0px !important;
}

@media (max-width: 768px) {
    .accordion-wrapper.active .accordion-header {
        border-bottom-color: transparent !important; 
    }

    .accordion-header {
        padding: 0px !important;
    }
}

/* Estilo adicional para garantir que os inputs do Mercado Pago fiquem visíveis */
.mp-input-table-container,
.mp-input-table-list,
.mp-input-table-bank-interest-container {
    clip-path: none !important;
    clip: auto !important;
    /* Garante que permaneçam visíveis para o JavaScript */
}

 @media (min-width: 728px) {
.mp-pix-template-container {
    padding-bottom: 16px !important;
    padding-top: 0px !important;
}
}

.elementor-364 .elementor-element.elementor-element-2faaddc4 .wcf-embed-checkout-form .woocommerce-checkout #payment ul.payment_methods {
    border: none !important;
}</style><script>document.addEventListener('DOMContentLoaded', function() {
    // Função para remover asteriscos dos placeholders
    function removePlaceholderAsterisks() {
        const inputs = document.querySelectorAll('input[placeholder]');
        inputs.forEach(input => {
            let newPlaceholder = input.placeholder.replace(/\s*\*\s*/g, '');
            input.placeholder = newPlaceholder;
            
            // Observa mudanças no atributo placeholder
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.target.placeholder.includes('*')) {
                        mutation.target.placeholder = mutation.target.placeholder.replace(/\s*\*\s*/g, '');
                    }
                });
            });

            observer.observe(input, {
                attributes: true,
                attributeFilter: ['placeholder']
            });
        });
    }

    // Executa inicialmente
    removePlaceholderAsterisks();

    // Observa mudanças no DOM para novos inputs
    const domObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                removePlaceholderAsterisks();
            }
        });
    });

    domObserver.observe(document.body, {
        childList: true,
        subtree: true
    });
});</script><script>document.addEventListener('DOMContentLoaded', function() {
    // Função para mudar o texto
    function changeSecurityCodeText() {
        // Procura todos os elementos com a classe mp-input-label
        const labels = document.querySelectorAll('.mp-input-label');
        
        // Itera sobre cada elemento encontrado
        labels.forEach(label => {
            // Verifica se o texto contém "Código de segurança"
            if (label.textContent.includes('Código de segurança')) {
                // Substitui mantendo o asterisco vermelho
                label.innerHTML = 'CVV<b style="color: red;">*</b>';
            }
        });
    }

    // Executa a função inicialmente
    changeSecurityCodeText();

    // Observa mudanças no DOM para casos de carregamento dinâmico
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                changeSecurityCodeText();
            }
        });
    });

    // Configura o observer para observar todo o documento
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Adiciona listener para eventos de alteração do método de pagamento
    const paymentInputs = document.querySelectorAll('input[name="payment_method"]');
    paymentInputs.forEach(input => {
        input.addEventListener('change', function() {
            setTimeout(changeSecurityCodeText, 100);
        });
    });
});</script><style class="wpcode-css-snippet">/* Reset de estilos do CartFlows */
.wcf-collapsed-order-review-section {
display: none !important;
}

.accordion-wrapper {
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
background: white;
border-radius: 8px;
box-shadow: 0 1px 3px rgba(0,0,0,0.1);
margin-bottom: 20px;
padding: 16px;
display: block !important;
width: 100% !important;
box-sizing: border-box;
}

/* Estilos do cabeçalho */
.header-left span {
color: black;
font-weight: 600;
}

.header-right span.total-amount {
color: black;
}

.accordion-header {
display: flex;
justify-content: space-between;
align-items: center;
cursor: pointer;
padding: 8px 0;
width: 100%;
}

.header-content {
display: flex;
align-items: center;
justify-content: space-between;
width: 100%;
}

.header-left {
display: flex;
align-items: center;
gap:12px !important;
}

.header-right {
display: flex;
align-items: center;
gap: 12px;
}

/* Ícones */
.cart-icon {
stroke: currentColor;
}
.chevron-icon {
transition: transform 0.3s ease;
}

/* Conteúdo do acordeon */
.accordion-content {
display: none;
padding-top: 16px;
width: 100%;
}

.accordion-wrapper.active .accordion-content {
display: block !important;
}

.accordion-wrapper.active .chevron-icon {
transform: rotate(180deg);
}

/* Estilos da tabela */
.shop_table {
width: 100%;
border-collapse: collapse;
margin-top: 10px;
}
.cartflows_table thead {
background-color: #f8f9fa;
}

.cartflows_table th,
.cartflows_table td {
padding: 12px;
text-align: left;
}
@media screen and (max-width: 769px) {
.wcf-product-image {
display: flex;
align-items: center;
gap: 0px !important;
}
}

@media screen and (min-width: 769px) {
table.shop_table.woocommerce-checkout-review-order-table {
    /* padding: 50px !important; */
    padding-right: 20px !important;
    padding-left: 20px !important;
}
}
@media screen and (max-width: 769px) {
.wcf-embed-checkout-form table.shop_table thead tr th:nth-child( 2 ), .wcf-embed-checkout-form table.shop_table tbody tr td:nth-child( 2 ), .wcf-embed-checkout-form table.shop_table tfoot tr td:nth-child( 2 ) {

    padding-left: 30px !important;
}
}
.wcf-product-thumbnail img {
width: 48px;
height: 48px;
object-fit: cover;
border-radius: 4px;
}
@media (max-width: 768px) {
.accordion-wrapper.mobile-only {
    margin: 0 !important;
}
}
@media (max-width: 768px) {
table.shop_table.woocommerce-checkout-review-order-table.cartflows_table {
    background: #f9fafb;
    border-radius: 4px !important;
}
}

@media (max-width: 768px) {
.wcf-product-thumbnail img {
    width: 48px !important;
    height: 48px !important;
  
}
}

@media (max-width: 768px) {
h3#billing_fields_heading {
   
    margin-top: -0.1px !important;
}
}


li.payment_method_woo-mercado-pago-pix label {
   
    color: #000000 !important;
  
}


.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box {
    border-top: none !important;
  
}

p.mp-pix-template-title {
    font-weight: 600 !important;
}
@media (max-width: 768px) {
/* Garantir visibilidade do wrapper */
#order_review,
.woocommerce-checkout-review-order-table {
display: block !important;
width: 100% !important;
}
}

/* Ajustes para o container principal */
form.checkout {
width: 100% !important;
max-width: 100% !important;
}
pix-template,
.mp-pix-template-container,
.mp-pix-template-title,
.mp-pix-template-subtitle {
    font-family: "Inter", sans-serif !important;
}</style><script>document.addEventListener('DOMContentLoaded', function () {
    // Selecionar o campo de CEP
    const cepField = document.querySelector('#billing_postcode');
    const cepFieldWrapper = document.querySelector('#billing_postcode_field');

    if (cepField && cepFieldWrapper) {
        // Remover atributos de validação
        cepField.removeAttribute('required');
        cepField.removeAttribute('aria-required');
        cepFieldWrapper.classList.remove('validate-required');

        // Interceptar o envio do formulário
        const checkoutForm = document.querySelector('form.checkout');
        if (checkoutForm) {
            checkoutForm.addEventListener('submit', function (e) {
                // Preencher o CEP com um valor válido para ignorar a validação
                cepField.value = '00000-000';
            });
        }

        // Opcional: Ocultar o campo visualmente
        cepFieldWrapper.style.display = 'none';
    }
});
</script>			<meta name="theme-color" content="#141414">
			<style class='wp-fonts-local'>
@font-face{font-family:Inter;font-style:normal;font-weight:300 900;font-display:fallback;src:url('https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/fonts/Inter-VariableFont_slnt,wght.woff2') format('woff2');font-stretch:normal;}
@font-face{font-family:Cardo;font-style:normal;font-weight:400;font-display:fallback;src:url('https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/fonts/cardo_normal_400.woff2') format('woff2');}
</style>
</head>

<body class="wp-singular cartflows_step-template cartflows_step-template-cartflows-canvas single single-cartflows_step postid-364 wp-embed-responsive wp-theme-hello-elementor theme-hello-elementor woocommerce-checkout woocommerce-page woocommerce-no-js theme-default cartflows-2.1.14  cartflows-pro-2.1.2 elementor-default elementor-kit-9 elementor-page elementor-page-364 cartflows-canvas">

	
			<div class="cartflows-container" >

			<div data-elementor-type="wp-post" data-elementor-id="364" class="elementor elementor-364" data-elementor-post-type="cartflows_step">
				<div class="elementor-element elementor-element-1b53e36 e-flex e-con-boxed e-con e-parent" data-id="1b53e36" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
					<div class="e-con-inner">
				<div class="elementor-element elementor-element-65bb08a elementor-position-left elementor-mobile-position-left elementor-view-default elementor-vertical-align-top elementor-widget elementor-widget-icon-box" data-id="65bb08a" data-element_type="widget" data-widget_type="icon-box.default">
				<div class="elementor-widget-container">
							<div class="elementor-icon-box-wrapper">

						<div class="elementor-icon-box-icon">
				<span  class="elementor-icon">
				<svg xmlns="http://www.w3.org/2000/svg" id="Layer_2" height="512" viewBox="0 0 512 512" width="512" data-name="Layer 2"><path d="m429.42 144.33-169.78-66.4a10 10 0 0 0 -7.28 0l-169.78 66.4a10 10 0 0 0 -6.31 8.27c-3.62 35.4-12.18 215.11 176.43 281.75a10 10 0 0 0 6.6 0c188.61-66.64 180.05-246.35 176.43-281.75a10 10 0 0 0 -6.31-8.27zm-88.82 73.67-99.73 94.25a12.49 12.49 0 0 1 -17.43-.25l-52.3-52.3a12.5 12.5 0 0 1 17.68-17.7l43.71 43.7 90.9-85.9a12.5 12.5 0 1 1 17.17 18.2z"></path><path d="m255.98 76.5.02.01.02-.01z"></path><path d=""></path></svg>				</span>
			</div>
			
						<div class="elementor-icon-box-content">

									<h3 class="elementor-icon-box-title">
						<span  >
							COMPRA SEGURA						</span>
					</h3>
				
				
			</div>
			
		</div>
						</div>
				</div>
					</div>
				</div>
		<div class="elementor-element elementor-element-cbd0793 e-flex e-con-boxed e-con e-parent" data-id="cbd0793" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
					<div class="e-con-inner">
		<div class="elementor-element elementor-element-e3ab6a2 e-con-full e-flex e-con e-child" data-id="e3ab6a2" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
				<div class="elementor-element elementor-element-528d2f8 elementor-widget elementor-widget-heading" data-id="528d2f8" data-element_type="widget" data-widget_type="heading.default">
				<div class="elementor-widget-container">
					<h2 class="elementor-heading-title elementor-size-default">Promoção por tempo limitado
</h2>				</div>
				</div>
				<div class="elementor-element elementor-element-c7aef7b elementor-widget elementor-widget-html" data-id="c7aef7b" data-element_type="widget" data-widget_type="html.default">
				<div class="elementor-widget-container">
					
    <style>
  
/* Importação da fonte Inter (caso necessário) */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

.timer-container,
.timer-container *,
.time-box,
.label,
.separator,
.time-group {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
    letter-spacing: -0.02em !important;
}

.timer-container {
    display: flex;
    align-items: center;
    gap: clamp(8px, 2vw, 10px);
    padding: clamp(15px, 4vw, 20px);
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    justify-content: center;
}

.time-box {
    background-color: white;
    color: black;
    padding: clamp(6px, 2vw, 12px);
    border-radius: 6px;
    font-size: clamp(18px, 5vw, 30px);  /* Corrigi o valor máximo que estava 0px */
    font-weight: 600 !important;
    min-width: clamp(40px, 10vw, 45px);
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #eee;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
}

.separator {
    margin-top: -20px;
    font-size: clamp(20px, 2vw, 24px);  /* Corrigi o valor máximo que estava 5px */
    font-weight: bold;
    color: #fff;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
}

.label {
    font-size: clamp(10px, 3vw, 12px);
    color: #fff;
    text-align: center;
    margin-top: 4px;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
    font-weight: 500 !important;
}

.time-group {
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Força a fonte Inter nos números */
#hours,
#minutes,
#seconds {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
    font-feature-settings: "tnum" on, "lnum" on !important;
    font-variant-numeric: tabular-nums !important;
}

@media (max-width: 280px) {
    .timer-container {
        gap: 4px;
        padding: 10px;
    }

    .time-box {
        min-width: 35px;
        padding: 4px;
    }
}
    </style>
</head>
<body>
    <div class="timer-container">
        <div class="time-group">
            <div class="time-box" id="hours">00</div>
            <div class="label">horas</div>
        </div>
        <div class="separator">:</div>
        <div class="time-group">
            <div class="time-box" id="minutes">08</div>
            <div class="label">min</div>
        </div>
        <div class="separator">:</div>
        <div class="time-group">
            <div class="time-box" id="seconds">57</div>
            <div class="label">seg</div>
        </div>
    </div>

    <script>
        // Set the initial time (in seconds)
        let totalSeconds = 8 * 60 + 57; // 8 minutes and 57 seconds

        function updateTimer() {
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;

            // Update the display
            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');

            if (totalSeconds > 0) {
                totalSeconds--;
                setTimeout(updateTimer, 1000);
            }
        }

        // Start the timer when the page loads
        window.onload = updateTimer;
    </script>
</body>
</html>				</div>
				</div>
				</div>
					</div>
				</div>
		<div class="elementor-element elementor-element-ab0b4c0 e-flex e-con-boxed e-con e-parent" data-id="ab0b4c0" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
					<div class="e-con-inner">
		<div class="elementor-element elementor-element-4a5ca53 e-con-full e-flex e-con e-child" data-id="4a5ca53" data-element_type="container">
		<div class="elementor-element elementor-element-1dc7193 e-con-full elementor-hidden-desktop elementor-hidden-tablet elementor-hidden-mobile e-flex e-con e-child" data-id="1dc7193" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
				<div class="elementor-element elementor-element-0ecc505 elementor-widget elementor-widget-heading" data-id="0ecc505" data-element_type="widget" data-widget_type="heading.default">
				<div class="elementor-widget-container">
					<h2 class="elementor-heading-title elementor-size-default">Promoção por tempo limitado
</h2>				</div>
				</div>
				<div class="elementor-element elementor-element-b1d2a14 elementor-widget elementor-widget-html" data-id="b1d2a14" data-element_type="widget" data-widget_type="html.default">
				<div class="elementor-widget-container">
					
    <style>
  

        .timer-container {
            display: flex;
            align-items: center;
            gap: clamp(8px, 2vw, 10px);
            font-family: consolas;
            padding: clamp(15px, 4vw, 20px);
            border-radius: 8px;
            width: 90%;
            max-width: 400px;
            justify-content: center;
        }

        .time-box {
            background-color: white;
            color: black;
            padding: clamp(6px, 2vw, 12px);
            border-radius: 6px;
            font-size: clamp(18px, 5vw, 0px);
            font-weight: 600;
            min-width: clamp(40px, 10vw, 45px);
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #eee;
        }

        .separator {
            margin-top: -20px;
            font-size: clamp(20px, 2vw, 5px);
            font-weight: bold;
            color: #fff;
        }

        .label {
            font-size: clamp(10px, 3vw, 12px);
            color: #fff;
            text-align: center;
            margin-top: 4px;
        }

        .time-group {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        @media (max-width: 280px) {
            .timer-container {
                gap: 4px;
                padding: 10px;
            }

            .time-box {
                min-width: 35px;
                padding: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="timer-container">
        <div class="time-group">
            <div class="time-box" id="hours">00</div>
            <div class="label">horas</div>
        </div>
        <div class="separator">:</div>
        <div class="time-group">
            <div class="time-box" id="minutes">08</div>
            <div class="label">min</div>
        </div>
        <div class="separator">:</div>
        <div class="time-group">
            <div class="time-box" id="seconds">57</div>
            <div class="label">seg</div>
        </div>
    </div>

    <script>
        // Set the initial time (in seconds)
        let totalSeconds = 8 * 60 + 57; // 8 minutes and 57 seconds

        function updateTimer() {
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;

            // Update the display
            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');

            if (totalSeconds > 0) {
                totalSeconds--;
                setTimeout(updateTimer, 1000);
            }
        }

        // Start the timer when the page loads
        window.onload = updateTimer;
    </script>
</body>
</html>				</div>
				</div>
				</div>
				<div class="elementor-element elementor-element-a035089 elementor-widget elementor-widget-image" data-id="a035089" data-element_type="widget" data-widget_type="image.default">
				<div class="elementor-widget-container">
															<img fetchpriority="high" decoding="async" width="975" height="400" src="https://pay.desyne.pro/wp-content/uploads/2024/08/sd.png" class="attachment-full size-full wp-image-989" alt="" srcset="https://pay.desyne.pro/wp-content/uploads/2024/08/sd.png 975w, https://pay.desyne.pro/wp-content/uploads/2024/08/sd-600x246.png 600w, https://pay.desyne.pro/wp-content/uploads/2024/08/sd-300x123.png 300w, https://pay.desyne.pro/wp-content/uploads/2024/08/sd-768x315.png 768w" sizes="(max-width: 975px) 100vw, 975px" />															</div>
				</div>
				<div class="elementor-element elementor-element-2faaddc4 elementor-widget-mobile__width-inherit elementor-widget elementor-widget-checkout-form" data-id="2faaddc4" data-element_type="widget" data-widget_type="checkout-form.default">
				<div class="elementor-widget-container">
							<div class = "wcf-el-checkout-form cartflows-elementor__checkout-form">
			<div id="wcf-embed-checkout-form" class="wcf-embed-checkout-form wcf-embed-checkout-form-modern-checkout wcf-modern-skin-one-column wcf-field-modern-label">
<!-- CHECKOUT SHORTCODE -->

<div class="woocommerce"><div class="woocommerce-notices-wrapper"></div>
<!-- Mobile responsive order review template -->
<div class="wcf-collapsed-order-review-section  order-review-summary-position-top">
	<div class='wcf-order-review-toggle'>
		<div class='wcf-order-review-toggle-button-wrap'>
			<span class='wcf-order-review-toggle-text'>Show Order Summary</span>
			<span class='wcf-order-review-toggle-button cartflows-icon cartflows-cheveron-down'></span>
			<span class='wcf-order-review-toggle-button cartflows-icon cartflows-cheveron-up'></span>
		</div>
		<div class='wcf-order-review-total'>&#082;&#036;&nbsp;39,00</div>
	</div>

	<div class="wcf-cartflows-review-order-wrapper">
		
<table class="shop_table woocommerce-checkout-review-order-table cartflows_table" data-update-time="1752310756">
	<thead>
		<tr>
			<th class="product-name">Produto</th>
			<th class="product-total">Subtotal</th>
		</tr>
	</thead>
	<tbody>
						<tr class="cart_item">
					<td class="product-name">
						ElemenIA&nbsp;						 <strong class="product-quantity">&times;&nbsp;1</strong>											</td>
					<td class="product-total">
						<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#82;&#36;</span>&nbsp;39,00</bdi></span>					</td>
				</tr>
					</tbody>
	<tfoot>
		<tr class="cart-subtotal">
			<th>Subtotal</th>
			<td><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#82;&#36;</span>&nbsp;39,00</bdi></span></td>
		</tr>
										<tr class="order-total">
			<th>Total</th>
			<td><strong><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#82;&#36;</span>&nbsp;39,00</bdi></span></strong> </td>
		</tr>

		
	</tfoot>
</table>

			</div>
</div>
<div class="woocommerce-notices-wrapper"></div>
<form name="checkout" method="post" class="checkout woocommerce-checkout" action="https://pay.desyne.pro" enctype="multipart/form-data">

	
		<div class='wcf-bump-order-grid-wrap wcf-all-bump-order-wrap wcf-before-checkout' data-update-time='1752310756'></div><div class="wcf-customer-info-main-wrapper">
		<div class="wcf-col2-set col2-set" id="customer_details">
			<div class="wcf-col-1 col-1">
							<div class="wcf-customer-info" id="customer_info">
				<div class="wcf-customer-info__notice"></div>
				<div class="woocommerce-billing-fields-custom">
					<h3 id="customer_information_heading">Customer information											</h3>
					<div class="woocommerce-billing-fields__customer-info-wrapper">
					<p class="form-row form-row-fill validate-required" id="billing_email_field" data-priority=""><label for="billing_email" class="required_field">Email Address&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="email" class="input-text " name="billing_email" id="billing_email" placeholder="Email Address &#042;"  value="" aria-required="true" autocomplete="email username" /></span></p>																</div>
				</div>
			</div>
		<wc-order-attribution-inputs></wc-order-attribution-inputs><div class="woocommerce-billing-fields">
	
		<h3 id="billing_fields_heading">Dados Pessoais</h3>

	
	
	<div class="woocommerce-billing-fields__field-wrapper">
		<p class="form-row form-row-first wcf-column-50 validate-required" id="billing_first_name_field" data-priority="10"><label for="billing_first_name" class="required_field">Preencha seu nome&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_first_name" id="billing_first_name" placeholder="Preencha seu nome&nbsp;*"  value="" aria-required="true" autocomplete="given-name" /></span></p><p class="form-row form-row-wide validate-required" id="billing_cpf_cnpj_field" data-priority="21"><label for="billing_cpf_cnpj" class="required_field">CPF/CNPJ&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_cpf_cnpj" id="billing_cpf_cnpj" placeholder="CPF/CNPJ&nbsp;*"  value="" aria-required="true" /></span></p><p class="form-row form-row-wide validate-required" id="billing_cellphone_field" data-priority="22"><label for="billing_cellphone" class="required_field">Celular&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="tel" class="input-text " name="billing_cellphone" id="billing_cellphone" placeholder="Celular&nbsp;*"  value="" aria-required="true" /></span></p><p class="form-row form-row-wide validate-required" id="billing_postcode_field" data-priority="23"><label for="billing_postcode" class="required_field">CEP&nbsp;<span class="required" aria-hidden="true">*</span></label><span class="woocommerce-input-wrapper"><input type="tel" class="input-text " name="billing_postcode" id="billing_postcode" placeholder="CEP&nbsp;*"  value="" aria-required="true" /></span></p><input type="hidden" id="billing_persontype" name="billing_persontype" value="" class="input-hidden" /> <input type="hidden" id="billing_cpf" name="billing_cpf" value="" class="input-hidden" /> <input type="hidden" id="billing_cnpj" name="billing_cnpj" value="" class="input-hidden" /> <p class="form-row hidden" id="billing_address_1_field" data-priority=""><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_address_1" id="billing_address_1" placeholder=""  value=""  /></span></p><p class="form-row hidden" id="billing_city_field" data-priority=""><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_city" id="billing_city" placeholder=""  value=""  /></span></p><p class="form-row hidden" id="billing_state_field" data-priority=""><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_state" id="billing_state" placeholder=""  value="GO"  /></span></p><p class="form-row hidden" id="billing_neighborhood_field" data-priority=""><span class="woocommerce-input-wrapper"><input type="text" class="input-text " name="billing_neighborhood" id="billing_neighborhood" placeholder=""  value=""  /></span></p><input type="hidden" id="billing_number" name="billing_number" value="123" class="input-hidden" /> <input type="hidden" id="billing_phone" name="billing_phone" value="" class="input-hidden" /> 	</div>

	</div>

			</div>

			<div class="wcf-col-2 col-2">
				
<div class="woocommerce-shipping-fields">
	</div>
<div class="woocommerce-additional-fields">
	
	
	<input type="hidden" class="input-hidden _wcf_flow_id" name="_wcf_flow_id" value="363"><input type="hidden" class="input-hidden _wcf_checkout_id" name="_wcf_checkout_id" value="364"></div>
			</div>
		</div>

		<div class='wcf-customer-shipping'></div><div class='wcf-bump-order-grid-wrap wcf-all-bump-order-wrap wcf-after-customer' data-update-time='1752310756'></div></div>
	
	<div class='wcf-order-wrap'>

		
		
		<h3 id="order_review_heading">COMPRA 100% SEGURA</h3>

		
		<div id="order_review" class="woocommerce-checkout-review-order">
			<table class="shop_table woocommerce-checkout-review-order-table" data-update-time="1752310756">
	<thead>
		<tr>
			<th class="product-name">Produto</th>
			<th class="product-total">Subtotal</th>
		</tr>
	</thead>
	<tbody>
						<tr class="cart_item">
					<td class="product-name">
						ElemenIA&nbsp;						 <strong class="product-quantity">&times;&nbsp;1</strong>											</td>
					<td class="product-total">
						<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#82;&#36;</span>&nbsp;39,00</bdi></span>					</td>
				</tr>
					</tbody>
	<tfoot>
				<tr class="cart-subtotal">
			<th>Subtotal</th>
			<td><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#82;&#36;</span>&nbsp;39,00</bdi></span></td>
		</tr>

				
				
		
		<tr class="order-total">
			<th>Total</th>
			<td><strong><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#82;&#36;</span>&nbsp;39,00</bdi></span></strong> </td>
		</tr>

		
	</tfoot>
</table>
<div class='wcf-bump-order-grid-wrap wcf-all-bump-order-wrap wcf-after-order' data-update-time='1752310756'></div>		<div class="wcf-payment-option-heading">
			<h3 id="payment_options_heading">Payment</h3>
		</div>
		<div id="payment" class="woocommerce-checkout-payment">
			<ul class="wc_payment_methods payment_methods methods">
			<li class="wc_payment_method payment_method_woo-mercado-pago-pix">
	<input id="payment_method_woo-mercado-pago-pix" type="radio" class="input-radio" name="payment_method" value="woo-mercado-pago-pix"  checked='checked' data-order_button_text="" />

	<label for="payment_method_woo-mercado-pago-pix">
		Pix <img decoding="async" src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/icons/icon-pix.png?ver=8.2.0" alt="Pix" />	</label>
			<div class="payment_box payment_method_woo-mercado-pago-pix" >
			
<div class='mp-checkout-container'>
     
        <div class="mp-checkout-pix-container">
            
            <pix-template
                title="Pague de forma segura e instantânea"
                subtitle="Ao confirmar a compra, nós vamos te mostrar o código para fazer o pagamento."
                alt="Logo Pix"
                src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/pix/pix.png?ver=8.2.0">
            </pix-template>

            <div class="mp-checkout-pix-terms-and-conditions">
                <terms-and-conditions
                    description="Ao continuar, você concorda com nossos"
                    link-text="Termos e condições"
                    link-src="https://www.mercadopago.com.br/ajuda/termos-e-politicas_194">
                </terms-and-conditions>
            </div>
        </div>
     
</div>

<script type="text/javascript">
    if (document.getElementById("payment_method_woo-mercado-pago-custom")) {
        jQuery("form.checkout").on("checkout_place_order_woo-mercado-pago-pix", function() {
            cardFormLoad();
        });
    }
</script>
		</div>
	</li>
<li class="wc_payment_method payment_method_woo-mercado-pago-custom">
	<input id="payment_method_woo-mercado-pago-custom" type="radio" class="input-radio" name="payment_method" value="woo-mercado-pago-custom"  data-order_button_text="" />

	<label for="payment_method_woo-mercado-pago-custom">
		Cartão de crédito <img decoding="async" src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/icons/icon-custom.png?ver=8.2.0" alt="Cartão de crédito" />	</label>
			<div class="payment_box payment_method_woo-mercado-pago-custom" style="display:none;">
			<div class="mp-checkout-custom-load">
    <div class="spinner-card-form"></div>
</div>
<div class='mp-checkout-container'>
            <div class='mp-checkout-custom-container'>
            
            
            <div id="mp-custom-checkout-form-container">
                <div class='mp-checkout-custom-available-payments'>
                    <div class='mp-checkout-custom-available-payments-header'>
                        <div class="mp-checkout-custom-available-payments-title">
                            <img decoding="async" src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/icons/icon-blue-card.png?ver=8.2.0" class='mp-icon'>
                            <p class="mp-checkout-custom-available-payments-text">
                                Quais cartões você pode usar?                            </p>
                        </div>

                        <img decoding="async"
                            src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/custom/chevron-down.png?ver=8.2.0"
                            class='mp-checkout-custom-available-payments-collapsible'
                        />
                    </div>

                    <div class='mp-checkout-custom-available-payments-content'>
                        <payment-methods methods='[{&quot;title&quot;:&quot;Cart\u00f5es de cr\u00e9dito&quot;,&quot;label&quot;:&quot;At\u00e9 12x&quot;,&quot;payment_methods&quot;:[{&quot;src&quot;:&quot;https:\/\/http2.mlstatic.com\/storage\/logos-api-admin\/b4785730-c13f-11ee-b4b3-bb9a23b70639-xl.svg&quot;,&quot;alt&quot;:&quot;American Express&quot;},{&quot;src&quot;:&quot;https:\/\/http2.mlstatic.com\/storage\/logos-api-admin\/0daa1670-5c81-11ec-ae75-df2bef173be2-xl.png&quot;,&quot;alt&quot;:&quot;Mastercard&quot;},{&quot;src&quot;:&quot;https:\/\/http2.mlstatic.com\/storage\/logos-api-admin\/d589be70-eb86-11e9-b9a8-097ac027487d-xl.png&quot;,&quot;alt&quot;:&quot;Visa&quot;},{&quot;src&quot;:&quot;https:\/\/http2.mlstatic.com\/storage\/logos-api-admin\/fcdc39d0-57ad-11e8-8359-5d73691de80c-xl.svg&quot;,&quot;alt&quot;:&quot;Hipercard&quot;},{&quot;src&quot;:&quot;https:\/\/www.mercadopago.com\/org-img\/MP3\/API\/logos\/elo.gif&quot;,&quot;alt&quot;:&quot;Elo&quot;}]},{&quot;title&quot;:&quot;Cart\u00f5es de d\u00e9bito&quot;,&quot;payment_methods&quot;:[{&quot;src&quot;:&quot;https:\/\/www.mercadopago.com\/org-img\/MP3\/API\/logos\/elo.gif&quot;,&quot;alt&quot;:&quot;Elo Debito&quot;}]}]'></payment-methods>

                                                <hr>
                    </div>
                </div>

                <div class='mp-checkout-custom-card-form'>
                    <p class='mp-checkout-custom-card-form-title'>
                        Preencha os dados do seu cartão                    </p>

                    <div class='mp-checkout-custom-card-row'>
                        <input-label
                            isOptinal=false
                            message="Número do cartão"
                            for='mp-card-number'
                        >
                        </input-label>

                        <div class="mp-checkout-custom-card-input" id="form-checkout__cardNumber-container"></div>

                        <input-helper
                            isVisible=false
                            message="Dado obrigatório"
                            input-id="mp-card-number-helper"
                        >
                        </input-helper>
                    </div>

                    <div class='mp-checkout-custom-card-row' id="mp-card-holder-div">
                        <input-label
                            message="Nome do titular como aparece no cartão"
                            isOptinal=false
                        >
                        </input-label>

                        <input
                            class="mp-checkout-custom-card-input mp-card-holder-name"
                            placeholder="Ex.: María López"
                            id="form-checkout__cardholderName"
                            name="mp-card-holder-name"
                            data-checkout="cardholderName"
                        />

                        <input-helper
                            isVisible=false
                            message="Dado obrigatório"
                            input-id="mp-card-holder-name-helper"
                            data-main="mp-card-holder-name"
                        >
                        </input-helper>
                    </div>

                    <div class='mp-checkout-custom-card-row mp-checkout-custom-dual-column-row'>
                        <div class='mp-checkout-custom-card-column'>
                            <input-label
                                message="Vencimento"
                                isOptinal=false
                            >
                            </input-label>

                            <div
                                id="form-checkout__expirationDate-container"
                                class="mp-checkout-custom-card-input mp-checkout-custom-left-card-input"
                            >
                            </div>

                            <input-helper
                                isVisible=false
                                message="Dado obrigatório"
                                input-id="mp-expiration-date-helper"
                            >
                            </input-helper>
                        </div>

                        <div class='mp-checkout-custom-card-column'>
                            <input-label
                                message="Código de segurança"
                                isOptinal=false
                            >
                            </input-label>

                            <div id="form-checkout__securityCode-container" class="mp-checkout-custom-card-input"></div>

                            <p id="mp-security-code-info" class="mp-checkout-custom-info-text"></p>

                            <input-helper
                                isVisible=false
                                message="Dado obrigatório"
                                input-id="mp-security-code-helper"
                            >
                            </input-helper>
                        </div>
                    </div>

                    <div id="mp-doc-div" class="mp-checkout-custom-input-document" style="display: none;">
                        <input-document
                            label-message="Documento do titular"
                            helper-invalid="Insira o documento completo."
                            helper-empty="Preencha este campo."
                            helper-wrong="Insira um documento válido."
                            input-name="identificationNumber"
                            hidden-id="form-checkout__identificationNumber"
                            input-data-checkout="doc_number"
                            select-id="form-checkout__identificationType"
                            select-name="identificationType"
                            select-data-checkout="doc_type"
                            flag-error="docNumberError"
                        >
                        </input-document>
                    </div>
                </div>

                <div id="mp-checkout-custom-installments" class="mp-checkout-custom-installments-display-none">
                    <p class='mp-checkout-custom-card-form-title'>
                        Escolha o número de parcelas                    </p>

                    <div id="mp-checkout-custom-issuers-container" class="mp-checkout-custom-issuers-container">
                        <div class='mp-checkout-custom-card-row'>
                            <input-label
                                isOptinal=false
                                message="Banco emissor"
                                for='mp-issuer'
                            >
                            </input-label>
                        </div>

                        <div class="mp-input-select-input">
                            <select name="issuer" id="form-checkout__issuer" class="mp-input-select-select"></select>
                        </div>
                    </div>

                    <div id="mp-checkout-custom-installments-container" class="mp-checkout-custom-installments-container"></div>

                    <input-helper
                        isVisible=false
                        message="Escolha o número de parcelas"
                        input-id="mp-installments-helper"
                    >
                    </input-helper>

                    <select
                        style="display: none;"
                        data-checkout="installments"
                        name="installments"
                        id="form-checkout__installments"
                        class="mp-input-select-select"
                    >
                    </select>

                    <div id="mp-checkout-custom-box-input-tax-cft">
                        <div id="mp-checkout-custom-box-input-tax-tea">
                            <div id="mp-checkout-custom-tax-tea-text"></div>
                        </div>
                        <div id="mp-checkout-custom-tax-cft-text"></div>
                    </div>
                </div>

                <div class="mp-checkout-custom-terms-and-conditions">
                    <terms-and-conditions
                        description="Ao continuar, você concorda com nossos"
                        link-text="Termos e condições"
                        link-src="https://www.mercadopago.com.br/ajuda/termos-e-politicas_194"
                    >
                    </terms-and-conditions>
                </div>
            </div>


        </div>
    
</div>

<div id="mercadopago-utilities" style="display:none;">
    <input type="hidden" id="mp-amount" value='39' name="mercadopago_custom[amount]"/>
    <input type="hidden" id="currency_ratio" value='1' name="mercadopago_custom[currency_ratio]"/>
    <input type="hidden" id="paymentMethodId" name="mercadopago_custom[payment_method_id]"/>
    <input type="hidden" id="mp_checkout_type" name="mercadopago_custom[checkout_type]" value="custom"/>
    <input type="hidden" id="cardExpirationMonth" data-checkout="cardExpirationMonth"/>
    <input type="hidden" id="cardExpirationYear" data-checkout="cardExpirationYear"/>
    <input type="hidden" id="cardTokenId" name="mercadopago_custom[token]"/>
    <input type="hidden" id="cardInstallments" name="mercadopago_custom[installments]"/>
    <input type="hidden" id="mpCardSessionId" name="mercadopago_custom[session_id]" />
    <input type="hidden" id="payerDocNumber" name="mercadopago_custom[doc_number]" />
    <input type="hidden" id="payerDocType" name="mercadopago_custom[doc_type]" />
</div>

<script type="text/javascript">
    function submitWalletButton(event) {
        event.preventDefault();
        jQuery('#mp_checkout_type').val('wallet_button');
        jQuery('form.checkout, form#order_review').submit();
    }

    var availablePayment = document.getElementsByClassName('mp-checkout-custom-available-payments')[0];
    var collapsible = availablePayment.getElementsByClassName('mp-checkout-custom-available-payments-header')[0];

    collapsible.addEventListener("click", function() {
        const icon = collapsible.getElementsByClassName('mp-checkout-custom-available-payments-collapsible')[0];
        const content = availablePayment.getElementsByClassName('mp-checkout-custom-available-payments-content')[0];

        if (content.style.maxHeight) {
            content.style.maxHeight = null;
            content.style.padding = "0px";
            icon.src = "https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/custom/chevron-down.png?ver=8.2.0";
        } else {
            let hg = content.scrollHeight + 15 + "px";
            content.style.setProperty("max-height", hg, "important");
            content.style.setProperty("padding", "24px 0px 0px", "important");
            icon.src = "https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/custom/chevron-up.png?ver=8.2.0";
        }
    });
</script>

		</div>
	</li>
		</ul>
		<div class="form-row place-order">
		<noscript>
			Seu navegador não suporta JavaScript ou ele está desativado. Certifique-se de clicar no botão <em>Atualizar totais</em> antes de finalizar o seu pedido. Você poderá ser cobrado mais do que a quantidade indicada acima, se não fizer isso.			<br/><button type="submit" class="button alt" name="woocommerce_checkout_update_totals" value="Atualizar">Atualizar</button>
		</noscript>

			<div class="woocommerce-terms-and-conditions-wrapper">
		<div class="woocommerce-privacy-policy-text"><p>Os seus dados pessoais serão utilizados para processar a sua compra, apoiar a sua experiência em todo este site e para outros fins descritos na nossa <a href="" class="woocommerce-privacy-policy-link" target="_blank">política de privacidade</a>.</p>
</div>
			</div>
	
		<div class='wcf-bump-order-grid-wrap wcf-all-bump-order-wrap wcf-after-payment' data-update-time='1752310756'></div>
		<button type="submit" class="button alt" name="woocommerce_checkout_place_order" id="place_order" value="Finalizar compra&nbsp;&nbsp;&#082;&#036;&nbsp;39,00" data-value="Finalizar compra&nbsp;&nbsp;&#082;&#036;&nbsp;39,00">Finalizar compra&nbsp;&nbsp;&#082;&#036;&nbsp;39,00</button>
		
		<input type="hidden" id="woocommerce-process-checkout-nonce" name="woocommerce-process-checkout-nonce" value="7ae0e70ffb" /><input type="hidden" name="_wp_http_referer" value="/step/elemenia/" />	</div>
</div>
		</div>

		<input type="hidden" name="_wcf_bump_products" value="">
	</div>
</form>

</div>
<!-- END CHECKOUT SHORTCODE -->
</div>
		</div>
						</div>
				</div>
				<div class="elementor-element elementor-element-88742ee elementor-position-left elementor-mobile-position-left elementor-view-default elementor-vertical-align-top elementor-widget elementor-widget-icon-box" data-id="88742ee" data-element_type="widget" data-widget_type="icon-box.default">
				<div class="elementor-widget-container">
							<div class="elementor-icon-box-wrapper">

						<div class="elementor-icon-box-icon">
				<span  class="elementor-icon">
				<svg xmlns="http://www.w3.org/2000/svg" id="Layer_2" height="512" viewBox="0 0 512 512" width="512" data-name="Layer 2"><path d="m429.42 144.33-169.78-66.4a10 10 0 0 0 -7.28 0l-169.78 66.4a10 10 0 0 0 -6.31 8.27c-3.62 35.4-12.18 215.11 176.43 281.75a10 10 0 0 0 6.6 0c188.61-66.64 180.05-246.35 176.43-281.75a10 10 0 0 0 -6.31-8.27zm-88.82 73.67-99.73 94.25a12.49 12.49 0 0 1 -17.43-.25l-52.3-52.3a12.5 12.5 0 0 1 17.68-17.7l43.71 43.7 90.9-85.9a12.5 12.5 0 1 1 17.17 18.2z"></path><path d="m255.98 76.5.02.01.02-.01z"></path><path d=""></path></svg>				</span>
			</div>
			
						<div class="elementor-icon-box-content">

									<h3 class="elementor-icon-box-title">
						<span  >
							COMPRA SEGURA						</span>
					</h3>
				
				
			</div>
			
		</div>
						</div>
				</div>
				<div class="elementor-element elementor-element-2d0af61 elementor-widget elementor-widget-text-editor" data-id="2d0af61" data-element_type="widget" data-widget_type="text-editor.default">
				<div class="elementor-widget-container">
									<p><span class="mt-3 text-center text-sm text-slate-400 "><strong>Mercado Pago</strong> está processando este pagamento para o vendedor <a class="transititext-primary text-primary hover:text-primary-600 focus:text-primary-600 active:text-primary-700 dark:text-primary-400 dark:hover:text-primary-500 dark:focus:text-primary-500 dark:active:text-primary-600 transition duration-150 ease-in-out" title="Enviar e-<NAME_EMAIL>" href="mailto:<EMAIL>" data-te-toggle="tooltip">Vinicios Rabaioli</a></span> os seus dados pessoais serão utilizados somente para processar a sua compra</p>								</div>
				</div>
				</div>
		<div class="elementor-element elementor-element-44ca567 e-con-full elementor-hidden-tablet elementor-hidden-mobile e-flex e-con e-child" data-id="44ca567" data-element_type="container">
				<div class="elementor-element elementor-element-c431bde elementor-widget elementor-widget-image" data-id="c431bde" data-element_type="widget" data-widget_type="image.default">
				<div class="elementor-widget-container">
															<img decoding="async" width="748" height="1816" src="https://pay.desyne.pro/wp-content/uploads/2024/08/banner_lateral.webp" class="attachment-full size-full wp-image-992" alt="" srcset="https://pay.desyne.pro/wp-content/uploads/2024/08/banner_lateral.webp 748w, https://pay.desyne.pro/wp-content/uploads/2024/08/banner_lateral-600x1457.webp 600w, https://pay.desyne.pro/wp-content/uploads/2024/08/banner_lateral-124x300.webp 124w, https://pay.desyne.pro/wp-content/uploads/2024/08/banner_lateral-422x1024.webp 422w, https://pay.desyne.pro/wp-content/uploads/2024/08/banner_lateral-633x1536.webp 633w" sizes="(max-width: 748px) 100vw, 748px" />															</div>
				</div>
				</div>
					</div>
				</div>
		<div class="elementor-element elementor-element-c920a2c e-flex e-con-boxed e-con e-parent" data-id="c920a2c" data-element_type="container">
					<div class="e-con-inner">
				<div class="elementor-element elementor-element-aa721a1 elementor-widget elementor-widget-html" data-id="aa721a1" data-element_type="widget" data-widget_type="html.default">
				<div class="elementor-widget-container">
					<script>
document.addEventListener('DOMContentLoaded', function() {

    // --- Start of IIFE ---
    (function() {

        // --- Configuration ---
        const mobileBreakpoint = 768;
        const initDelay = 300;
        const observerDebounceDelay = 200;
        const displayDelay = 10; // Tiny delay (ms) before showing content

        // --- State Variables ---
        let accordionActive = false;
        let originalTableElement = null;
        let currentAccordionWrapper = null;
        let observer = null;
        let debouncedUpdateTotal = null;
        let isObserving = false; // Track observer state explicitly

        // --- Utility Functions ---
        function isMobile() { /* ... */ }
        function debounce(func, wait) { /* ... */ }
        function updateAccordionTotal() { /* ... */ }

        // --- Utility Functions (Keep as before) ---
        function isMobile() {
            return typeof window !== 'undefined' && window.innerWidth <= mobileBreakpoint;
        }
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => { clearTimeout(timeout); func.apply(this, args); };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        function updateAccordionTotal() {
             if (!accordionActive || !currentAccordionWrapper) return;
            const totalElement = document.querySelector('.order-total .woocommerce-Price-amount');
            const headerTotal = currentAccordionWrapper.querySelector('.accordion-header .total-amount');
            if (totalElement && headerTotal) {
                 const newTotalText = totalElement.textContent.trim();
                 if (headerTotal.textContent !== newTotalText) { headerTotal.textContent = newTotalText; }
            }
        }
        // --- End Utility Functions ---


        // --- Core Logic: Initialize Accordion ---
        function initializeAccordion() {
            if (!isMobile() || accordionActive) return;
            const table = document.querySelector('.shop_table:not(.order_details)');
            if (!table || !table.parentNode || table.closest('.accordion-wrapper')) return;

            originalTableElement = table;
            const originalParent = table.parentNode;

            // Create Structure (Header, Content, Wrapper)
            const accordionWrapper = document.createElement('div');
            accordionWrapper.className = 'accordion-wrapper mobile-only';
            currentAccordionWrapper = accordionWrapper;

            const totalElement = document.querySelector('.order-total .woocommerce-Price-amount');
            const totalAmount = totalElement ? totalElement.textContent.trim() : 'R$ 0,00';
            const accordionHeader = document.createElement('div');
            accordionHeader.className = 'accordion-header';
            accordionHeader.innerHTML = `
                <div class="header-content">
                    <div class="header-left">
                       <svg class="cart-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 8px; flex-shrink: 0;"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg><span>Detalhes da compra</span>
                    </div>
                    <div class="header-right">
                        <span class="total-amount">${totalAmount}</span>
                        <svg class="chevron-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-left: 8px; transition: transform 0.3s ease; flex-shrink: 0;"><polyline points="6 9 12 15 18 9"></polyline></svg>
                    </div>
                </div>`; // Keep innerHTML concise

            const accordionContent = document.createElement('div');
            accordionContent.className = 'accordion-content';
            accordionContent.style.display = 'none';

            // Assemble and Replace
            accordionWrapper.appendChild(accordionHeader);
            accordionWrapper.appendChild(accordionContent);
            try {
                originalParent.replaceChild(accordionWrapper, originalTableElement);
            } catch (error) { console.error('Error replacing table:', error); originalTableElement = null; currentAccordionWrapper = null; return; }
            accordionContent.appendChild(originalTableElement);

            // Mark Active & Add Listeners
            accordionActive = true;
            accordionContent.addEventListener('click', function(e) { e.stopPropagation(); });

            // --- MODIFIED CLICK HANDLER ---
            accordionHeader.addEventListener('click', function(e) {
                e.preventDefault(); e.stopPropagation();
                if (!currentAccordionWrapper) return;

                const chevron = accordionHeader.querySelector('.chevron-icon');
                const isActive = currentAccordionWrapper.classList.contains('active'); // Check current state BEFORE toggling

                if (!isActive) {
                    // --- OPENING ACCORDION ---
                    currentAccordionWrapper.classList.add('active');
                    if (chevron) { chevron.style.transform = 'rotate(180deg)'; }

                    // 1. Disconnect observer BEFORE showing content
                    stopObservingTotal();

                    // 2. Show content after a tiny delay
                    setTimeout(() => {
                        accordionContent.style.display = 'block';
                        // 3. Reconnect observer AFTER content is potentially rendered
                        //    Add another small delay if needed
                        setTimeout(startObservingTotal, 50); // Give time for display block to apply
                    }, displayDelay); // Tiny delay before display:block

                } else {
                    // --- CLOSING ACCORDION ---
                    currentAccordionWrapper.classList.remove('active');
                    if (chevron) { chevron.style.transform = 'rotate(0deg)'; }
                    accordionContent.style.display = 'none';
                    // Optional: Stop observer when closed if updates aren't needed then
                    // stopObservingTotal();
                }
            });

            // Create debounced function instance
            debouncedUpdateTotal = debounce(updateAccordionTotal, observerDebounceDelay);

            // Start observing initially (if needed, or wait until first open)
            startObservingTotal();
        }

        // --- Core Logic: Remove Accordion ---
        // (Keep removeAccordion as before - Ensure stopObservingTotal is called)
        function removeAccordion() {
            if (!accordionActive || !currentAccordionWrapper || !originalTableElement) return;
            const accordionContent = currentAccordionWrapper.querySelector('.accordion-content');
            if (!accordionContent || originalTableElement.parentNode !== accordionContent) {
                accordionActive = false; currentAccordionWrapper = null; originalTableElement = null; stopObservingTotal(); return;
            }
            const parentOfWrapper = currentAccordionWrapper.parentNode;
            if (parentOfWrapper) {
                try {
                    stopObservingTotal(); // Stop observer before removing
                    parentOfWrapper.replaceChild(originalTableElement, currentAccordionWrapper);
                    accordionActive = false; currentAccordionWrapper = null; originalTableElement = null;
                } catch (error) {
                    console.error('Error restoring table:', error);
                    accordionActive = false; currentAccordionWrapper = null; originalTableElement = null; stopObservingTotal();
                }
            } else {
                accordionActive = false; currentAccordionWrapper = null; originalTableElement = null; stopObservingTotal();
            }
        }

        // --- Mutation Observer Setup ---
        function startObservingTotal() {
            if (!currentAccordionWrapper || isObserving) return; // Don't start if already observing

            // Ensure previous one is stopped just in case
            stopObservingTotal(true); // Pass true to skip cancellation if debounce not ready

            const targetNode = currentAccordionWrapper.closest('.woocommerce-checkout-review-order-table') || currentAccordionWrapper.closest('form.checkout') || document.body;
            if (!targetNode) { console.warn("Observer: Could not find target node."); return; }

            if (!debouncedUpdateTotal) { // Ensure debounced function exists
                debouncedUpdateTotal = debounce(updateAccordionTotal, observerDebounceDelay);
            }

            observer = new MutationObserver(function(mutations) {
                debouncedUpdateTotal(); // Call the lightweight debounced function
            });

            const config = { childList: true, subtree: true, characterData: true };
            try {
                observer.observe(targetNode, config);
                isObserving = true; // Mark as observing
                // console.log('Observer started on:', targetNode); // DEBUG
            } catch (error) {
                console.error("Error starting Observer:", error);
                observer = null; isObserving = false;
            }
        }

        function stopObservingTotal(skipDebounceCancel = false) {
            if (observer) {
                try { observer.disconnect(); } catch (e) {}
                observer = null;
            }
            isObserving = false; // Mark as not observing

            // Cancel pending debounced calls unless skipped
            if (!skipDebounceCancel && debouncedUpdateTotal && typeof debouncedUpdateTotal.cancel === 'function') {
                 // Assuming your debounce implementation has a .cancel() method
                 // debouncedUpdateTotal.cancel();
            }
            // console.log("Observer stopped."); // DEBUG
        }


        // --- Event Handlers & Initial Setup ---
        const handleResize = debounce(function() { /* ... */ }, 250);
        // (Keep Initialization logic as before: CartFlows removal, setTimeout for init, resize listener)
        if (isMobile()) {
            const cf = document.querySelector('.wcf-collapsed-order-review-section'); if(cf){try{cf.remove();}catch(e){}}
            setTimeout(() => { if (isMobile() && !accordionActive) { initializeAccordion(); } }, initDelay);
        }
        if(typeof window !== 'undefined') { window.addEventListener('resize', handleResize); }


    // --- End of IIFE ---
    })();

});
</script>

<!-- Include the previous CSS block here -->
<style>
/* Paste the complete CSS from the previous working version here */
@media (max-width: 768px) { .accordion-wrapper.mobile-only { display: block; margin-bottom: 1em; border-radius: 4px; } }
@media (max-width: 768px) { .accordion-header {   cursor: pointer;  transparent; transition: border-color 0.3s ease; } .accordion-wrapper.active .accordion-header { border-bottom-color: #e0e0e0; } .accordion-header .header-content { display: flex; justify-content: space-between; align-items: center; width: 100%; } }
.accordion-header .header-left, .accordion-header .header-right { display: flex; align-items: center; }
.accordion-header .total-amount { font-weight: bold; margin-right: 8px; }
.accordion-content {  
.accordion-wrapper:not(.active) .accordion-content { border-top: none; padding: 0 15px; }
.accordion-header .chevron-icon { transition: transform 0.3s ease; }
.accordion-wrapper.active .accordion-header .chevron-icon { transform: rotate(180deg); }
@media (min-width: 769px) { .accordion-wrapper.mobile-only { display: none !important; } .shop_table:not(.order_details) { display: table !important; } }

@media only screen and (max-width: 768px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table {
     
        padding: 15px 0 0 0 !important;
      border-radius: 8px !important;
    }
}

@media only screen and (max-width: 768px) {
.header-right {
    display: flex !important;

    gap: 0px !important;

}
}

</style>



<script>
document.addEventListener('DOMContentLoaded', function() {
    // Encontra o botão
    const button = document.querySelector('#place_order');
    
    if (button) {
        // Pega o texto atual e o preço
        const currentText = button.textContent;
        const priceMatch = currentText.match(/R\$\s*[\d,\.]+/);
        const price = priceMatch ? priceMatch[0] : '';

        // Define o novo HTML do botão
        button.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13 7H3C2.44772 7 2 7.44772 2 8V14C2 14.5523 2.44772 15 3 15H13C13.5523 15 14 14.5523 14 14V8C14 7.44772 13.5523 7 13 7Z" stroke="currentColor" stroke-width="1.5"/>
                    <path d="M11.5 7V5C11.5 3.067 9.933 1.5 8 1.5C6.067 1.5 4.5 3.067 4.5 5V7" stroke="currentColor" stroke-width="1.5"/>
                </svg>
                Finalizar compra ${price}
            </div>
        `;

        // Adiciona estilos diretamente ao botão
        button.style.cssText += `
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
            font-size: 15px !important;
            gap: 8px !important;
            padding: 16px 24px !important;
            width: 100% !important;
            border-radius: 8px !important;
        `;
    }
});
</script>

				</div>
				</div>
				<div class="elementor-element elementor-element-15871d9 elementor-widget elementor-widget-html" data-id="15871d9" data-element_type="widget" data-widget_type="html.default">
				<div class="elementor-widget-container">
					<style>

.accordion-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    padding: 16px;
}


table.shop_table.woocommerce-checkout-review-order-table.cartflows_table {
    background: #f9fafb;
    border-radius: 8px;
}

.wcf-product-thumbnail img {
    width: 54px !important;
    height: 54px !important;
 
}
@media screen and (min-width: 769px) {
   .wcf-product-name {
    margin-left: -28px !important;
}
}

    .wcf-product-thumbnail {
        flex-shrink: 0;
    }

    .wcf-product-name {
        margin-left: 0;
        padding-left: 0;
    }

    td.product-name {
        white-space: normal;
    }

    /* Remove espaços extras que podem estar vindo do &nbsp; */
    td.product-name .wcf-product-image + strong.product-quantity {
        margin-left: 10px;
    }
}
.accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 8px 0;
    width: 100%;
}

.header-content {
    display: flex;
    align-items: center;
    width: 100%;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: auto;
}

.cart-icon {
    stroke: currentColor;
}

.chevron-icon {
    transition: transform 0.3s ease;
    margin-left: 12px;
}

.accordion-wrapper.active .chevron-icon {
    transform: rotate(180deg);
}

.accordion-content {
    display: none;
    padding-top: 16px;
}

.accordion-wrapper.active .accordion-content {
    display: block;
}

.total-amount {
    font-weight: 600;
}

.shop_table {
    width: 100%;
    border-collapse: collapse;
}

.cartflows_table thead {
    background-color: #f8f9fa;
}

.cartflows_table th,
.cartflows_table td {
    padding: 12px;
    text-align: left;
}

.wcf-product-image {
    display: flex;
    align-items: center;
    gap: 12px;
}

.wcf-product-thumbnail img {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 4px;
}

.accordion-wrapper {
    border-radius: 0px 0px 8px 8px !important
    ;
}
</style>

				</div>
				</div>
				<div class="elementor-element elementor-element-7499a46 elementor-widget elementor-widget-html" data-id="7499a46" data-element_type="widget" data-widget_type="html.default">
				<div class="elementor-widget-container">
					<style>
.phone-input-wrapper {
    position: relative;
    display: flex; /* Mantém flex para alinhar itens se necessário, mas o prefixo é absoluto */
    align-items: center; /* Ajuda a alinhar o input em si se houver outros elementos */
    width: 100%;
}

.flag-prefix {
    position: absolute;
    left: 12px;
    /* AQUI ESTÁ A MUDANÇA PRINCIPAL */
    top: 50%;               /* Posiciona o topo do prefixo no meio vertical do wrapper */
    transform: translateY(-50%); /* Move o prefixo para cima pela metade de sua própria altura */
    /* FIM DA MUDANÇA PRINCIPAL */
    display: flex;
    align-items: center;
    gap: 4px;
    pointer-events: none; /* Para poder clicar "através" do ícone no input */
    z-index: 1; /* Garante que fique acima do fundo do input */
}

.flag-prefix img {
    width: 18px; /* Ajuste o tamanho se necessário */
    height: auto;
    vertical-align: middle; /* Ajuda, mas flex align-items é mais robusto */
    display: inline-block; /* Necessário para vertical-align funcionar se não for flex */
}

.flag-prefix span {
    color: #666;
    font-size: 14px; /* Ajuste o tamanho da fonte se necessário */
}

#billing_cellphone {
    width: 100%;
    border-radius: 4px;
    line-height: 1.5; /* Linha padrão */
    box-sizing: border-box; /* Importante para padding/border não aumentarem o tamanho total */
    padding-left: 65px !important; /* Espaço para a bandeira e +55. Mantenha o !important se for necessário para sobrescrever outros estilos */
    /* padding-top: 22.2px; */ /* Remova ou ajuste este padding-top se ele for o causador do movimento do label e desalinhamento. Veja a nota abaixo. */
    padding-top: 10px; /* Valor de exemplo - ajuste conforme necessário */
    padding-bottom: 10px; /* Adicione padding-bottom para equilíbrio */
    height: 52px; /* Mantenha a altura desejada */
    vertical-align: middle; /* Tenta alinhar o texto verticalmente */
}


/* Estilo opcional para quando o campo está focado */
#billing_cellphone:focus {
    outline: none;
    border-color: #666; /* Ou a cor de foco desejada */
}

/* Estilo opcional para o placeholder */
#billing_cellphone::placeholder {
  color: #999; /* Cor mais clara para o placeholder */
  /* O navegador tentará centralizar verticalmente o placeholder por padrão */
}

</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const phoneInput = document.getElementById('billing_cellphone');
    if (!phoneInput) return;

    // Wrap phone input in a container
    const wrapper = document.createElement('div');
    wrapper.className = 'phone-input-wrapper';
    phoneInput.parentNode.insertBefore(wrapper, phoneInput);
    wrapper.appendChild(phoneInput);

    // Add flag image and prefix
    const flagPrefix = document.createElement('div');
    flagPrefix.className = 'flag-prefix';
    flagPrefix.innerHTML = `
        <img decoding="async" src="https://upload.wikimedia.org/wikipedia/en/thumb/0/05/Flag_of_Brazil.svg/1024px-Flag_of_Brazil.svg.png">
        <span>+55</span>
    `;
    wrapper.insertBefore(flagPrefix, phoneInput);

    // Phone number formatting
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 11) value = value.slice(0, 11);
        
        if (value.length > 0) {
            value = '(' + value;
            if (value.length > 3) {
                value = value.slice(0, 3) + ') ' + value.slice(3);
            }
            if (value.length > 10) {
                value = value.slice(0, 10) + '-' + value.slice(10);
            }
        }
        
        e.target.value = value;
    });
});
</script>				</div>
				</div>
				<div class="elementor-element elementor-element-af0af4b elementor-widget elementor-widget-html" data-id="af0af4b" data-element_type="widget" data-widget_type="html.default">
				<div class="elementor-widget-container">
					<style>
.secure-payment-notice {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 16px 0 !important;
    width: 100% !important;
    justify-content: center !important;
}
@media (max-width: 480px) {
.secure-payment-notice {
    display: flex !important;
    align-items: center !important;
    gap: 0px !important;
   
}
}
.secure-payment-notice svg {
    width: 16px !important;
    height: 16px !important;
    color: #3BAE7E !important;
    flex-shrink: 0 !important;
}

.secure-payment-notice span {
     color: #3BAE7E !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    text-align: center !important;
}

.mp-checkout-custom-terms-and-conditions,
.mp-checkout-pix-terms-and-conditions {
    display: none !important;
}
</style>

<script>
(function() {
    const secureNoticeHTML = `
        <div class="secure-payment-notice">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12.6667 7.33333H3.33333C2.59695 7.33333 2 7.93028 2 8.66666V13.3333C2 14.0697 2.59695 14.6667 3.33333 14.6667H12.6667C13.403 14.6667 14 14.0697 14 13.3333V8.66666C14 7.93028 13.403 7.33333 12.6667 7.33333Z" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.66667 7.33333V4.66666C4.66667 3.78261 5.01786 2.93476 5.64298 2.30964C6.2681 1.68452 7.11595 1.33333 8 1.33333C8.88406 1.33333 9.73191 1.68452 10.357 2.30964C10.9821 2.93476 11.3333 3.78261 11.3333 4.66666V7.33333" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 10.6667V11.3333" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span>Os seus dados de pagamento são criptografados e processados de forma segura.</span>
        </div>
    `;

    // Função para inserir a notificação apenas na aba do cartão
    function insertNotice() {
        // Remover qualquer notificação existente
        document.querySelectorAll('.secure-payment-notice').forEach(el => el.remove());
        
        // Verificar se a aba do cartão está ativa
        const cartaoRadio = document.getElementById('payment_method_woo-mercado-pago-custom');
        
        if (cartaoRadio && cartaoRadio.checked) {
            // Inserir no container de parcelas do cartão
            const installmentsContainer = document.getElementById('mp-checkout-custom-installments');
            if (installmentsContainer && !installmentsContainer.querySelector('.secure-payment-notice')) {
                installmentsContainer.insertAdjacentHTML('beforeend', secureNoticeHTML);
            }
            
            // Backup: se não encontrar o container de parcelas, inserir na caixa de pagamento do cartão
            if (!installmentsContainer) {
                const cartaoBox = document.querySelector('.payment_method_woo-mercado-pago-custom .payment_box');
                if (cartaoBox && !cartaoBox.querySelector('.secure-payment-notice')) {
                    cartaoBox.insertAdjacentHTML('beforeend', secureNoticeHTML);
                }
            }
        }
        
        // Sempre esconder os termos
        document.querySelectorAll('.mp-checkout-custom-terms-and-conditions, .mp-checkout-pix-terms-and-conditions')
            .forEach(el => { el.style.display = 'none'; });
    }

    // Função que será chamada quando os métodos de pagamento forem alterados
    function handlePaymentMethodChange() {
        setTimeout(insertNotice, 300);
    }

    // Executar inicialmente
    setTimeout(insertNotice, 500);
    
    // Adicionar event listeners aos radios dos métodos de pagamento
    document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
        radio.addEventListener('change', handlePaymentMethodChange);
    });
    
    // Adicionar suporte para eventos do WooCommerce
    if (typeof jQuery !== 'undefined') {
        jQuery(document.body).on('payment_method_selected updated_checkout', function() {
            setTimeout(insertNotice, 300);
        });
    }
    
    // Verificar ocasionalmente
    const checkInterval = setInterval(insertNotice, 2000);
    
    // Parar de verificar após 30 segundos para economizar recursos
    setTimeout(() => {
        clearInterval(checkInterval);
    }, 30000);
})();
</script>				</div>
				</div>
				<div class="elementor-element elementor-element-cdb350f elementor-widget elementor-widget-html" data-id="cdb350f" data-element_type="widget" data-widget_type="html.default">
				<div class="elementor-widget-container">
					<style>
/* Reset e container principal */
.wc_payment_methods {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    gap: 20px !important;
    list-style: none !important;
}
#payment_options_heading {
    display: flex;
    align-items: center;
}

#payment_options_heading::before {
    content: '';
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M1 4a1 1 0 011-1h16a1 1 0 011 1v8a1 1 0 01-1 1H2a1 1 0 01-1-1V4zm12 4a3 3 0 11-6 0 3 3 0 016 0zM4 9a1 1 0 100-2 1 1 0 000 2zm13-1a1 1 0 11-2 0 1 1 0 012 0zM1.75 14.5a.75.75 0 000 1.5c4.417 0 8.693.603 12.749 1.73 1.111.309 2.251-.512 2.251-1.696v-.784a.75.75 0 00-1.5 0v.784a.272.272 0 01-.35.25A49.043 49.043 0 001.75 14.5z' clip-rule='evenodd'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}
/* Container dos botões */
.payment-buttons-container {
    display: flex !important;
    gap: 16px !important;
    width: 100% !important;
}

/* Cada botão de pagamento */
.wc_payment_method {
    flex: 1 !important;
    list-style: none !important;
    position: relative !important;
}

/* Labels dos botões */
.wc_payment_method > label {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    padding: 16px !important;
    background: white !important;
    border: 1px solid #E5E7EB !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    width: 100% !important;
    font-weight: 500 !important;
}

/* Ícones dos botões */
.wc_payment_method > label img {
    height: 24px !important;
    width: auto !important;
    margin-left: 8px !important;
}

/* Estilo específico para o ícone do Pix */
.wc_payment_method.payment_method_woo-mercado-pago-pix > label img {
    content: url('https://pay.desyne.pro/wp-content/uploads/2025/01/pix.svg') !important;
    height: 24px !important;
    width: 24px !important;
}

/* Estilo específico para o ícone do Cartão */
.wc_payment_method.payment_method_woo-mercado-pago-custom > label img {
    content: url('https://pay.desyne.pro/wp-content/uploads/2025/01/fff.svg') !important;
    height: 24px !important;
    width: 24px !important;
}

/* Radio buttons */
.wc_payment_method > input[type="radio"] {
    display: none !important;
}

/* Estado ativo */
.wc_payment_method > input[type="radio"]:checked + label {
    background: #f9fafb !important;
}

/* Ajuste específico para o ícone do Pix quando selecionado */
.wc_payment_method.payment_method_woo-mercado-pago-pix > input[type="radio"]:checked + label img {
    filter: brightness(0) !important;
}

/* Remove o filtro de inversão para o ícone do cartão */
.wc_payment_method.payment_method_woo-mercado-pago-custom > input[type="radio"]:checked + label img {
    filter: none !important;
}

/* Container do conteúdo */
.payment_box {
    display: none !important;
    width: 100% !important;
    padding: 24px !important;
    background: white !important;
    border: 1px solid #E5E7EB !important;
    border-radius: 8px !important;
    margin-top: 16px !important;
}

/* Mostrar conteúdo quando selecionado */
.wc_payment_method > input[type="radio"]:checked ~ .payment_box {
    display: block !important;
}

/* Força largura total em elementos do conteúdo */
.payment_box,
.mp-checkout-container,
.mp-checkout-custom-container,
.mp-checkout-pix-container,
.mp-checkout-custom-card-form,
.mp-checkout-custom-card-row,
.mp-checkout-custom-available-payments,
.mp-checkout-custom-card-column,
.mp-input-select-input,
.mp-checkout-custom-issuers-container,
.mp-checkout-custom-installments-container,
#mp-custom-checkout-form-container,
.mp-checkout-custom-available-payments-content,
.mp-checkout-custom-card-form,
.mp-checkout-custom-installments,
.mp-checkout-pix-container,
.mp-checkout-container {
    width: 100% !important;
    max-width: none !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    box-sizing: border-box !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Função para inicializar os métodos de pagamento
    function initializePaymentMethods() {
        const methods = document.querySelectorAll('.wc_payment_method');
        
        methods.forEach(method => {
            const radio = method.querySelector('input[type="radio"]');
            const paymentBox = method.querySelector('.payment_box');
            
            if (radio) {
                radio.addEventListener('change', function() {
                    // Esconder todos os payment boxes
                    document.querySelectorAll('.payment_box').forEach(box => {
                        box.style.display = 'none';
                    });
                    
                    // Mostrar o payment box selecionado
                    if (paymentBox) {
                        paymentBox.style.display = 'block';
                    }
                });
            }
        });

        // Selecionar o primeiro método por padrão
        const firstRadio = document.querySelector('.wc_payment_method input[type="radio"]');
        if (firstRadio) {
            firstRadio.checked = true;
            firstRadio.dispatchEvent(new Event('change'));
        }
    }

    // Inicializar
    initializePaymentMethods();
});
</script>				</div>
				</div>
				<div class="elementor-element elementor-element-863982f elementor-widget elementor-widget-html" data-id="863982f" data-element_type="widget" data-widget_type="html.default">
				<div class="elementor-widget-container">
					<script>
    
    
    (function() {
    let lastValue = '';
    let isProcessing = false;

    function maskCPFCNPJ(value) {
        value = value.replace(/\D/g, '');
        
        if (value.length <= 11) {
            return value.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, '$1.$2.$3-$4')
                        .replace(/^(\d{3})(\d{3})(\d{3})(\d{1})$/, '$1.$2.$3-$4')
                        .replace(/^(\d{3})(\d{3})(\d{1,3})$/, '$1.$2.$3')
                        .replace(/^(\d{3})(\d{1,3})$/, '$1.$2')
                        .replace(/^(\d{1,3})$/, '$1');
        } else {
            return value.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, '$1.$2.$3/$4-$5')
                        .replace(/^(\d{2})(\d{3})(\d{3})(\d{1,4})$/, '$1.$2.$3/$4')
                        .replace(/^(\d{2})(\d{3})(\d{1,3})$/, '$1.$2.$3')
                        .replace(/^(\d{2})(\d{1,3})$/, '$1.$2')
                        .replace(/^(\d{1,2})$/, '$1');
        }
    }

    function updateMPFields(value, force = false) {
        if (isProcessing && !force) return;
        isProcessing = true;

        try {
            const cleanValue = value.replace(/\D/g, '');
            if (!cleanValue) return;

            lastValue = cleanValue;

            // Atualiza campo oculto
            const hiddenInput = document.getElementById('form-checkout__identificationNumber');
            if (hiddenInput && hiddenInput.value !== cleanValue) {
                hiddenInput.value = cleanValue;
                hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
            }

            // Atualiza campos visíveis se existirem
            const docInput = document.querySelector('.mp-document');
            const docSelect = document.getElementById('form-checkout__identificationType');

            if (docInput && docInput.value !== cleanValue) {
                docInput.value = cleanValue;
                docInput.dispatchEvent(new Event('input', { bubbles: true }));
            }

            const docType = cleanValue.length > 11 ? 'CNPJ' : 'CPF';
            if (docSelect && docSelect.value !== docType) {
                docSelect.value = docType;
                docSelect.dispatchEvent(new Event('change', { bubbles: true }));
            }
        } finally {
            isProcessing = false;
        }
    }

    function initializeDocumentSync() {
        const mainInput = document.getElementById('billing_cpf_cnpj');
        if (!mainInput) return;

        // Sincroniza valor inicial
        if (mainInput.value) {
            updateMPFields(mainInput.value, true);
        }

        // Remove listener anterior se existir
        mainInput.removeEventListener('input', mainInput.mpListener);

        // Adiciona novo listener
        mainInput.mpListener = function(e) {
            const maskedValue = maskCPFCNPJ(e.target.value);
            if (e.target.value !== maskedValue) {
                e.target.value = maskedValue;
            }
            updateMPFields(maskedValue);
        };

        mainInput.addEventListener('input', mainInput.mpListener);
    }

    // Inicialização inicial
    initializeDocumentSync();

    // Observador mais eficiente
    let debounceTimer;
    const observer = new MutationObserver(() => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
            if (lastValue) {
                updateMPFields(lastValue, true);
            }
            initializeDocumentSync();
        }, 500);
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Atualização periódica mais leve
    setInterval(() => {
        if (lastValue && !isProcessing) {
            updateMPFields(lastValue, true);
        }
    }, 2000);
})();
</script>				</div>
				</div>
				<div class="elementor-element elementor-element-c72d281 elementor-widget elementor-widget-html" data-id="c72d281" data-element_type="widget" data-widget_type="html.default">
				<div class="elementor-widget-container">
					<script>
jQuery(document).ready(function($) {
    // Variáveis globais para rastreamento
    var mpTabsInitialized = false;
    
    function setupModernPaymentTabs() {
        console.log("Verificando configuração de abas");
        
        // Função para limpar duplicações na aba do cartão
        function fixDuplicatedLabelsCard() {
            // Textos dos labels que estão duplicados
            const labelsToFix = ['Número do cartão', 'Nome do titular como aparece no cartão', 'Vencimento', 'CVV'];
            
            // Para cada texto duplicado, mantenha apenas o primeiro
            labelsToFix.forEach(function(text) {
                // Encontrar todos os elementos com esse texto exato
                $(".payment_method_woo-mercado-pago-custom").find(":contains('" + text + "')").filter(function() {
                    return $(this).text().trim() === text;
                }).not(':first').remove();
            });
            
            // Remover duplicações na estrutura do DOM para campos específicos
            $('.mp-input-label:contains("Número do cartão")').not(':first').remove();
            $('.mp-input-label:contains("Nome do titular como aparece no cartão")').not(':first').remove();
            $('.mp-input-label:contains("Vencimento")').not(':first').remove();
            $('.mp-input-label:contains("CVV")').not(':first').remove();
        }
        
        // Função para limpar duplicações na aba do PIX
        function fixDuplicatedElementsPix() {
            // Remover containers duplicados do PIX
            $('.payment_method_woo-mercado-pago-pix .mp-pix-template-container').not(':first').remove();
            
            // Remover outros elementos duplicados
            $('.payment_method_woo-mercado-pago-pix .mp-pix-template-title').not(':first').remove();
            $('.payment_method_woo-mercado-pago-pix .mp-pix-template-subtitle').not(':first').remove();
            $('.payment_method_woo-mercado-pago-pix .mp-terms-and-conditions-container').not(':first').remove();
            
            // Se houver qualquer outro elemento duplicado específico do PIX, remova aqui
            $('.payment_method_woo-mercado-pago-pix pix-template').each(function() {
                $(this).children(':gt(0)').remove();
            });
        }
        
        // Chamar as funções para corrigir duplicações
        fixDuplicatedLabelsCard();
        fixDuplicatedElementsPix();
        
        // Verificar se as abas já existem
        if ($('.mp-payment-tabs-container').length && mpTabsInitialized) {
            console.log("Abas já existem, atualizando estado");
            updateTabsState();
            return;
        }
        
        // Remover quaisquer abas duplicadas
        $('.mp-payment-tabs-container').not(':first').remove();
        
        // Adicionar CSS para estilo e comportamento
        if (!$('#mp-tabs-styles').length) {
            $('head').append(`
                <style id="mp-tabs-styles">
                    .mp-payment-tabs-container {
                        display: flex;
                        margin-bottom: 16px !important;
                        background: #f7f7f7;
                        border-radius: 8px;
                        overflow: hidden;
                        padding: 4px;
                        position: relative;
                        z-index: 10;
                    }
                    
                    .mp-payment-tab {
                        flex: 1;
                        padding: 12px;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 8px;
                        transition: all 0.2s ease;
                        font-weight: 500;
                        font-size: 16px;
                        color: #333;
                        border-radius: 6px;
                        margin: 0 3px;
                    }
                    
                    .mp-payment-tab:hover {
                        background: rgba(42, 187, 172, 0.08);
                    }
                    
                    .mp-payment-tab.active {
                        background: white;
                        color: #2abbac;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
                    }
                    
                    .mp-payment-tab img {
                        height: 20px;
                        transition: all 0.2s ease;
                        vertical-align: middle;
                        filter: brightness(0);
                    }
                    
                    .mp-payment-tab.active img {
                        filter: invert(67%) sepia(29%) saturate(1064%) hue-rotate(133deg) brightness(88%) contrast(89%);
                    }
                    
                    /* Wrapper para conteúdo com altura condicionada */
                    .mp-content-wrapper {
                        position: relative;
                        background: #fff;
                        border-radius: 6px;
                        overflow: hidden;
                    }
                    
                    /* Altura mínima para PIX (225px) */
                    .mp-pix-mode .mp-content-wrapper {
                        min-height: 225px;
                    }
                    
                    /* Altura mínima para o cartão */
                    .mp-card-mode .mp-content-wrapper {
                        min-height: 300px;
                    }
                    
                    /* Substitui o spinner pelo efeito shimmer/skeleton do Elementor */
                    .mp-content-loading {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(255,255,255,0.9);
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        z-index: 5;
                        padding: 20px;
                    }
                    
                    /* Skeleton loading effect */
                    .mp-skeleton-item {
                        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                        background-size: 200% 100%;
                        animation: shimmer 1.5s infinite;
                        border-radius: 4px;
                        margin-bottom: 12px;
                    }
                    
                    @keyframes shimmer {
                        0% { background-position: -200% 0; }
                        100% { background-position: 200% 0; }
                    }
                    
                    .mp-skeleton-container {
                        width: 100%;
                        max-width: 500px;
                    }
                    
                    .mp-skeleton-card .mp-skeleton-item:nth-child(1) {
                        height: 30px;
                        width: 70%;
                        margin-bottom: 20px;
                    }
                    
                    .mp-skeleton-card .mp-skeleton-item:nth-child(2),
                    .mp-skeleton-card .mp-skeleton-item:nth-child(3) {
                        height: 40px;
                    }
                    
                    .mp-skeleton-card .mp-skeleton-row {
                        display: flex;
                        gap: 12px;
                        margin-bottom: 12px;
                    }
                    
                    .mp-skeleton-card .mp-skeleton-row .mp-skeleton-item {
                        flex: 1;
                        height: 40px;
                        margin-bottom: 0;
                    }
                    
                    .mp-skeleton-pix .mp-skeleton-item:nth-child(1) {
                        height: 80px;
                        width: 80px;
                        border-radius: 50%;
                        margin: 0 auto 20px;
                    }
                    
                    .mp-skeleton-pix .mp-skeleton-item:nth-child(2) {
                        height: 24px;
                        width: 70%;
                        margin: 0 auto 15px;
                    }
                    
                    .mp-skeleton-pix .mp-skeleton-item:nth-child(3) {
                        height: 16px;
                        width: 90%;
                        margin: 0 auto;
                    }
                    
                    .wc_payment_methods {
                        padding: 0 !important;
                        border: none !important;
                        margin: 0 !important;
                    }
                    
                    /* Ocultar apenas os labels dos radio buttons, não todos os labels */
                    .wc_payment_methods > li > label {
                        display: none !important;
                    }
                    
                    /* Manter todos os outros labels visíveis */
                    .wc_payment_methods .payment_box label,
                    .wc_payment_methods .mp-input-label {
                        display: block !important;
                    }
                    
                    .wc_payment_methods input[type="radio"] {
                        position: absolute;
                        opacity: 0;
                    }
                    
                    .payment_box {
                        margin-top: 0 !important;
                        padding: 16px 0 !important;
                        border: none !important;
                        background: transparent !important;
                    }
                    
                    /* Preservar layout original dos inputs de cartão */
                    .mp-checkout-custom-dual-column-row {
                        display: flex !important;
                        gap: 16px !important;
                    }
                    
                    .mp-checkout-custom-card-column {
                        flex: 1 !important;
                    }
                    
                    /* Garante que o label CVV esteja visível */
                    .mp-input-label[data-cy="input-label"] {
                        display: block !important;
                    }
                    
                    /* Evita duplicação visual do PIX */
                    .mp-pix-template-container:not(:first-of-type) {
                        display: none !important;
                    }
                </style>
            `);
        }
        
        // Criar o container de abas se não existir
        if (!$('.mp-payment-tabs-container').length) {
            var tabsContainer = $('<div class="mp-payment-tabs-container"></div>');
            
            // Criar a aba Cartão
            var cardTab = $('<div class="mp-payment-tab mp-payment-tab-card"></div>');
            cardTab.html('<img decoding="async" src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/icons/icon-blue-card.png" alt="Cartão"> Cartão');
            
            // Criar a aba PIX
            var pixTab = $('<div class="mp-payment-tab mp-payment-tab-pix"></div>');
            pixTab.html('<img decoding="async" src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/icons/icon-pix-blue.png" alt="Pix"> Pix');
            
            // Adicionar as abas ao container
            tabsContainer.append(cardTab).append(pixTab);
            
            // Adicionar o container antes da lista de métodos de pagamento
            $('.wc_payment_methods').before(tabsContainer);
        }
        
        // Envolver os métodos de pagamento em um wrapper se não estiverem
        if (!$('.mp-content-wrapper').length) {
            $('.wc_payment_methods').wrap('<div class="mp-content-wrapper"></div>');
        }
        
        // Criar o esqueleto de carregamento para o conteúdo (substitui o spinner)
        if ($('.mp-content-loading').length) {
            $('.mp-content-loading').remove();
        }
        
        // Criar novos elementos de esqueleto
        var skeletonHTML = `
            <div class="mp-content-loading" style="display:none;">
                <div class="mp-skeleton-container mp-skeleton-card" style="display:none;">
                    <div class="mp-skeleton-item"></div>
                    <div class="mp-skeleton-item"></div>
                    <div class="mp-skeleton-item"></div>
                    <div class="mp-skeleton-row">
                        <div class="mp-skeleton-item"></div>
                        <div class="mp-skeleton-item"></div>
                    </div>
                    <div class="mp-skeleton-item"></div>
                </div>
                <div class="mp-skeleton-container mp-skeleton-pix" style="display:none;">
                    <div class="mp-skeleton-item"></div>
                    <div class="mp-skeleton-item"></div>
                    <div class="mp-skeleton-item"></div>
                </div>
            </div>
        `;
        
        $('.mp-content-wrapper').append(skeletonHTML);
        
        // Função para mostrar o esqueleto do tipo apropriado
        function showContentLoading(type) {
            $('.mp-content-loading').fadeIn(100);
            if (type === 'card') {
                $('.mp-skeleton-card').show();
                $('.mp-skeleton-pix').hide();
            } else {
                $('.mp-skeleton-pix').show();
                $('.mp-skeleton-card').hide();
            }
        }
        
        // Função para esconder esqueleto
        function hideContentLoading() {
            $('.mp-content-loading').fadeOut(200);
        }
        
        // Aplicar evento de clique para a aba PIX
        $('.mp-payment-tab-pix').off('click').on('click', function() {
            // Atualizar aparência das abas
            $('.mp-payment-tab').removeClass('active');
            $(this).addClass('active');
            
            // Usar classe de PIX (com altura mínima específica)
            $('body').removeClass('mp-card-mode').addClass('mp-pix-mode');
            
            // Mostrar esqueleto para PIX
            showContentLoading('pix');
            
            // Selecionar o método PIX
            $('#payment_method_woo-mercado-pago-pix').prop('checked', true).get(0).click();
            
            // Verificar carregamento completo do PIX
            checkPixLoaded();
        });
        
        // Aplicar evento de clique para a aba Cartão
        $('.mp-payment-tab-card').off('click').on('click', function() {
            // Atualizar aparência das abas
            $('.mp-payment-tab').removeClass('active');
            $(this).addClass('active');
            
            // Usar classe de cartão
            $('body').removeClass('mp-pix-mode').addClass('mp-card-mode');
            
            // Mostrar esqueleto para cartão
            showContentLoading('card');
            
            // Selecionar o método Cartão
            $('#payment_method_woo-mercado-pago-custom').prop('checked', true).get(0).click();
            
            // Verificar carregamento completo do Cartão
            checkCardLoaded();
        });
        
        // Função para verificar se o PIX carregou completamente
        function checkPixLoaded() {
            if ($('.mp-pix-template-container').length) {
                setTimeout(function() {
                    hideContentLoading();
                    $('.payment_method_woo-mercado-pago-pix').show();
                    $('.payment_method_woo-mercado-pago-custom').hide();
                    
                    // Verificar novamente se há duplicações após o carregamento completo
                    fixDuplicatedElementsPix();
                }, 300);
                return;
            }
            
            setTimeout(checkPixLoaded, 200);
        }
        
        // Função para verificar se o Cartão carregou completamente
        function checkCardLoaded() {
            if ($('#form-checkout__cardNumber-container iframe').length && 
                $('#form-checkout__expirationDate-container iframe').length && 
                $('#form-checkout__securityCode-container iframe').length) {
                
                setTimeout(function() {
                    hideContentLoading();
                    $('.payment_method_woo-mercado-pago-custom').show();
                    $('.payment_method_woo-mercado-pago-pix').hide();
                    
                    // Verificar novamente se há duplicações após o carregamento completo
                    fixDuplicatedLabelsCard();
                }, 700);
                return;
            }
            
            setTimeout(checkCardLoaded, 200);
        }
        
        // Marcar que as abas foram inicializadas
        mpTabsInitialized = true;
        
        // Inicializar estado das abas
        updateTabsState();
    }
    
    // Função para apenas atualizar o estado das abas
    function updateTabsState() {
        if ($('#payment_method_woo-mercado-pago-pix').is(':checked')) {
            $('.mp-payment-tab-pix').addClass('active');
            $('.mp-payment-tab-card').removeClass('active');
            $('.payment_method_woo-mercado-pago-pix').show();
            $('.payment_method_woo-mercado-pago-custom').hide();
            // Adicionar classe de PIX, remover classe de cartão
            $('body').removeClass('mp-card-mode').addClass('mp-pix-mode');
        } else {
            $('.mp-payment-tab-card').addClass('active');
            $('.mp-payment-tab-pix').removeClass('active');
            $('.payment_method_woo-mercado-pago-custom').show();
            $('.payment_method_woo-mercado-pago-pix').hide();
            // Adicionar classe de cartão, remover classe de PIX
            $('body').removeClass('mp-pix-mode').addClass('mp-card-mode');
        }
    }
    
    // Executar quando o documento estiver pronto
    $(document).ready(function() {
        setTimeout(setupModernPaymentTabs, 500);
    });
    
    // Executar quando o checkout for atualizado
    $(document.body).on('updated_checkout', function() {
        // Verificar se precisamos recriar as abas ou apenas atualizar estado
        if (!$('.mp-payment-tabs-container').length) {
            // Abas foram removidas, precisamos recriar
            mpTabsInitialized = false;
        }
        setTimeout(setupModernPaymentTabs, 300);
    });
});
</script>				</div>
				</div>
					</div>
				</div>
				</div>
			</div>

							
	<script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wp-*.php","\/wp-admin\/*","\/wp-content\/uploads\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/hello-elementor\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
<div class="wcf-quick-view-wrapper">
	<div class="wcf-quick-view-bg"><div class="wcf-quick-view-loader"></div></div>
	<div id="wcf-quick-view-modal">
		<div class="wcf-content-main-wrapper"><!--
		--><div class="wcf-content-main">
				<div class="wcf-lightbox-content">
					<div class="wcf-content-main-head">
						<a href="#" id="wcf-quick-view-close" class="wcf-quick-view-close-btn cfa cfa-close"><span class="cartflows-icon-close"></span></a>
					</div>
					<div id="wcf-quick-view-content" class="woocommerce single-product"></div>
				</div>
			</div>
		</div>
	</div>
</div>
			<script>
				const lazyloadRunObserver = () => {
					const lazyloadBackgrounds = document.querySelectorAll( `.e-con.e-parent:not(.e-lazyloaded)` );
					const lazyloadBackgroundObserver = new IntersectionObserver( ( entries ) => {
						entries.forEach( ( entry ) => {
							if ( entry.isIntersecting ) {
								let lazyloadBackground = entry.target;
								if( lazyloadBackground ) {
									lazyloadBackground.classList.add( 'e-lazyloaded' );
								}
								lazyloadBackgroundObserver.unobserve( entry.target );
							}
						});
					}, { rootMargin: '200px 0px 200px 0px' } );
					lazyloadBackgrounds.forEach( ( lazyloadBackground ) => {
						lazyloadBackgroundObserver.observe( lazyloadBackground );
					} );
				};
				const events = [
					'DOMContentLoaded',
					'elementor/lazyload/observe',
				];
				events.forEach( ( event ) => {
					document.addEventListener( event, lazyloadRunObserver );
				} );
			</script>
				<script>
		(function () {
			var c = document.body.className;
			c = c.replace(/woocommerce-no-js/, 'woocommerce-js');
			document.body.className = c;
		})();
	</script>
	<script type="text/template" id="tmpl-variation-template">
	<div class="woocommerce-variation-description">{{{ data.variation.variation_description }}}</div>
	<div class="woocommerce-variation-price">{{{ data.variation.price_html }}}</div>
	<div class="woocommerce-variation-availability">{{{ data.variation.availability_html }}}</div>
</script>
<script type="text/template" id="tmpl-unavailable-variation-template">
	<p role="alert">Desculpe, este produto não está disponível. Escolha uma combinação diferente.</p>
</script>
<link rel='stylesheet' id='wc-blocks-style-css' href='https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/client/blocks/wc-blocks.css?ver=wc-9.9.5' media='all' />
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js" id="jquery-mask-js"></script>
<script id="jquery-mask-js-after">
jQuery(document).ready(function($) {
                    console.log("jQuery is ready.");

                    function setMaxlength() {
                        $("#billing_cpf_cnpj").attr("maxlength", "18");
                    }

                    function applyPhoneMask() {
                        if ($("#billing_cellphone").length) {
                            $("#billing_cellphone").mask("(00) 00000-0000");
                            console.log("Phone mask applied.");
                        } else {
                            console.log("Phone field not found.");
                        }
                    }

                    function applyCpfCnpjMask() {
                        if ($("#billing_cpf_cnpj").length) {
                            $("#billing_cpf_cnpj").attr("inputmode", "numeric").attr("pattern", "[0-9]*"); // Garantir que os atributos sejam aplicados
                            $("#billing_cpf_cnpj").on("input", function() {
                                var element = this;
                                var value = $(this).val().replace(/\D/g, "");
                                if (value.length > 11) {
                                    $(this).unmask().mask("00.000.000/0000-00");
                                    $("#billing_persontype").val("2"); // Pessoa Jurídica
                                    $("input[name='billing_cnpj']").val(value);
                                    $("input[name='billing_cpf']").val("");
                                } else {
                                    $(this).unmask().mask("000.000.000-00");
                                    $("#billing_persontype").val("1"); // Pessoa Física
                                    $("input[name='billing_cpf']").val(value);
                                    $("input[name='billing_cnpj']").val("");
                                }
                                setMaxlength(); // Reaplicar maxlength após a mudança

                                // Ajustar a posição do cursor
                                setTimeout(function() {
                                    element.selectionStart = element.selectionEnd = element.value.length;
                                }, 0);
                            }).trigger("input");
                            setMaxlength(); // Aplicar maxlength inicialmente
                            console.log("CPF/CNPJ mask applied.");
                        } else {
                            console.log("CPF/CNPJ field not found.");
                        }
                    }

                    function autoFillFields(cepData) {
                        if (cepData) {
                            $("input[name='billing_address_1']").val(cepData.logradouro);
                            $("input[name='billing_city']").val(cepData.localidade);
                            $("input[name='billing_state']").val(cepData.uf);
                            $("input[name='billing_neighborhood']").val(cepData.bairro);
                            console.log("Endereço preenchido: " + JSON.stringify(cepData));
                            $("#billing_postcode").css("border-color", "green");
                        } else {
                            $("input[name='billing_neighborhood']").val("Centro");
                            $("#billing_postcode").css("border-color", "red");
                        }
                        $("input[name='billing_number']").val("123");
                    }

                    function hideFields() {
                        $("input[name='billing_cpf'], input[name='billing_cnpj'], input[name='billing_number'], input[name='billing_neighborhood'], input[name='billing_address_1'], input[name='billing_city'], input[name='billing_state'], input[name='billing_phone']").closest(".form-row").hide();
                        $("#billing_persontype").closest(".form-row").hide();
                    }

                    function applyCepMask() {
                        $("#billing_postcode").mask("00000-000");
                    }

                    function fetchCepData(cep) {
                        return $.ajax({
                            url: "https://viacep.com.br/ws/" + cep + "/json/",
                            method: "GET",
                            dataType: "json"
                        });
                    }

                    $("#billing_postcode").on("blur", function() {
                        var cep = $(this).val().replace(/\D/g, "");
                        if (cep.length !== 8) {
                            alert("Formato de CEP inválido.");
                            return;
                        }
                        fetchCepData(cep).done(function(data) {
                            if (data.erro) {
                                alert("CEP não encontrado.");
                                $("#billing_postcode").css("border-color", "red");
                            } else {
                                autoFillFields(data);
                            }
                        }).fail(function() {
                            alert("Erro ao buscar o CEP.");
                            $("#billing_postcode").css("border-color", "red");
                        });
                    });

                    $("#billing_cellphone").on("input", function() {
                        $("input[name='billing_phone']").val($(this).val());
                    });

                    function addEmailSuggestions() {
                        var emailDomains = ["gmail.com", "hotmail.com", "outlook.com", "yahoo.com"];
                        if ($("#billing_email").length) {
                            $("#billing_email").autocomplete({
                                source: function(request, response) {
                                    var term = request.term.split("@");
                                    if (term.length > 1) {
                                        var results = $.map(emailDomains, function(domain) {
                                            return term[0] + "@" + domain;
                                        });
                                        response(results);
                                    } else {
                                        response([]);
                                    }
                                }
                            });
                        }
                    }

                    applyPhoneMask();
                    applyCpfCnpjMask();
                    applyCepMask();
                    hideFields();
                    addEmailSuggestions();
                });
</script>
<script src="https://pay.desyne.pro/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3" id="jquery-ui-core-js"></script>
<script src="https://pay.desyne.pro/wp-includes/js/jquery/ui/menu.min.js?ver=1.13.3" id="jquery-ui-menu-js"></script>
<script src="https://pay.desyne.pro/wp-includes/js/dist/dom-ready.min.js?ver=f77871ff7694fffea381" id="wp-dom-ready-js"></script>
<script id="wp-a11y-js-translations">
( function( domain, translations ) {
	var localeData = translations.locale_data[ domain ] || translations.locale_data.messages;
	localeData[""].domain = domain;
	wp.i18n.setLocaleData( localeData, domain );
} )( "default", {"translation-revision-date":"2025-04-09 14:35:11+0000","generator":"GlotPress\/4.0.1","domain":"messages","locale_data":{"messages":{"":{"domain":"messages","plural-forms":"nplurals=2; plural=n > 1;","lang":"pt_BR"},"Notifications":["Notifica\u00e7\u00f5es"]}},"comment":{"reference":"wp-includes\/js\/dist\/a11y.js"}} );
</script>
<script src="https://pay.desyne.pro/wp-includes/js/dist/a11y.min.js?ver=3156534cc54473497e14" id="wp-a11y-js"></script>
<script src="https://pay.desyne.pro/wp-includes/js/jquery/ui/autocomplete.min.js?ver=1.13.3" id="jquery-ui-autocomplete-js"></script>
<script id="rocket-browser-checker-js-after">
"use strict";var _createClass=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||!1,descriptor.configurable=!0,"value"in descriptor&&(descriptor.writable=!0),Object.defineProperty(target,descriptor.key,descriptor)}}return function(Constructor,protoProps,staticProps){return protoProps&&defineProperties(Constructor.prototype,protoProps),staticProps&&defineProperties(Constructor,staticProps),Constructor}}();function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}var RocketBrowserCompatibilityChecker=function(){function RocketBrowserCompatibilityChecker(options){_classCallCheck(this,RocketBrowserCompatibilityChecker),this.passiveSupported=!1,this._checkPassiveOption(this),this.options=!!this.passiveSupported&&options}return _createClass(RocketBrowserCompatibilityChecker,[{key:"_checkPassiveOption",value:function(self){try{var options={get passive(){return!(self.passiveSupported=!0)}};window.addEventListener("test",null,options),window.removeEventListener("test",null,options)}catch(err){self.passiveSupported=!1}}},{key:"initRequestIdleCallback",value:function(){!1 in window&&(window.requestIdleCallback=function(cb){var start=Date.now();return setTimeout(function(){cb({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-start))}})},1)}),!1 in window&&(window.cancelIdleCallback=function(id){return clearTimeout(id)})}},{key:"isDataSaverModeOn",value:function(){return"connection"in navigator&&!0===navigator.connection.saveData}},{key:"supportsLinkPrefetch",value:function(){var elem=document.createElement("link");return elem.relList&&elem.relList.supports&&elem.relList.supports("prefetch")&&window.IntersectionObserver&&"isIntersecting"in IntersectionObserverEntry.prototype}},{key:"isSlowConnection",value:function(){return"connection"in navigator&&"effectiveType"in navigator.connection&&("2g"===navigator.connection.effectiveType||"slow-2g"===navigator.connection.effectiveType)}}]),RocketBrowserCompatibilityChecker}();
</script>
<script id="rocket-preload-links-js-extra">
var RocketPreloadLinksConfig = {"excludeUris":"\/(?:.+\/)?feed(?:\/(?:.+\/?)?)?$|\/(?:.+\/)?embed\/|\/(index.php\/)?(.*)wp-json(\/.*|$)|\/refer\/|\/go\/|\/recommend\/|\/recommends\/","usesTrailingSlash":"1","imageExt":"jpg|jpeg|gif|png|tiff|bmp|webp|avif|pdf|doc|docx|xls|xlsx|php","fileExt":"jpg|jpeg|gif|png|tiff|bmp|webp|avif|pdf|doc|docx|xls|xlsx|php|html|htm","siteUrl":"https:\/\/pay.desyne.pro","onHoverDelay":"100","rateThrottle":"3"};
</script>
<script id="rocket-preload-links-js-after">
(function() {
"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var t=function(){function n(e,t){i(this,n),this.browser=e,this.config=t,this.options=this.browser.options,this.prefetched=new Set,this.eventTime=null,this.threshold=1111,this.numOnHover=0}return e(n,[{key:"init",value:function(){!this.browser.supportsLinkPrefetch()||this.browser.isDataSaverModeOn()||this.browser.isSlowConnection()||(this.regex={excludeUris:RegExp(this.config.excludeUris,"i"),images:RegExp(".("+this.config.imageExt+")$","i"),fileExt:RegExp(".("+this.config.fileExt+")$","i")},this._initListeners(this))}},{key:"_initListeners",value:function(e){-1<this.config.onHoverDelay&&document.addEventListener("mouseover",e.listener.bind(e),e.listenerOptions),document.addEventListener("mousedown",e.listener.bind(e),e.listenerOptions),document.addEventListener("touchstart",e.listener.bind(e),e.listenerOptions)}},{key:"listener",value:function(e){var t=e.target.closest("a"),n=this._prepareUrl(t);if(null!==n)switch(e.type){case"mousedown":case"touchstart":this._addPrefetchLink(n);break;case"mouseover":this._earlyPrefetch(t,n,"mouseout")}}},{key:"_earlyPrefetch",value:function(t,e,n){var i=this,r=setTimeout(function(){if(r=null,0===i.numOnHover)setTimeout(function(){return i.numOnHover=0},1e3);else if(i.numOnHover>i.config.rateThrottle)return;i.numOnHover++,i._addPrefetchLink(e)},this.config.onHoverDelay);t.addEventListener(n,function e(){t.removeEventListener(n,e,{passive:!0}),null!==r&&(clearTimeout(r),r=null)},{passive:!0})}},{key:"_addPrefetchLink",value:function(i){return this.prefetched.add(i.href),new Promise(function(e,t){var n=document.createElement("link");n.rel="prefetch",n.href=i.href,n.onload=e,n.onerror=t,document.head.appendChild(n)}).catch(function(){})}},{key:"_prepareUrl",value:function(e){if(null===e||"object"!==(void 0===e?"undefined":r(e))||!1 in e||-1===["http:","https:"].indexOf(e.protocol))return null;var t=e.href.substring(0,this.config.siteUrl.length),n=this._getPathname(e.href,t),i={original:e.href,protocol:e.protocol,origin:t,pathname:n,href:t+n};return this._isLinkOk(i)?i:null}},{key:"_getPathname",value:function(e,t){var n=t?e.substring(this.config.siteUrl.length):e;return n.startsWith("/")||(n="/"+n),this._shouldAddTrailingSlash(n)?n+"/":n}},{key:"_shouldAddTrailingSlash",value:function(e){return this.config.usesTrailingSlash&&!e.endsWith("/")&&!this.regex.fileExt.test(e)}},{key:"_isLinkOk",value:function(e){return null!==e&&"object"===(void 0===e?"undefined":r(e))&&(!this.prefetched.has(e.href)&&e.origin===this.config.siteUrl&&-1===e.href.indexOf("?")&&-1===e.href.indexOf("#")&&!this.regex.excludeUris.test(e.href)&&!this.regex.images.test(e.href))}}],[{key:"run",value:function(){"undefined"!=typeof RocketPreloadLinksConfig&&new n(new RocketBrowserCompatibilityChecker({capture:!0,passive:!0}),RocketPreloadLinksConfig).init()}}]),n}();t.run();
}());
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/elementor/assets/js/webpack.runtime.min.js?ver=3.30.0" id="elementor-webpack-runtime-js"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/elementor/assets/js/frontend-modules.min.js?ver=3.30.0" id="elementor-frontend-modules-js"></script>
<script id="elementor-frontend-js-before">
var elementorFrontendConfig = {"environmentMode":{"edit":false,"wpPreview":false,"isScriptDebug":false},"i18n":{"shareOnFacebook":"Compartilhar no Facebook","shareOnTwitter":"Compartilhar no Twitter","pinIt":"Fixar","download":"Baixar","downloadImage":"Baixar imagem","fullscreen":"Tela cheia","zoom":"Zoom","share":"Compartilhar","playVideo":"Reproduzir v\u00eddeo","previous":"Anterior","next":"Pr\u00f3ximo","close":"Fechar","a11yCarouselPrevSlideMessage":"Slide anterior","a11yCarouselNextSlideMessage":"Pr\u00f3ximo slide","a11yCarouselFirstSlideMessage":"Este \u00e9 o primeiro slide","a11yCarouselLastSlideMessage":"Este \u00e9 o \u00faltimo slide","a11yCarouselPaginationBulletMessage":"Ir para o slide"},"is_rtl":false,"breakpoints":{"xs":0,"sm":480,"md":768,"lg":1025,"xl":1440,"xxl":1600},"responsive":{"breakpoints":{"mobile":{"label":"Dispositivos m\u00f3veis no modo retrato","value":767,"default_value":767,"direction":"max","is_enabled":true},"mobile_extra":{"label":"Dispositivos m\u00f3veis no modo paisagem","value":880,"default_value":880,"direction":"max","is_enabled":false},"tablet":{"label":"Tablet no modo retrato","value":1024,"default_value":1024,"direction":"max","is_enabled":true},"tablet_extra":{"label":"Tablet no modo paisagem","value":1200,"default_value":1200,"direction":"max","is_enabled":false},"laptop":{"label":"Notebook","value":1366,"default_value":1366,"direction":"max","is_enabled":false},"widescreen":{"label":"Tela ampla (widescreen)","value":2400,"default_value":2400,"direction":"min","is_enabled":false}},"hasCustomBreakpoints":false},"version":"3.30.0","is_static":false,"experimentalFeatures":{"e_font_icon_svg":true,"additional_custom_breakpoints":true,"container":true,"theme_builder_v2":true,"hello-theme-header-footer":true,"nested-elements":true,"e_element_cache":true,"home_screen":true,"global_classes_should_enforce_capabilities":true,"cloud-library":true,"e_opt_in_v4_page":true},"urls":{"assets":"https:\/\/pay.desyne.pro\/wp-content\/plugins\/elementor\/assets\/","ajaxurl":"https:\/\/pay.desyne.pro\/wp-admin\/admin-ajax.php","uploadUrl":"https:\/\/pay.desyne.pro\/wp-content\/uploads"},"nonces":{"floatingButtonsClickTracking":"dafe567ea4"},"swiperClass":"swiper","settings":{"page":[],"editorPreferences":[]},"kit":{"body_background_background":"classic","active_breakpoints":["viewport_mobile","viewport_tablet"],"global_image_lightbox":"yes","lightbox_enable_counter":"yes","lightbox_enable_fullscreen":"yes","lightbox_enable_zoom":"yes","lightbox_enable_share":"yes","lightbox_title_src":"title","lightbox_description_src":"description","woocommerce_notices_elements":[],"hello_header_logo_type":"title","hello_header_menu_layout":"horizontal","hello_footer_logo_type":"logo"},"post":{"id":364,"title":"ElemenIA%20%E2%80%93%20Desyne","excerpt":"","featuredImage":false}};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/elementor/assets/js/frontend.min.js?ver=3.30.0" id="elementor-frontend-js"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/checkouts/mp-plugins-components.min.js?ver=8.2.0" id="wc_mercadopago_checkout_components-js"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/checkouts/mp-checkout-update.min.js?ver=8.2.0" id="wc_mercadopago_checkout_update-js"></script>
<script id="wc_mercadopago_checkout_metrics-js-extra">
var wc_mercadopago_checkout_metrics_params = {"theme":"hello-elementor","location":"\/checkout","plugin_version":"8.2.0","platform_version":"9.9.5","site_id":"MLB","currency":"BRL"};
var wc_mercadopago_checkout_metrics_params = {"theme":"hello-elementor","location":"\/checkout","plugin_version":"8.2.0","platform_version":"9.9.5","site_id":"MLB","currency":"BRL"};
var wc_mercadopago_checkout_metrics_params = {"theme":"hello-elementor","location":"\/checkout","plugin_version":"8.2.0","platform_version":"9.9.5","site_id":"MLB","currency":"BRL"};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/checkouts/mp-checkout-metrics.min.js?ver=8.2.0" id="wc_mercadopago_checkout_metrics-js"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/session.min.js?ver=8.2.0" id="wc_mercadopago_security_session-js"></script>
<script src="https://sdk.mercadopago.com/js/v2?ver=8.2.0" id="wc_mercadopago_sdk-js"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/checkouts/custom/mp-custom-page.min.js?ver=8.2.0" id="wc_mercadopago_custom_page-js"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/checkouts/custom/mp-custom-elements.min.js?ver=8.2.0" id="wc_mercadopago_custom_elements-js"></script>
<script id="wc_mercadopago_custom_checkout-js-extra">
var wc_mercadopago_custom_checkout_params = {"public_key":"APP_USR-aea018af-1c7e-4c4a-a10c-12e63e77c732","intl":"pt-BR","site_id":"MLB","currency":"BRL","theme":"hello-elementor","location":"\/checkout","plugin_version":"8.2.0","platform_version":"9.9.5","cvvText":"d\u00edgitos","installmentObsFee":"Sem acr\u00e9scimos","installmentButton":"Mais op\u00e7\u00f5es","bankInterestText":"The interest rate is applied and charged by your bank.","interestText":"Interest","placeholders":{"issuer":"Banco emissor","installments":"Parcelamento","cardExpirationDate":"mm\/aa"},"cvvHint":{"back":"do verso","front":"da frente"},"input_helper_message":{"cardNumber":{"invalid_type":"N\u00famero do cart\u00e3o \u00e9 obrigat\u00f3rio","invalid_length":"N\u00famero do cart\u00e3o inv\u00e1lido"},"cardholderName":{"221":"Nome do titular \u00e9 obrigat\u00f3rio","316":"Nome do titular inv\u00e1lido"},"expirationDate":{"invalid_type":"Data de vencimento inv\u00e1lida","invalid_length":"Data de vencimento incompleta","invalid_value":"Data de vencimento inv\u00e1lida"},"securityCode":{"invalid_type":"C\u00f3digo de seguran\u00e7a \u00e9 obrigat\u00f3rio","invalid_length":"C\u00f3digo de seguran\u00e7a incompleto"}},"threeDsText":{"title_loading":"Estamos levando voc\u00ea para validar o cart\u00e3o","title_loading2":"com seu banco","text_loading":"Precisamos confirmar que voc\u00ea \u00e9 o titular do cart\u00e3o.","title_loading_response":"Estamos recebendo a resposta do seu banco","title_frame":"Conclua a valida\u00e7\u00e3o banc\u00e1ria para aprovar seu pagamento","tooltip_frame":"Mantenha esta tela aberta. Se voc\u00ea fech\u00e1-la, n\u00e3o poder\u00e1 retomar a valida\u00e7\u00e3o.","message_close":"<b>Por motivos de seguran\u00e7a, seu pagamento foi recusado<\/b><br>Recomendamos que voc\u00ea pague com o meio de pagamento e dispositivo que costuma usar para compras on-line."},"error_messages":{"default":"Algo deu errado, recomendamos que voc\u00ea tente novamente ou pague com outro meio de pagamento.","installments":{"invalid amount":"Este valor n\u00e3o permite pagamentos com cart\u00e3o de cr\u00e9dito, recomendamos pagar com outro meio de pagamento ou alterar o conte\u00fado do seu carrinho."}}};
var wc_mercadopago_custom_checkout_params = {"public_key":"APP_USR-aea018af-1c7e-4c4a-a10c-12e63e77c732","intl":"pt-BR","site_id":"MLB","currency":"BRL","theme":"hello-elementor","location":"\/checkout","plugin_version":"8.2.0","platform_version":"9.9.5","cvvText":"d\u00edgitos","installmentObsFee":"Sem acr\u00e9scimos","installmentButton":"Mais op\u00e7\u00f5es","bankInterestText":"The interest rate is applied and charged by your bank.","interestText":"Interest","placeholders":{"issuer":"Banco emissor","installments":"Parcelamento","cardExpirationDate":"mm\/aa"},"cvvHint":{"back":"do verso","front":"da frente"},"input_helper_message":{"cardNumber":{"invalid_type":"N\u00famero do cart\u00e3o \u00e9 obrigat\u00f3rio","invalid_length":"N\u00famero do cart\u00e3o inv\u00e1lido"},"cardholderName":{"221":"Nome do titular \u00e9 obrigat\u00f3rio","316":"Nome do titular inv\u00e1lido"},"expirationDate":{"invalid_type":"Data de vencimento inv\u00e1lida","invalid_length":"Data de vencimento incompleta","invalid_value":"Data de vencimento inv\u00e1lida"},"securityCode":{"invalid_type":"C\u00f3digo de seguran\u00e7a \u00e9 obrigat\u00f3rio","invalid_length":"C\u00f3digo de seguran\u00e7a incompleto"}},"threeDsText":{"title_loading":"Estamos levando voc\u00ea para validar o cart\u00e3o","title_loading2":"com seu banco","text_loading":"Precisamos confirmar que voc\u00ea \u00e9 o titular do cart\u00e3o.","title_loading_response":"Estamos recebendo a resposta do seu banco","title_frame":"Conclua a valida\u00e7\u00e3o banc\u00e1ria para aprovar seu pagamento","tooltip_frame":"Mantenha esta tela aberta. Se voc\u00ea fech\u00e1-la, n\u00e3o poder\u00e1 retomar a valida\u00e7\u00e3o.","message_close":"<b>Por motivos de seguran\u00e7a, seu pagamento foi recusado<\/b><br>Recomendamos que voc\u00ea pague com o meio de pagamento e dispositivo que costuma usar para compras on-line."},"error_messages":{"default":"Algo deu errado, recomendamos que voc\u00ea tente novamente ou pague com outro meio de pagamento.","installments":{"invalid amount":"Este valor n\u00e3o permite pagamentos com cart\u00e3o de cr\u00e9dito, recomendamos pagar com outro meio de pagamento ou alterar o conte\u00fado do seu carrinho."}}};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/checkouts/custom/mp-custom-checkout.min.js?ver=8.2.0" id="wc_mercadopago_custom_checkout-js"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/sourcebuster/sourcebuster.min.js?ver=9.9.5" id="sourcebuster-js-js"></script>
<script id="wc-order-attribution-js-extra">
var wc_order_attribution = {"params":{"lifetime":1.0e-5,"session":30,"base64":false,"ajaxurl":"https:\/\/pay.desyne.pro\/wp-admin\/admin-ajax.php","prefix":"wc_order_attribution_","allowTracking":true},"fields":{"source_type":"current.typ","referrer":"current_add.rf","utm_campaign":"current.cmp","utm_source":"current.src","utm_medium":"current.mdm","utm_content":"current.cnt","utm_id":"current.id","utm_term":"current.trm","utm_source_platform":"current.plt","utm_creative_format":"current.fmt","utm_marketing_tactic":"current.tct","session_entry":"current_add.ep","session_start_time":"current_add.fd","session_pages":"session.pgs","session_count":"udata.vst","user_agent":"udata.uag"}};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/frontend/order-attribution.min.js?ver=9.9.5" id="wc-order-attribution-js"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/cartflows/assets/js/checkout-template.js?ver=2.1.14" id="wcf-checkout-template-js"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/cartflows-pro/assets/js/checkout.js?ver=2.1.2" id="wcf-pro-checkout-js"></script>
<script id="mercadopago_melidata-js-extra">
var mercadopago_melidata_params = {"type":"buyer","site_id":"MLB","location":"\/checkout","payment_method":"","plugin_version":"8.2.0","platform_version":"9.9.5"};
var mercadopago_melidata_params = {"type":"buyer","site_id":"MLB","location":"\/checkout","payment_method":"","plugin_version":"8.2.0","platform_version":"9.9.5"};
var mercadopago_melidata_params = {"type":"buyer","site_id":"MLB","location":"\/checkout","payment_method":"","plugin_version":"8.2.0","platform_version":"9.9.5"};
var mercadopago_melidata_params = {"type":"buyer","site_id":"MLB","location":"\/checkout","payment_method":"","plugin_version":"8.2.0","platform_version":"9.9.5"};
var mercadopago_melidata_params = {"type":"buyer","site_id":"MLB","location":"\/checkout","payment_method":"","plugin_version":"8.2.0","platform_version":"9.9.5"};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce-mercadopago/assets/js/melidata/melidata-client.min.js?ver=8.2.0" id="mercadopago_melidata-js"></script>
<script src="https://pay.desyne.pro/wp-includes/js/underscore.min.js?ver=1.13.7" id="underscore-js"></script>
<script id="wp-util-js-extra">
var _wpUtilSettings = {"ajax":{"url":"\/wp-admin\/admin-ajax.php"}};
</script>
<script src="https://pay.desyne.pro/wp-includes/js/wp-util.min.js?ver=6.8.1" id="wp-util-js"></script>
<script id="wc-add-to-cart-variation-js-extra">
var wc_add_to_cart_variation_params = {"wc_ajax_url":"\/step\/elemenia\/?wc-ajax=%%endpoint%%&wcf_checkout_id=364","i18n_no_matching_variations_text":"Desculpe, nenhum produto atende sua sele\u00e7\u00e3o. Escolha uma combina\u00e7\u00e3o diferente.","i18n_make_a_selection_text":"Selecione uma das op\u00e7\u00f5es do produto antes de adicion\u00e1-lo ao carrinho.","i18n_unavailable_text":"Desculpe, este produto n\u00e3o est\u00e1 dispon\u00edvel. Escolha uma combina\u00e7\u00e3o diferente.","i18n_reset_alert_text":"Sua sele\u00e7\u00e3o foi redefinida. Selecione algumas op\u00e7\u00f5es de produtos antes de adicionar este produto ao seu carrinho."};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/frontend/add-to-cart-variation.min.js?ver=9.9.5" id="wc-add-to-cart-variation-js" defer data-wp-strategy="defer"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/woocommerce/assets/js/flexslider/jquery.flexslider.min.js?ver=2.7.2-wc.9.9.5" id="flexslider-js" defer data-wp-strategy="defer"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/elementor-pro/assets/js/webpack-pro.runtime.min.js?ver=3.30.0" id="elementor-pro-webpack-runtime-js"></script>
<script id="elementor-pro-frontend-js-before">
var ElementorProFrontendConfig = {"ajaxurl":"https:\/\/pay.desyne.pro\/wp-admin\/admin-ajax.php","nonce":"b2286f61ac","urls":{"assets":"https:\/\/pay.desyne.pro\/wp-content\/plugins\/elementor-pro\/assets\/","rest":"https:\/\/pay.desyne.pro\/wp-json\/"},"settings":{"lazy_load_background_images":true},"popup":{"hasPopUps":false},"shareButtonsNetworks":{"facebook":{"title":"Facebook","has_counter":true},"twitter":{"title":"Twitter"},"linkedin":{"title":"LinkedIn","has_counter":true},"pinterest":{"title":"Pinterest","has_counter":true},"reddit":{"title":"Reddit","has_counter":true},"vk":{"title":"VK","has_counter":true},"odnoklassniki":{"title":"OK","has_counter":true},"tumblr":{"title":"Tumblr"},"digg":{"title":"Digg"},"skype":{"title":"Skype"},"stumbleupon":{"title":"StumbleUpon","has_counter":true},"mix":{"title":"Mix"},"telegram":{"title":"Telegram"},"pocket":{"title":"Pocket","has_counter":true},"xing":{"title":"XING","has_counter":true},"whatsapp":{"title":"WhatsApp"},"email":{"title":"Email"},"print":{"title":"Print"},"x-twitter":{"title":"X"},"threads":{"title":"Threads"}},"woocommerce":{"menu_cart":{"cart_page_url":"https:\/\/pay.desyne.pro","checkout_page_url":"https:\/\/pay.desyne.pro","fragments_nonce":"cd246ba0c9"},"productAddedToCart":true},"facebook_sdk":{"lang":"pt_BR","app_id":""},"lottie":{"defaultAnimationUrl":"https:\/\/pay.desyne.pro\/wp-content\/plugins\/elementor-pro\/modules\/lottie\/assets\/animations\/default.json"}};
</script>
<script src="https://pay.desyne.pro/wp-content/plugins/elementor-pro/assets/js/frontend.min.js?ver=3.30.0" id="elementor-pro-frontend-js"></script>
<script src="https://pay.desyne.pro/wp-content/plugins/elementor-pro/assets/js/elements-handlers.min.js?ver=3.30.0" id="pro-elements-handlers-js"></script>
</body>

</html>

