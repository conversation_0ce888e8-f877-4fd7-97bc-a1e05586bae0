<div id="order_review" class="woocommerce-checkout-review-order">
			<table class="shop_table woocommerce-checkout-review-order-table" data-update-time="1752367814">
	<thead>
		<tr>
			<th class="product-name">Produto</th>
			<th class="product-total">Subtotal</th>
		</tr>
	</thead>
	<tbody>
						<tr class="cart_item">
					<td class="product-name">
						<div class="wcf-product-image"> <div class="wcf-product-thumbnail"><img src="https://pay.membero.pro/wp-content/plugins/woocommerce/assets/images/placeholder.png" width="300" height="300" class="woocommerce-placeholder wp-post-image" alt="Conteúdo de marcação"> </div> <div class="wcf-product-name">Teste</div></div>&nbsp;						 <strong class="product-quantity">×&nbsp;1</strong>											</td>
					<td class="product-total">
						<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">R$</span>&nbsp;50,00</bdi></span>					</td>
				</tr>
					</tbody>
	<tfoot>
				<tr class="cart-subtotal">
			<th>Subtotal</th>
			<td><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">R$</span>&nbsp;50,00</bdi></span></td>
		</tr>

				
				
		
		<tr class="order-total">
			<th>Total</th>
			<td><strong><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">R$</span>&nbsp;50,00</bdi></span></strong> </td>
		</tr>

		
	</tfoot>
</table>

<div class="wcf-bump-order-grid-wrap wcf-all-bump-order-wrap wcf-after-order" data-update-time="1752367814"></div>		<div class="wcf-payment-option-heading">
			<h3 id="payment_options_heading">Pagamento</h3>
		</div>
		<div id="payment" class="woocommerce-checkout-payment">
			<ul class="wc_payment_methods payment_methods methods">
			<li class="wc_payment_method payment_method_woo-mercado-pago-custom">
	<input id="payment_method_woo-mercado-pago-custom" type="radio" class="input-radio" name="payment_method" value="woo-mercado-pago-custom" checked="checked" data-order_button_text="">

	<label for="payment_method_woo-mercado-pago-custom">
		Cartão de crédito ou débito <img src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/icons/icon-custom.png?ver=8.2.0" alt="Cartão de crédito ou débito">	</label>
			<div class="payment_box payment_method_woo-mercado-pago-custom">
			<div class="mp-checkout-custom-load" style="display: none;">
    <div class="spinner-card-form"></div>
</div>
<div class="mp-checkout-container">
            <div class="mp-checkout-custom-container" style="display: block;">
            
                            <div class="mp-wallet-button-container">

                    <div class="mp-wallet-button-title">
                        <span>Pague com seus cartões salvos</span>
                    </div>

                    <div class="mp-wallet-button-description">
                        Acesse o Mercado Pago e pague mais rápido sem preencher formulários.                    </div>

                    <div class="mp-wallet-button-button">
                        <button id="mp-wallet-button" onclick="submitWalletButton(event)" value="">
                            <img src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/gateways/wallet-button/logo.svg?ver=8.2.0">
                        </button>
                    </div>
                </div>
            
            <div id="mp-custom-checkout-form-container">
                <div class="mp-checkout-custom-available-payments">
                    <div class="mp-checkout-custom-available-payments-header">
                        <div class="mp-checkout-custom-available-payments-title">
                            <img src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/icons/icon-blue-card.png?ver=8.2.0" class="mp-icon">
                            <p class="mp-checkout-custom-available-payments-text">
                                Quais cartões você pode usar?                            </p>
                        </div>

                        <img src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/custom/chevron-down.png?ver=8.2.0" class="mp-checkout-custom-available-payments-collapsible">
                    </div>

                    <div class="mp-checkout-custom-available-payments-content">
                        <payment-methods methods="[{&quot;title&quot;:&quot;Cart\u00f5es de cr\u00e9dito&quot;,&quot;label&quot;:&quot;At\u00e9 12x&quot;,&quot;payment_methods&quot;:[{&quot;src&quot;:&quot;https:\/\/http2.mlstatic.com\/storage\/logos-api-admin\/0daa1670-5c81-11ec-ae75-df2bef173be2-xl.png&quot;,&quot;alt&quot;:&quot;Mastercard&quot;},{&quot;src&quot;:&quot;https:\/\/http2.mlstatic.com\/storage\/logos-api-admin\/d589be70-eb86-11e9-b9a8-097ac027487d-xl.png&quot;,&quot;alt&quot;:&quot;Visa&quot;},{&quot;src&quot;:&quot;https:\/\/http2.mlstatic.com\/storage\/logos-api-admin\/fcdc39d0-57ad-11e8-8359-5d73691de80c-xl.svg&quot;,&quot;alt&quot;:&quot;Hipercard&quot;},{&quot;src&quot;:&quot;https:\/\/www.mercadopago.com\/org-img\/MP3\/API\/logos\/elo.gif&quot;,&quot;alt&quot;:&quot;Elo&quot;},{&quot;src&quot;:&quot;https:\/\/http2.mlstatic.com\/storage\/logos-api-admin\/b4785730-c13f-11ee-b4b3-bb9a23b70639-xl.svg&quot;,&quot;alt&quot;:&quot;American Express&quot;}]},{&quot;title&quot;:&quot;Cart\u00f5es de d\u00e9bito&quot;,&quot;payment_methods&quot;:[{&quot;src&quot;:&quot;https:\/\/www.mercadopago.com\/org-img\/MP3\/API\/logos\/elo.gif&quot;,&quot;alt&quot;:&quot;Elo Debito&quot;}]}]"><div class="mp-payment-methods-container"><div class="mp-payment-method-type-container"><div class="mp-payment-methods-header"><p class="mp-payment-methods-title">Cartões de crédito</p><div class="mp-payment-methods-badge"><span class="mp-payment-methods-badge-text">Até 12x</span></div></div><div class="mp-payment-methods-content"><payment-method-logo src="https://http2.mlstatic.com/storage/logos-api-admin/0daa1670-5c81-11ec-ae75-df2bef173be2-xl.png" alt="Mastercard"><div class="mp-payment-method-logo-container"><img class="mp-payment-method-logo-image" alt="Mastercard" src="https://http2.mlstatic.com/storage/logos-api-admin/0daa1670-5c81-11ec-ae75-df2bef173be2-xl.png"></div></payment-method-logo><payment-method-logo src="https://http2.mlstatic.com/storage/logos-api-admin/d589be70-eb86-11e9-b9a8-097ac027487d-xl.png" alt="Visa"><div class="mp-payment-method-logo-container"><img class="mp-payment-method-logo-image" alt="Visa" src="https://http2.mlstatic.com/storage/logos-api-admin/d589be70-eb86-11e9-b9a8-097ac027487d-xl.png"></div></payment-method-logo><payment-method-logo src="https://http2.mlstatic.com/storage/logos-api-admin/fcdc39d0-57ad-11e8-8359-5d73691de80c-xl.svg" alt="Hipercard"><div class="mp-payment-method-logo-container"><img class="mp-payment-method-logo-image" alt="Hipercard" src="https://http2.mlstatic.com/storage/logos-api-admin/fcdc39d0-57ad-11e8-8359-5d73691de80c-xl.svg"></div></payment-method-logo><payment-method-logo src="https://www.mercadopago.com/org-img/MP3/API/logos/elo.gif" alt="Elo"><div class="mp-payment-method-logo-container"><img class="mp-payment-method-logo-image" alt="Elo" src="https://www.mercadopago.com/org-img/MP3/API/logos/elo.gif"></div></payment-method-logo><payment-method-logo src="https://http2.mlstatic.com/storage/logos-api-admin/b4785730-c13f-11ee-b4b3-bb9a23b70639-xl.svg" alt="American Express"><div class="mp-payment-method-logo-container"><img class="mp-payment-method-logo-image" alt="American Express" src="https://http2.mlstatic.com/storage/logos-api-admin/b4785730-c13f-11ee-b4b3-bb9a23b70639-xl.svg"></div></payment-method-logo></div></div><div class="mp-payment-method-type-container"><div class="mp-payment-methods-header"><p class="mp-payment-methods-title">Cartões de débito</p></div><div class="mp-payment-methods-content"><payment-method-logo src="https://www.mercadopago.com/org-img/MP3/API/logos/elo.gif" alt="Elo Debito"><div class="mp-payment-method-logo-container"><img class="mp-payment-method-logo-image" alt="Elo Debito" src="https://www.mercadopago.com/org-img/MP3/API/logos/elo.gif"></div></payment-method-logo></div></div></div></payment-methods>

                                                <hr>
                    </div>
                </div>

                <div class="mp-checkout-custom-card-form">
                    <p class="mp-checkout-custom-card-form-title">
                        Preencha os dados do seu cartão                    </p>

                    <div class="mp-checkout-custom-card-row">
                        <input-label isoptinal="false" message="Número do cartão" for="mp-card-number">
                        <div class="mp-input-label" data-cy="input-label">Número do cartão<b style="color: red;">*</b></div></input-label>

                        <div class="mp-checkout-custom-card-input" id="form-checkout__cardNumber-container"><iframe frameborder="0" allowtransparency="true" scrolling="no" height="100%" width="100%" name="cardNumber" style="color-scheme: normal" data-primary="true" src="https://api-static.mercadopago.com/secure-fields"></iframe></div>

                        <input-helper isvisible="false" message="Dado obrigatório" input-id="mp-card-number-helper">
                        <div class="mp-helper" id="mp-card-number-helper" data-cy="helper-container" style="display: none;"><div class="mp-helper-icon">!</div><div class="mp-helper-message" data-cy="helper-message">Dado obrigatório</div></div></input-helper>
                    </div>

                    <div class="mp-checkout-custom-card-row" id="mp-card-holder-div">
                        <input-label message="Nome do titular como aparece no cartão" isoptinal="false">
                        <div class="mp-input-label" data-cy="input-label">Nome do titular como aparece no cartão<b style="color: red;">*</b></div></input-label>

                        <input class="mp-checkout-custom-card-input mp-card-holder-name" placeholder="Ex.: María López" id="form-checkout__cardholderName" name="mp-card-holder-name" data-checkout="cardholderName">

                        <input-helper isvisible="false" message="Dado obrigatório" input-id="mp-card-holder-name-helper" data-main="mp-card-holder-name">
                        <div class="mp-helper" id="mp-card-holder-name-helper" data-cy="helper-container" style="display: none;"><div class="mp-helper-icon">!</div><div class="mp-helper-message" data-cy="helper-message">Dado obrigatório</div></div></input-helper>
                    </div>

                    <div class="mp-checkout-custom-card-row mp-checkout-custom-dual-column-row">
                        <div class="mp-checkout-custom-card-column">
                            <input-label message="Vencimento" isoptinal="false">
                            <div class="mp-input-label" data-cy="input-label">Vencimento<b style="color: red;">*</b></div></input-label>

                            <div id="form-checkout__expirationDate-container" class="mp-checkout-custom-card-input mp-checkout-custom-left-card-input"><iframe frameborder="0" allowtransparency="true" scrolling="no" height="100%" width="100%" name="expirationDate" style="color-scheme: normal" src="https://api-static.mercadopago.com/secure-fields"></iframe></div>

                            <input-helper isvisible="false" message="Dado obrigatório" input-id="mp-expiration-date-helper">
                            <div class="mp-helper" id="mp-expiration-date-helper" data-cy="helper-container" style="display: none;"><div class="mp-helper-icon">!</div><div class="mp-helper-message" data-cy="helper-message">Dado obrigatório</div></div></input-helper>
                        </div>

                        <div class="mp-checkout-custom-card-column">
                            <input-label message="Código de segurança" isoptinal="false">
                            <div class="mp-input-label" data-cy="input-label">Código de segurança<b style="color: red;">*</b></div></input-label>

                            <div id="form-checkout__securityCode-container" class="mp-checkout-custom-card-input"><iframe frameborder="0" allowtransparency="true" scrolling="no" height="100%" width="100%" name="securityCode" style="color-scheme: normal" src="https://api-static.mercadopago.com/secure-fields"></iframe></div>

                            <p id="mp-security-code-info" class="mp-checkout-custom-info-text"></p>

                            <input-helper isvisible="false" message="Dado obrigatório" input-id="mp-security-code-helper">
                            <div class="mp-helper" id="mp-security-code-helper" data-cy="helper-container" style="display: none;"><div class="mp-helper-icon">!</div><div class="mp-helper-message" data-cy="helper-message">Dado obrigatório</div></div></input-helper>
                        </div>
                    </div>

                    <div id="mp-doc-div" class="mp-checkout-custom-input-document" style="display: none;">
                        <input-document label-message="Documento do titular" helper-invalid="Insira o documento completo." helper-empty="Preencha este campo." helper-wrong="Insira um documento válido." input-name="identificationNumber" hidden-id="form-checkout__identificationNumber" input-data-checkout="doc_number" select-id="form-checkout__identificationType" select-name="identificationType" select-data-checkout="doc_type" flag-error="docNumberError">
                        <div class="mp-input-document" data-cy="input-document-container"><input-label message="Documento do titular" isoptional="false"><div class="mp-input-label" data-cy="input-label">Documento do titular<b style="color: red;">*</b></div></input-label><div class="mp-input" id="form-checkout__identificationNumber-container"><select class="mp-document-select" name="identificationType" id="form-checkout__identificationType" data-checkout="doc_type" data-cy="select-document"><option value="CPF">CPF</option><option value="CNPJ">CNPJ</option></select><div class="mp-vertical-line"></div><input name="identificationNumber" data-checkout="doc_number" data-cy="input-document" class="mp-document" type="text" inputmode="text" maxlength="20" placeholder=""></div><input type="hidden" id="form-checkout__identificationNumber" value=""><input-helper isvisible="false" message="Preencha este campo." input-id="mp-doc-number-helper"><div class="mp-helper" id="mp-doc-number-helper" data-cy="helper-container" style="display: none;"><div class="mp-helper-icon">!</div><div class="mp-helper-message" data-cy="helper-message">Preencha este campo.</div></div></input-helper></div></input-document>
                    </div>
                </div>

                <div id="mp-checkout-custom-installments" class="mp-checkout-custom-installments-display-none">
                    <p class="mp-checkout-custom-card-form-title">
                        Escolha o número de parcelas                    </p>

                    <div id="mp-checkout-custom-issuers-container" class="mp-checkout-custom-issuers-container">
                        <div class="mp-checkout-custom-card-row">
                            <input-label isoptinal="false" message="Banco emissor" for="mp-issuer">
                            <div class="mp-input-label" data-cy="input-label">Banco emissor<b style="color: red;">*</b></div></input-label>
                        </div>

                        <div class="mp-input-select-input">
                            <select id="form-checkout__issuer" class="mp-input-select-select" data-name="issuer" autocomplete="off"><option data-placeholder="" selected="" disabled="">Banco emissor</option></select>
                        </div>
                    </div>

                    <div id="mp-checkout-custom-installments-container" class="mp-checkout-custom-installments-container"></div>

                    <input-helper isvisible="false" message="Escolha o número de parcelas" input-id="mp-installments-helper">
                    <div class="mp-helper" id="mp-installments-helper" data-cy="helper-container" style="display: none;"><div class="mp-helper-icon">!</div><div class="mp-helper-message" data-cy="helper-message">Escolha o número de parcelas</div></div></input-helper>

                    <select style="display: none;" data-checkout="installments" name="installments" id="form-checkout__installments" class="mp-input-select-select">
                    <option data-placeholder="" selected="" disabled="">Parcelamento</option></select>

                    <div id="mp-checkout-custom-box-input-tax-cft">
                        <div id="mp-checkout-custom-box-input-tax-tea">
                            <div id="mp-checkout-custom-tax-tea-text"></div>
                        </div>
                        <div id="mp-checkout-custom-tax-cft-text"></div>
                    </div>
                </div>

                <div class="mp-checkout-custom-terms-and-conditions">
                    <terms-and-conditions description="Ao continuar, você concorda com nossos" link-text="Termos e condições" link-src="https://www.mercadopago.com.br/ajuda/termos-e-politicas_194">
                    <div class="mp-terms-and-conditions-container" data-cy="terms-and-conditions-container"><span class="mp-terms-and-conditions-text">Ao continuar, você concorda com nossos</span><a class="mp-terms-and-conditions-link" href="https://www.mercadopago.com.br/ajuda/termos-e-politicas_194" target="blank">Termos e condições</a></div></terms-and-conditions>
                </div>
            </div>


        </div>
    
</div>

<div id="mercadopago-utilities" style="display:none;">
    <input type="hidden" id="mp-amount" value="50" name="mercadopago_custom[amount]">
    <input type="hidden" id="currency_ratio" value="1" name="mercadopago_custom[currency_ratio]">
    <input type="hidden" id="paymentMethodId" name="mercadopago_custom[payment_method_id]" value="">
    <input type="hidden" id="mp_checkout_type" name="mercadopago_custom[checkout_type]" value="custom">
    <input type="hidden" id="cardExpirationMonth" data-checkout="cardExpirationMonth" value="">
    <input type="hidden" id="cardExpirationYear" data-checkout="cardExpirationYear" value="">
    <input type="hidden" id="cardTokenId" name="mercadopago_custom[token]" value="">
    <input type="hidden" id="cardInstallments" name="mercadopago_custom[installments]" value="">
    <input type="hidden" id="mpCardSessionId" name="mercadopago_custom[session_id]" value="">
    <input type="hidden" id="payerDocNumber" name="mercadopago_custom[doc_number]" value="">
    <input type="hidden" id="payerDocType" name="mercadopago_custom[doc_type]" value="">
</div>

<script type="text/javascript">
    function submitWalletButton(event) {
        event.preventDefault();
        jQuery('#mp_checkout_type').val('wallet_button');
        jQuery('form.checkout, form#order_review').submit();
    }

    var availablePayment = document.getElementsByClassName('mp-checkout-custom-available-payments')[0];
    var collapsible = availablePayment.getElementsByClassName('mp-checkout-custom-available-payments-header')[0];

    collapsible.addEventListener("click", function() {
        const icon = collapsible.getElementsByClassName('mp-checkout-custom-available-payments-collapsible')[0];
        const content = availablePayment.getElementsByClassName('mp-checkout-custom-available-payments-content')[0];

        if (content.style.maxHeight) {
            content.style.maxHeight = null;
            content.style.padding = "0px";
            icon.src = "https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/custom/chevron-down.png?ver=8.2.0";
        } else {
            let hg = content.scrollHeight + 15 + "px";
            content.style.setProperty("max-height", hg, "important");
            content.style.setProperty("padding", "24px 0px 0px", "important");
            icon.src = "https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/custom/chevron-up.png?ver=8.2.0";
        }
    });
</script>

		</div>
	</li>
<li class="wc_payment_method payment_method_woo-mercado-pago-pix">
	<input id="payment_method_woo-mercado-pago-pix" type="radio" class="input-radio" name="payment_method" value="woo-mercado-pago-pix" data-order_button_text="">

	<label for="payment_method_woo-mercado-pago-pix">
		Pix <img src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/icons/icon-pix.png?ver=8.2.0" alt="Pix">	</label>
			<div class="payment_box payment_method_woo-mercado-pago-pix" style="display:none;">
			
<div class="mp-checkout-container">
     
        <div class="mp-checkout-pix-container">
            
            <pix-template title="Pague de forma segura e instantânea" subtitle="Ao confirmar a compra, nós vamos te mostrar o código para fazer o pagamento." alt="Logo Pix" src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/pix/pix.png?ver=8.2.0">
            <div class="mp-pix-template-container" data-cy="pix-template-container"><img class="mp-pix-template-image" src="https://pay.membero.pro/wp-content/plugins/woocommerce-mercadopago/assets/images/checkouts/pix/pix.png?ver=8.2.0" alt="Logo Pix"><p class="mp-pix-template-title">Pague de forma segura e instantânea</p><p class="mp-pix-template-subtitle">Ao confirmar a compra, nós vamos te mostrar o código para fazer o pagamento.</p></div></pix-template>

            <div class="mp-checkout-pix-terms-and-conditions">
                <terms-and-conditions description="Ao continuar, você concorda com nossos" link-text="Termos e condições" link-src="https://www.mercadopago.com.br/ajuda/termos-e-politicas_194">
                <div class="mp-terms-and-conditions-container" data-cy="terms-and-conditions-container"><span class="mp-terms-and-conditions-text">Ao continuar, você concorda com nossos</span><a class="mp-terms-and-conditions-link" href="https://www.mercadopago.com.br/ajuda/termos-e-politicas_194" target="blank">Termos e condições</a></div></terms-and-conditions>
            </div>
        </div>
     
</div>

<script type="text/javascript">
    if (document.getElementById("payment_method_woo-mercado-pago-custom")) {
        jQuery("form.checkout").on("checkout_place_order_woo-mercado-pago-pix", function() {
            cardFormLoad();
        });
    }
</script>
		</div>
	</li>
		</ul>
		<div class="form-row place-order">
		<noscript>
			Seu navegador não suporta JavaScript ou ele está desativado. Certifique-se de clicar no botão <em>Atualizar totais</em> antes de finalizar o seu pedido. Você poderá ser cobrado mais do que a quantidade indicada acima, se não fizer isso.			<br/><button type="submit" class="button alt" name="woocommerce_checkout_update_totals" value="Atualizar">Atualizar</button>
		</noscript>

			<div class="woocommerce-terms-and-conditions-wrapper">
		<div class="woocommerce-privacy-policy-text"><p>Os seus dados pessoais serão utilizados para processar a sua compra, apoiar a sua experiência em todo este site e para outros fins descritos na nossa <a href="" class="woocommerce-privacy-policy-link" target="_blank">política de privacidade</a>.</p>
</div>
			</div>
	
		<div class="wcf-bump-order-grid-wrap wcf-all-bump-order-wrap wcf-after-payment" data-update-time="1752367814"></div>
		<button type="submit" class="button alt" name="woocommerce_checkout_place_order" id="place_order" value="Finalizar compra&nbsp;&nbsp;R$&nbsp;50,00" data-value="Finalizar compra&nbsp;&nbsp;R$&nbsp;50,00">Finalizar compra&nbsp;&nbsp;R$&nbsp;50,00</button>
		
		<input type="hidden" id="woocommerce-process-checkout-nonce" name="woocommerce-process-checkout-nonce" value="91370f44a0"><input type="hidden" name="_wp_http_referer" value="/step/elemenia/?wc-ajax=update_order_review&amp;wcf_checkout_id=90">	</div>
</div>

		</div>