ul.wc_payment_methods.payment_methods.methods {
    border-radius: 8px !important;
}

.elementor-364 .elementor-element.elementor-element-2faaddc4 .wcf-embed-checkout-form .woocommerce-checkout #payment ul.payment_methods {
    border: 1px solid #bdbdbd5c !important;
}

span.wcf-field-required-error {
    position: absolute !important;
    opacity: 0 !important;
    pointer-events: none !important;
    height: 0 !important;
    overflow: hidden !important;
    visibility: hidden !important;
    /* Preserva a funcionalidade, mas oculta visualmente */
}

@media only screen and (max-width: 768px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce {
        padding: 0 0px !important;
    }
}

div#mp-doc-div {
    position: absolute !important;
    opacity: 0 !important;
    pointer-events: none !important;
    height: 0 !important;
    overflow: hidden !important;
    visibility: hidden !important;
    /* Preserva a funcionalidade, mas oculta visualmente */
}

.wcf-customer-info-main-wrapper {
    border: 1px solid #73737338 !important;
    background: #fff;
    padding-top: 0px !important; 
    padding-bottom: 28px !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box {
    border: none !important;
}

@media only screen and (min-width: 768px) {
    div#mp-doc-div {
        margin-top: -22px;
        position: absolute !important;
        opacity: 0 !important;
        pointer-events: none !important;
        height: 0 !important;
        visibility: hidden !important;
        /* Preserva a funcionalidade, mas oculta visualmente */
    }
	
    /* Mantemos posição absoluta e zero opacidade em vez de display:none */
    p#mp-security-code-info {
        position: absolute !important;
        opacity: 0 !important;
        pointer-events: none !important;
        height: 0 !important;
        overflow: hidden !important;
        visibility: hidden !important;
        /* Preserva a funcionalidade, mas oculta visualmente */
    }
}

.mp-checkout-custom-container {
    padding: 0px !important; 
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
    padding: 14px 16px 0 16px !important;
}

input#billing_first_name {
    color: black;
}

input#billing_cpf_cnpj {
    color: black;
}

input#billing_cellphone {
    color: black;
}

input#billing_email {
    color: black;
}

@media only screen and (min-width: 768px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
        padding: 0px 24px 0 24px !important;
    }

    .mp-checkout-custom-card-form .mp-checkout-custom-card-row {
        padding-bottom: 14px !important;
    }

    li.payment_method_woo-mercado-pago-custom input[type="radio"]:checked + label {
        border-color: #3BAE7E !important;
        border-style: solid !important;
        background-color: #e8f5e99e !important;
    }
}

table.shop_table.woocommerce-checkout-review-order-table {
    background-color: #f1f9f196 !important;
    border-radius: 6px !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout h3, 
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #ship-to-different-address, 
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #order_review_heading {
    margin: 22px 0 0px !important;
}

@media (max-width: 768px) {
    .accordion-wrapper.active .accordion-header {
        border-bottom-color: transparent !important; 
    }

    .accordion-header {
        padding: 0px !important;
    }
}

/* Estilo adicional para garantir que os inputs do Mercado Pago fiquem visíveis */
.mp-input-table-container,
.mp-input-table-list,
.mp-input-table-bank-interest-container {
    clip-path: none !important;
    clip: auto !important;
    /* Garante que permaneçam visíveis para o JavaScript */
}

 @media (min-width: 728px) {
.mp-pix-template-container {
    padding-bottom: 16px !important;
    padding-top: 0px !important;
}
}

.elementor-364 .elementor-element.elementor-element-2faaddc4 .wcf-embed-checkout-form .woocommerce-checkout #payment ul.payment_methods {
    border: none !important;
}