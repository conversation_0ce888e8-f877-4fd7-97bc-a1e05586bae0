<style>
.phone-input-wrapper {
    position: relative;
    display: flex; /* Mantém flex para alinhar itens se necessário, mas o prefixo é absoluto */
    align-items: center; /* Ajuda a alinhar o input em si se houver outros elementos */
    width: 100%;
}

.flag-prefix {
    position: absolute;
    left: 12px;
    /* AQUI ESTÁ A MUDANÇA PRINCIPAL */
    top: 50%;               /* Posiciona o topo do prefixo no meio vertical do wrapper */
    transform: translateY(-50%); /* Move o prefixo para cima pela metade de sua própria altura */
    /* FIM DA MUDANÇA PRINCIPAL */
    display: flex;
    align-items: center;
    gap: 4px;
    pointer-events: none; /* Para poder clicar "através" do ícone no input */
    z-index: 1; /* Garante que fique acima do fundo do input */
}

.flag-prefix img {
    width: 18px; /* Ajuste o tamanho se necessário */
    height: auto;
    vertical-align: middle; /* <PERSON><PERSON><PERSON>, mas flex align-items é mais robusto */
    display: inline-block; /* Necessário para vertical-align funcionar se não for flex */
}

.flag-prefix span {
    color: #666;
    font-size: 14px; /* Ajuste o tamanho da fonte se necessário */
}

#billing_cellphone {
    width: 100%;
    border-radius: 4px;
    line-height: 1.5; /* Linha padrão */
    box-sizing: border-box; /* Importante para padding/border não aumentarem o tamanho total */
    padding-left: 65px !important; /* Espaço para a bandeira e +55. Mantenha o !important se for necessário para sobrescrever outros estilos */
    /* padding-top: 22.2px; */ /* Remova ou ajuste este padding-top se ele for o causador do movimento do label e desalinhamento. Veja a nota abaixo. */
    padding-top: 10px; /* Valor de exemplo - ajuste conforme necessário */
    padding-bottom: 10px; /* Adicione padding-bottom para equilíbrio */
    height: 52px; /* Mantenha a altura desejada */
    vertical-align: middle; /* Tenta alinhar o texto verticalmente */
}


/* Estilo opcional para quando o campo está focado */
#billing_cellphone:focus {
    outline: none;
    border-color: #666; /* Ou a cor de foco desejada */
}

/* Estilo opcional para o placeholder */
#billing_cellphone::placeholder {
  color: #999; /* Cor mais clara para o placeholder */
  /* O navegador tentará centralizar verticalmente o placeholder por padrão */
}

</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const phoneInput = document.getElementById('billing_cellphone');
    if (!phoneInput) return;

    // Wrap phone input in a container
    const wrapper = document.createElement('div');
    wrapper.className = 'phone-input-wrapper';
    phoneInput.parentNode.insertBefore(wrapper, phoneInput);
    wrapper.appendChild(phoneInput);

    // Add flag image and prefix
    const flagPrefix = document.createElement('div');
    flagPrefix.className = 'flag-prefix';
    flagPrefix.innerHTML = `
        <img src="https://upload.wikimedia.org/wikipedia/en/thumb/0/05/Flag_of_Brazil.svg/1024px-Flag_of_Brazil.svg.png">
        <span>+55</span>
    `;
    wrapper.insertBefore(flagPrefix, phoneInput);

    // Phone number formatting
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 11) value = value.slice(0, 11);
        
        if (value.length > 0) {
            value = '(' + value;
            if (value.length > 3) {
                value = value.slice(0, 3) + ') ' + value.slice(3);
            }
            if (value.length > 10) {
                value = value.slice(0, 10) + '-' + value.slice(10);
            }
        }
        
        e.target.value = value;
    });
});
</script>